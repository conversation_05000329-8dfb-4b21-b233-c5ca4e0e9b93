<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>AI Oil Plus</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>oil_change_tracker</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>GADApplicationIdentifier</key>
	<string>ca-app-pub-2630068952962472~5833084059</string>
	<key>FIREBASE_ANALYTICS_COLLECTION_ENABLED</key>
	<true/>
	<key>FIREBASE_CRASHLYTICS_COLLECTION_ENABLED</key>
	<true/>
	<key>FirebaseAutomaticScreenReportingEnabled</key>
	<false/>
	<key>aps-environment</key>
	<string>development</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>googleapis.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<true/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.0</string>
			</dict>
		</dict>
	</dict>
	<key>NSCameraUsageDescription</key>
	<string>This app needs access to camera to capture photos of your car for maintenance records.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs access to photo library to select and save car photos for maintenance records.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>This app needs permission to save the photos you capture for maintenance receipts.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs access to microphone for voice input to add maintenance records quickly.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs location access to provide weather information for maintenance scheduling.</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.maximummdeia.oilChangeTracker</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.952433993522-isb4pl1ho1kao932l6pvrl50dcuoltc3</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
