import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../generated/app_localizations.dart';
import '../../features/main_navigation/presentation/screens/main_navigation_screen.dart';

/// A wrapper that adds bottom navigation to any screen
class BottomNavWrapper extends ConsumerWidget {
  final Widget child;
  final String? title;
  final bool showAppBar;

  const BottomNavWrapper({
    super.key,
    required this.child,
    this.title,
    this.showAppBar = false, // Default to false since most screens have their own app bar
  });

  void _onItemTapped(BuildContext context, WidgetRef ref, int index) {
    final current = ref.read(mainNavIndexProvider);
    if (current != index) {
      ref.read(mainNavIndexProvider.notifier).state = index;
      
      // Navigate to main navigation and set the correct tab
      context.go('/main');
      
      // Use post frame callback to ensure the navigation completes first
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(mainNavIndexProvider.notifier).state = index;
      });
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    final currentIndex = ref.watch(mainNavIndexProvider);

    return Scaffold(
      appBar: showAppBar && title != null ? AppBar(
        title: Text(title!),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
      ) : null,
      body: child,
      bottomNavigationBar: Container(
        height: 65,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
          ),
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: currentIndex,
          onTap: (index) => _onItemTapped(context, ref, index),
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: colorScheme.primary,
          unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
          selectedFontSize: 12,
          unselectedFontSize: 12,
          items: [
            BottomNavigationBarItem(
              icon: const Icon(Icons.dashboard_outlined),
              activeIcon: const Icon(Icons.dashboard),
              label: l10n.dashboard,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.directions_car_outlined),
              activeIcon: const Icon(Icons.directions_car),
              label: l10n.cars,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.smart_toy_outlined),
              activeIcon: const Icon(Icons.smart_toy),
              label: l10n.ai,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.settings_outlined),
              activeIcon: const Icon(Icons.settings),
              label: l10n.settings,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.person_outline),
              activeIcon: const Icon(Icons.person),
              label: l10n.profile,
            ),
          ],
        ),
      ),
    );
  }
}
