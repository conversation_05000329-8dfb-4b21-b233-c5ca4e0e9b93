import 'package:flutter/material.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart';
import '../../models/voice_command_result.dart';

/// A widget that displays a preview of the voice command result
class VoiceResultPreview extends StatelessWidget {
  /// The voice command result to display
  final VoiceCommandResult result;

  /// Creates a voice result preview widget
  const VoiceResultPreview({super.key, required this.result});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor.withOpacity(0.7),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).dividerColor,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCommandTypeHeader(context),
          const SizedBox(height: 8),
          _buildCommandDetails(context),
        ],
      ),
    );
  }

  Widget _buildCommandTypeHeader(BuildContext context) {
    final l10n = S.of(context);
    IconData icon;
    String title;

    switch (result.commandType) {
      case VoiceCommandType.addOilChange:
        icon = Icons.oil_barrel;
        title = l10n.addOilChange;
        break;
      case VoiceCommandType.addMaintenance:
        icon = Icons.build;
        title = l10n.addMaintenance;
        break;
      case VoiceCommandType.viewCarDetails:
        icon = Icons.directions_car;
        title = l10n.viewCar;
        break;
      case VoiceCommandType.unknown:
      default:
        icon = Icons.help_outline;
        title = l10n.unknownCommand;
        break;
    }

    return Row(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).textTheme.bodyLarge?.color,
          ),
        ),
      ],
    );
  }

  Widget _buildCommandDetails(BuildContext context) {
    final parameters = result.parameters;
    if (parameters.isEmpty) {
      return Text(S.of(context).noParametersDetected);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: parameters.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Text(
                "${_formatParameterName(entry.key)}: ",
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).textTheme.bodySmall?.color,
                ),
              ),
              Expanded(
                child: Text(
                  _formatParameterValue(context, entry.value),
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  String _formatParameterName(String name) {
    return name
        .replaceAll(RegExp(r'([A-Z])'), ' \$1')
        .trim()
        .split(' ')
        .map((word) =>
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : '')
        .join(' ');
  }

  String _formatParameterValue(BuildContext context, dynamic value) {
    final l10n = S.of(context);
    if (value == null) return l10n.notSpecified;
    if (value is DateTime) {
      return "${value.day}/${value.month}/${value.year}";
    }
    return value.toString();
  }
}
