import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../models/subscription_tier.dart';
import '../../providers/subscription_provider.dart';
import '../../services/subscription_service.dart';
import '../../../../core/providers/auth_providers.dart' as core_auth;
import 'dart:developer' as dev;

/// Welcome screen shown after signup to present subscription options
class WelcomeSubscriptionScreen extends ConsumerStatefulWidget {
  const WelcomeSubscriptionScreen({super.key});

  @override
  ConsumerState<WelcomeSubscriptionScreen> createState() =>
      _WelcomeSubscriptionScreenState();
}

class _WelcomeSubscriptionScreenState
    extends ConsumerState<WelcomeSubscriptionScreen> {
  bool _isLoading = false;
  bool _priceLoading = true;
  String _monthlyPrice = '...';
  String _yearlyPrice = '...';
  bool _isYearlySelected = true; // Default to yearly plan (most popular)

  @override
  void initState() {
    super.initState();
    _fetchPrices();
  }

  Future<void> _fetchPrices() async {
    try {
      final service = ref.read(subscriptionServiceProvider);
      await service.refreshProducts();
      final monthly =
          await service.getFormattedPrice(SubscriptionTier.premium, true);
      final yearly =
          await service.getFormattedPrice(SubscriptionTier.premium, false);

      if (mounted) {
        setState(() {
          _monthlyPrice = monthly;
          _yearlyPrice = yearly;
          _priceLoading = false;
        });
      }
    } catch (e) {
      dev.log('Error fetching prices in welcome screen: $e');
      if (mounted) {
        setState(() {
          _monthlyPrice = '2.99 EGP';
          _yearlyPrice = '24.99 EGP';
          _priceLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 20),

              // Welcome header
              _buildWelcomeHeader(context, s),

              const SizedBox(height: 32),

              // Premium features section
              _buildPremiumFeatures(context, s),

              const SizedBox(height: 32),

              // Trial offer card
              _buildTrialOfferCard(context, s),

              const SizedBox(height: 24),

              // Subscription options
              _buildSubscriptionOptions(context, s),

              const SizedBox(height: 32),

              // Action buttons
              _buildActionButtons(context, s),

              const SizedBox(height: 16),

              // Skip option
              _buildSkipOption(context, s),

              const SizedBox(height: 16),

              // Restore purchases button
              _buildRestorePurchasesButton(context, s),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeHeader(BuildContext context, S s) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: context.accentColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.star,
            size: 48,
            color: context.accentColor,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          'Welcome to Premium!',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          'Unlock powerful features to get the most out of Oil Change Tracker',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: context.secondaryTextColor,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildPremiumFeatures(BuildContext context, S s) {
    final features = [
      {
        'icon': Icons.cloud_upload,
        'title': 'Cloud Backup',
        'description': 'Never lose your data with automatic cloud sync',
      },
      {
        'icon': Icons.mic,
        'title': 'Voice Commands',
        'description': 'Add records quickly using voice input',
      },
      {
        'icon': Icons.smart_toy,
        'title': 'AI Assistant',
        'description': 'Get intelligent maintenance recommendations',
      },
      {
        'icon': Icons.block,
        'title': 'Ad-Free Experience',
        'description': 'Enjoy the app without any interruptions',
      },
      {
        'icon': Icons.analytics,
        'title': 'Advanced Analytics',
        'description': 'Detailed insights about your vehicle maintenance',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Premium Features',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.primaryTextColor,
              ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) => _buildFeatureItem(
              context,
              feature['icon'] as IconData,
              feature['title'] as String,
              feature['description'] as String,
            )),
      ],
    );
  }

  Widget _buildFeatureItem(
      BuildContext context, IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: context.accentColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: context.primaryTextColor,
                      ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: context.secondaryTextColor,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrialOfferCard(BuildContext context, S s) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.accentColor.withOpacity(0.1),
            context.accentColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.accentColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.card_giftcard,
                color: context.accentColor,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                'Free 7-Day Trial',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: context.accentColor,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Try all premium features for 7 days completely free. No charges during trial period. After trial ends, you can choose to subscribe if you want to continue.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.primaryTextColor,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionOptions(BuildContext context, S s) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Choose Your Plan',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
            ),
            const Spacer(),
            if (!_priceLoading)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _isYearlySelected ? 'Annual Selected' : 'Monthly Selected',
                  style: TextStyle(
                    color: context.accentColor,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),

        // Monthly plan
        GestureDetector(
          onTap: _isLoading ? null : () => setState(() => _isYearlySelected = false),
          child: _buildPlanCard(
            context,
            title: 'Monthly Premium',
            price: _priceLoading ? '...' : _monthlyPrice,
            period: 'per month',
            isPopular: false,
            isSelected: !_isYearlySelected,
            savings: null,
          ),
        ),

        const SizedBox(height: 12),

        // Yearly plan
        GestureDetector(
          onTap: _isLoading ? null : () => setState(() => _isYearlySelected = true),
          child: _buildPlanCard(
            context,
            title: 'Annual Premium',
            price: _priceLoading ? '...' : _yearlyPrice,
            period: 'per year',
            isPopular: true,
            isSelected: _isYearlySelected,
            savings: 'Save 17%',
          ),
        ),
      ],
    );
  }

  Widget _buildPlanCard(
    BuildContext context, {
    required String title,
    required String price,
    required String period,
    required bool isPopular,
    required bool isSelected,
    String? savings,
  }) {
    final isDisabled = _isLoading;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDisabled
            ? context.containerBackgroundColor.withOpacity(0.7)
            : context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDisabled
              ? Colors.grey.withOpacity(0.2)
              : isSelected
                  ? context.accentColor
                  : Colors.grey.withOpacity(0.3),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected && !isDisabled
            ? [
                BoxShadow(
                  color: context.accentColor.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ]
            : null,
      ),
      child: Row(
        children: [
          // Radio button indicator
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: isDisabled
                    ? Colors.grey.withOpacity(0.3)
                    : isSelected
                        ? context.accentColor
                        : Colors.grey.withOpacity(0.5),
                width: 2,
              ),
            ),
            child: isSelected && !isDisabled
                ? Center(
                    child: Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: context.accentColor,
                      ),
                    ),
                  )
                : null,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isDisabled
                                ? context.secondaryTextColor.withOpacity(0.5)
                                : context.primaryTextColor,
                          ),
                    ),
                    if (isPopular) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: context.accentColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'POPULAR',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  '$price / $period',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: isDisabled
                            ? context.secondaryTextColor.withOpacity(0.5)
                            : context.secondaryTextColor,
                      ),
                ),
                if (savings != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    savings,
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, S s) {
    return Column(
      children: [
        // Start trial button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _isLoading ? null : _startTrial,
            style: ElevatedButton.styleFrom(
              backgroundColor: context.accentColor,
              disabledBackgroundColor: Colors.grey.withOpacity(0.3),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    'Start 7-Day Free Trial',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
          ),
        ),

        const SizedBox(height: 12),

        // Subscribe directly button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: _isLoading ? null : _goToSubscription,
            style: OutlinedButton.styleFrom(
              foregroundColor: context.accentColor,
              side: BorderSide(color: context.accentColor),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    _isYearlySelected
                        ? 'Subscribe to Annual Plan'
                        : 'Subscribe to Monthly Plan',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  Widget _buildSkipOption(BuildContext context, S s) {
    return Column(
      children: [
        TextButton(
          onPressed: _isLoading ? null : _continueWithFree,
          child: Text(
            'Continue with Free Version',
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 14,
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'You can always upgrade later in Settings',
          style: TextStyle(
            color: context.secondaryTextColor,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRestorePurchasesButton(BuildContext context, S s) {
    return Center(
      child: OutlinedButton.icon(
        onPressed: _isLoading ? null : _restorePurchases,
        icon: Icon(
          Icons.restore,
          color: context.secondaryAccentColor,
          size: 20,
        ),
        label: Text(
          s.restorePurchases,
          style: TextStyle(
            color: context.secondaryAccentColor,
            fontWeight: FontWeight.w500,
          ),
        ),
        style: OutlinedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          side: BorderSide(color: context.secondaryAccentColor),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  void _startTrial() async {
    setState(() {
      _isLoading = true;
    });

    try {
      dev.log('WelcomeSubscriptionScreen: Starting trial for user');

      // Check current subscription state first
      final currentSubscriptionState = ref.read(subscriptionProvider);
      dev.log('WelcomeSubscriptionScreen: Current subscription state - hasActive: ${currentSubscriptionState.hasActiveSubscription}, subscription: ${currentSubscriptionState.subscription?.toJson()}');

      // If user already has an active subscription, show appropriate message
      if (currentSubscriptionState.hasActiveSubscription) {
        dev.log('WelcomeSubscriptionScreen: User already has active subscription, redirecting to main');

        // Reset new signup flag since user has made their choice
        if (!mounted) return;
        ref.read(core_auth.isNewSignupProvider.notifier).state = false;

        // Mark welcome subscription as shown using the notifier
        if (!mounted) return;
        await ref
            .read(core_auth.welcomeSubscriptionShownProvider.notifier)
            .markAsShown();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('You already have an active subscription! Redirecting to main screen.'),
              backgroundColor: Colors.blue,
              duration: Duration(seconds: 2),
            ),
          );

          // Wait a moment for the user to see the message
          await Future.delayed(const Duration(milliseconds: 1500));
          context.go('/main');
        }
        return;
      }

      final success = await ref
          .read(subscriptionProvider.notifier)
          .startFreeTrial(SubscriptionTier.premium);

      dev.log('WelcomeSubscriptionScreen: Trial start result: $success');

      if (success && mounted) {
        // Reset new signup flag since user has made their choice
        ref.read(core_auth.isNewSignupProvider.notifier).state = false;

        // Mark welcome subscription as shown using the notifier
        if (!mounted) return;
        await ref
            .read(core_auth.welcomeSubscriptionShownProvider.notifier)
            .markAsShown();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Trial started successfully! Enjoy premium features.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Wait a moment for the user to see the success message
        await Future.delayed(const Duration(milliseconds: 1500));

        if (mounted) {
          context.go('/main');
        }
      } else if (mounted) {
        // Get more specific error information
        final subscriptionState = ref.read(subscriptionProvider);
        String errorMessage = 'Failed to start trial. Please try again.';

        if (subscriptionState.error != null) {
          if (subscriptionState.error!.contains('already had a trial') ||
              subscriptionState.error!.contains('Trial blocked')) {
            errorMessage = 'You have already used your free trial. Please choose a subscription plan.';
          } else {
            errorMessage = 'Error: ${subscriptionState.error}';
          }
        }

        dev.log('WelcomeSubscriptionScreen: Trial failed - $errorMessage');

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      dev.log('WelcomeSubscriptionScreen: Exception during trial start: $e');
      if (mounted) {
        String errorMessage = 'An unexpected error occurred. Please try again.';

        // Provide more specific error messages based on the exception
        if (e.toString().contains('already had a trial') ||
            e.toString().contains('Trial blocked')) {
          errorMessage = 'You have already used your free trial. Please choose a subscription plan.';
        } else if (e.toString().contains('network') ||
                   e.toString().contains('connection')) {
          errorMessage = 'Network error. Please check your connection and try again.';
        } else if (e.toString().contains('auth') ||
                   e.toString().contains('user')) {
          errorMessage = 'Authentication error. Please sign in again.';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _goToSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Start the actual subscription purchase flow based on selected plan
      final success = await ref
          .read(subscriptionProvider.notifier)
          .purchase(SubscriptionTier.premium, monthly: !_isYearlySelected);

      if (success && mounted) {
        // Reset new signup flag since user has made their choice
        ref.read(core_auth.isNewSignupProvider.notifier).state = false;

        // Mark welcome subscription as shown using the notifier
        if (!mounted) return;
        await ref
            .read(core_auth.welcomeSubscriptionShownProvider.notifier)
            .markAsShown();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Subscription activated successfully! Welcome to Premium.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // Wait a moment for the user to see the success message
        await Future.delayed(const Duration(milliseconds: 1500));

        dev.log(
            'WelcomeSubscriptionScreen: Subscription successful, navigating to main');

        if (mounted) {
          context.go('/main');
        }
      } else if (mounted) {
        // Check if there's a specific error from the subscription provider
        final subscriptionState = ref.read(subscriptionProvider);
        final errorMessage = subscriptionState.error;

        if (errorMessage != null && errorMessage.isNotEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(errorMessage),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        } else {
          // User likely cancelled the purchase, don't show error
          dev.log('WelcomeSubscriptionScreen: Subscription purchase cancelled or failed silently');
        }
      }
    } catch (e) {
      dev.log('WelcomeSubscriptionScreen: Error in _goToSubscription: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting subscription: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _continueWithFree() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Reset new signup flag since user has made their choice
      if (!mounted) return;
      ref.read(core_auth.isNewSignupProvider.notifier).state = false;

      // Mark welcome subscription as shown using the notifier
      if (!mounted) return;
      await ref
          .read(core_auth.welcomeSubscriptionShownProvider.notifier)
          .markAsShown();

      dev.log(
          'WelcomeSubscriptionScreen: User chose free version, navigating to main');

      if (mounted) {
        // Force a small delay to ensure state updates are processed
        await Future.delayed(const Duration(milliseconds: 100));
        context.go('/main');
      }
    } catch (e) {
      dev.log('WelcomeSubscriptionScreen: Error in _continueWithFree: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _restorePurchases() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result =
          await ref.read(subscriptionProvider.notifier).restorePurchases();

      if (!mounted) return;

      if (result) {
        // If restore was successful, check if user now has active subscription
        final subscriptionState = ref.read(subscriptionProvider);
        if (subscriptionState.hasActiveSubscription) {
          // Reset new signup flag since user has made their choice
          ref.read(core_auth.isNewSignupProvider.notifier).state = false;

          // Mark welcome subscription as shown using the notifier
          await ref
              .read(core_auth.welcomeSubscriptionShownProvider.notifier)
              .markAsShown();

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).purchasesRestored),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
            ),
          );

          // Wait a moment for the user to see the success message
          await Future.delayed(const Duration(milliseconds: 1500));

          if (mounted) {
            context.go('/main');
          }
        } else {
          // Purchases restored but no active subscription
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).purchasesRestored),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      } else {
        // No purchases found
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).noPurchasesFound),
            backgroundColor: Colors.orange,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      dev.log('WelcomeSubscriptionScreen: Error restoring purchases: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).errorOccurred),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
