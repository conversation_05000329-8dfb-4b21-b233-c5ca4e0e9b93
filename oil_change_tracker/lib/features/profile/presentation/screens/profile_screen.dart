import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler_platform_interface/permission_handler_platform_interface.dart';
import 'package:intl/intl.dart';
import 'dart:developer' as dev;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';

import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/unified_app_bar.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/models/user_model.dart';
import '../../../../shared/services/image_cache_service.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../../../auth/providers/auth_provider.dart';
import '../../data/services/image_service.dart';
import '../../providers/license_provider.dart';
import '../../../../features/ads/application/interstitial_ad_manager.dart';
import '../../../../features/subscription/providers/subscription_provider.dart';
import '../widgets/settings_tile.dart';
import '../../../../features/subscription/presentation/screens/cloud_backup_screen.dart';
import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../ai_usage/providers/ai_usage_providers.dart';
import '../../../subscription/models/subscription_tier.dart';
import '../../../../core/services/analytics_service.dart';

// Expose openAppSettings method
Future<bool> openAppSettings() {
  return PermissionHandlerPlatform.instance.openAppSettings();
}

// Helper class for time remaining calculation
class TimeRemaining {
  final int years;
  final int months;
  final int weeks;
  final int days;
  final bool isPast;

  TimeRemaining({
    this.years = 0,
    this.months = 0,
    this.weeks = 0,
    this.days = 0,
    this.isPast = false,
  });

  static TimeRemaining calculate(DateTime futureDate) {
    final now = DateTime.now();
    if (futureDate.isBefore(now)) {
      final difference = now.difference(futureDate);
      return TimeRemaining(
        days: difference.inDays,
        isPast: true,
      );
    }

    DateTime tempDate = DateTime(now.year, now.month, now.day);
    int years = 0;
    int months = 0;
    int days = futureDate.difference(tempDate).inDays;

    if (days >= 365) {
      years = days ~/ 365;
      days %= 365;
    }

    if (days >= 30) {
      months = days ~/ 30; // Approximate months
      days %= 30;
    }

    int weeks = 0;
    if (days >= 7) {
      weeks = days ~/ 7;
      days %= 7;
    }

    return TimeRemaining(
        years: years, months: months, weeks: weeks, days: days);
  }

  String toDetailedString(S l10n) {
    if (isPast) {
      if (days == 0) return l10n.expiredToday;
      if (days == 1) return l10n.expiredYesterday;
      return l10n.expiredDaysAgo(days);
    }
    if (years == 0 && months == 0 && weeks == 0 && days == 0)
      return l10n.expiresToday;

    List<String> parts = [];
    if (years > 0) parts.add('$years ${years == 1 ? l10n.year : l10n.years}');
    if (months > 0)
      parts.add('$months ${months == 1 ? l10n.month : l10n.months}');
    if (weeks > 0) parts.add('$weeks ${weeks == 1 ? l10n.week : l10n.weeks}');
    if (days > 0) parts.add('$days ${days == 1 ? l10n.day : l10n.days}');

    if (parts.isEmpty)
      return l10n.expiresToday; // Should not happen if not past and not 0,0,0,0

    return l10n.expiresIn(parts.join(', '));
  }

  String toSimpleString(S l10n) {
    final totalDays = years * 365 + months * 30 + weeks * 7 + days;
    if (isPast) {
      if (this.days == 0) return l10n.expiredToday;
      if (this.days == 1) return l10n.expiredYesterday;
      return l10n.expiredDaysAgo(this.days);
    }
    if (totalDays == 0) return l10n.expiresToday;
    if (totalDays == 1) return l10n.expiresTomorrow;
    return l10n.expiresInDays(totalDays);
  }
}

// Stream provider emitting DateTime every minute to drive UI updates
final nowProvider = StreamProvider<DateTime>((ref) async* {
  yield DateTime.now();
  yield* Stream.periodic(const Duration(minutes: 1), (_) => DateTime.now());
});

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

// Global key for the license section to enable scrolling to it
final GlobalKey licenseSectionKey = GlobalKey();

// Provider to hold the profile screen state for external access
final profileScreenStateProvider =
    StateProvider<_ProfileScreenState?>((ref) => null);

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  late TextEditingController _displayNameController;
  late FocusNode _displayNameFocusNode;
  String? _originalPhotoUrl;
  bool _isUpdatingProfile = false;
  bool _isUpdatingImage = false;
  late final ImageService _imageService;
  late AnimationController _animationController;

  bool _removePhotoSelected = false;

  @override
  void initState() {
    super.initState();
    _imageService = ref.read(imageServiceProvider);

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    )..forward();

    _displayNameController = TextEditingController();
    _displayNameFocusNode = FocusNode();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final user = ref.read(authProvider).asData?.value;
      if (user != null) {
        _displayNameController.text = user.displayName;
        _originalPhotoUrl = user.photoUrl;
      }
      // Set the profile screen state in the provider for external access
      ref.read(profileScreenStateProvider.notifier).state = this;
    });
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _displayNameFocusNode.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    // Clear the profile screen state from the provider
    ref.read(profileScreenStateProvider.notifier).state = null;
    super.dispose();
  }

  /// Scrolls to the license section
  void scrollToLicenseSection() {
    if (licenseSectionKey.currentContext != null) {
      Scrollable.ensureVisible(
        licenseSectionKey.currentContext!,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;
    if (!mounted) return;
    setState(() => _isUpdatingProfile = true);

    try {
      await ref.read(authProvider.notifier).updateDisplayName(
            _displayNameController.text.trim(),
          );

      if (!mounted) return;

      // Show an interstitial ad after updating profile (with 50% probability)
      if (mounted) {
        ref
            .read(interstitialAdManagerProvider.notifier)
            .showAdAfterAction(context, 'profile_update');
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).profileUpdatedSuccessfully),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(S.of(context).errorOccurred),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    } finally {
      if (mounted) setState(() => _isUpdatingProfile = false);
    }
  }

  Future<ImageSource?> _showImageSourceDialog() async {
    _removePhotoSelected = false;

    return await showModalBottomSheet<ImageSource>(
      context: context,
      backgroundColor: context.containerBackgroundColor,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        final l10n = S.of(context);
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                l10n.changeProfilePhoto,
                style: TextStyle(
                    color: context.accentColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: Icon(Icons.photo_library, color: context.accentColor),
                title: Text(S.of(context).gallery,
                    style: TextStyle(color: context.primaryTextColor)),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
              if (ref.read(authProvider).asData?.value?.photoUrl != null &&
                  (ref.read(authProvider).asData!.value!.photoUrl?.isNotEmpty ??
                      false))
                ListTile(
                  leading:
                      Icon(Icons.delete, color: context.secondaryAccentColor),
                  title: Text(l10n.removePhoto,
                      style: TextStyle(color: context.primaryTextColor)),
                  onTap: () {
                    _removePhotoSelected = true;
                    Navigator.pop(context, null);
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _updateProfileImage(
      {ImageSource imageSource = ImageSource.gallery}) async {
    if (_isUpdatingImage) return; // already running
    _isUpdatingImage = true; // lock immediately
    setState(() {}); // reflect in UI
    try {
      // Request image from gallery or camera based on the provided source
      final ImagePicker picker = ImagePicker();
      final XFile? pickedImage = await picker.pickImage(
        source: imageSource,
        maxWidth: 1024,
        maxHeight: 1024,
        imageQuality: 85,
      );

      if (pickedImage == null) {
        dev.log('No image picked');
        return;
      }

      if (!mounted) return;
      setState(() => _isUpdatingImage = true);
      final userForUpload = ref.read(authProvider).asData?.value;
      if (userForUpload == null || userForUpload.id == null) {
        dev.log('User ID is null, cannot upload profile image');
        if (mounted) setState(() => _isUpdatingImage = false);
        return;
      }

      // Get user ID for upload
      final userId = userForUpload.id!;

      // Convert picked image to File and upload it
      final File imageFile = File(pickedImage.path);
      dev.log('Uploading image: ${imageFile.path}');
      String? imageUrl =
          await _imageService.uploadProfileImage(imageFile, userId);

      if (imageUrl == null) {
        dev.log('Failed to upload image');
        if (mounted) {
          setState(() => _isUpdatingImage = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(S.of(context).errorUpdatingProfileImage),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        return;
      }

      dev.log('Image uploaded successfully. URL: $imageUrl');

      // Clear image cache before updating Auth to ensure old image is gone
      final imageCacheService = ref.read(imageCacheServiceProvider);
      await imageCacheService.forceRefreshProfileImage(
          userId, _originalPhotoUrl);

      // Update Firebase Auth record with new URL
      try {
        await ref.read(authProvider.notifier).updateUserPhotoUrl(imageUrl);
        dev.log('Auth record updated with new photo URL');
      } catch (authError) {
        dev.log('Error updating Auth photo URL: $authError');
        // Continue anyway - we have the uploaded image, and we can still update Firestore directly
      }

      // As a fallback, also update Firestore directly if Auth update failed
      try {
        // Update Firestore directly as fallback
        await FirebaseFirestore.instance
            .collection('users')
            .doc(userId)
            .update({'photoUrl': imageUrl});
        dev.log('Firestore record updated with new photo URL');
      } catch (firestoreError) {
        dev.log('Error updating Firestore photo URL: $firestoreError');
        // Continue anyway - if both Auth and Firestore failed, we'll show an error
      }

      // Clear cache again after updates to ensure new image is loaded
      await imageCacheService.forceRefreshProfileImage(userId, imageUrl);
      dev.log('Auth record updated and cache cleared twice');

      if (mounted) {
        // Update UI to reflect changes
        setState(() {
          _isUpdatingImage = false;
          // Update local state to reflect the new URL
          _originalPhotoUrl = imageUrl;
        });

        // Force refresh the UI
        ref.invalidate(authProvider);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).profileImageUpdated),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      dev.log('Error in _updateProfileImage: $e');
      if (mounted) {
        setState(() => _isUpdatingImage = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).errorOccurred),
            backgroundColor: context.secondaryAccentColor,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final userAsync = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBarFactory.standard(
        title: l10n.profile,
        centerTitle: true,
      ),
      body: userAsync.when(
        data: (user) {
          if (user == null) {
            dev.log('Profile: User is null, showing sign-in prompt');
            return _buildSignInPrompt(context, l10n);
          }

          // Log user information for debugging
          dev.log(
              'Profile: Building for user ${user.id}, email: ${user.email}');
          dev.log('Profile: Provider data: ${user.providerData}');

          // Ensure form values are up to date
          if (_displayNameController.text != user.displayName) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) _displayNameController.text = user.displayName;
            });
          }
          if (_originalPhotoUrl != user.photoUrl) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted) _originalPhotoUrl = user.photoUrl;
            });
          }

          // Determine auth method
          final isGoogleUser = _isGoogleUser(user);
          dev.log(
              'Profile: User ${user.id} determined to be Google user: $isGoogleUser');

          return _buildProfileContent(context, l10n, user, isGoogleUser);
        },
        loading: () => Center(
          child: CircularProgressIndicator(color: context.accentColor),
        ),
        error: (error, stackTrace) =>
            _buildErrorView(context, l10n, error.toString()),
      ),
    );
  }

  bool _isGoogleUser(UserModel user) {
    bool isGoogleProvider = false;
    bool hasPasswordProvider = false;

    dev.log('Profile: Checking auth provider for user ${user.id}');

    // Check provider data array (which contains all authentication methods)
    if (user.providerData != null && user.providerData!.isNotEmpty) {
      // Log providers for debugging
      for (var provider in user.providerData!) {
        final providerId = provider['providerId'];
        dev.log('Profile: Found provider: $providerId');
      }

      // Check for password provider
      hasPasswordProvider = user.providerData!
          .any((provider) => provider['providerId'] == 'password');

      // Check for Google provider
      isGoogleProvider = user.providerData!
          .any((provider) => provider['providerId'] == 'google.com');

      dev.log('Profile: Has password provider: $hasPasswordProvider');
      dev.log('Profile: Is Google provider: $isGoogleProvider');
    }

    // If we have provider data but no Google or password provider was found,
    // check the main provider ID as a fallback
    if (!isGoogleProvider && user.providerId != null) {
      dev.log('Profile: Checking main providerId: ${user.providerId}');
      isGoogleProvider = user.providerId == 'google.com';
    }

    // Log the final decision
    dev.log(
        'Profile: Final determination - isGoogleProvider: $isGoogleProvider, hasPasswordProvider: $hasPasswordProvider');

    // If Google is the provider, consider this a Google user - this takes priority
    if (isGoogleProvider) {
      return true;
    }

    // If email is empty or null, it's likely not an email user
    if (user.email.isEmpty) {
      return false;
    }

    // Check if the email ends with gmail.com as an additional hint
    if (user.email.toLowerCase().endsWith('@gmail.com') &&
        !hasPasswordProvider) {
      dev.log(
          'Profile: User has Gmail address and no password provider, considering as Google user');
      return true;
    }

    // Default case: if not identified as a Google user, assume it's an email user
    return false;
  }

  Widget _buildSignInPrompt(BuildContext context, S l10n) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: context.accentColor.withOpacity(0.1),
            ),
            child: Icon(
              Icons.account_circle,
              size: 80,
              color: context.accentColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            l10n.pleaseSignIn,
            style: TextStyle(
              color: context.primaryTextColor,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            "You need to sign in to access your profile",
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 36),
          PrimaryButton(
            text: l10n.login,
            onPressed: () => context.go('/login'),
            icon: Icons.login,
            fullWidth: true,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    S l10n,
    UserModel user,
    bool isGoogleUser,
  ) {
    dev.log('Profile: Building profile content. isGoogleUser: $isGoogleUser');

    return SingleChildScrollView(
      controller: _scrollController,
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProfileHeader(context, user),
          const SizedBox(height: 16),

          // Main content sections with cards
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Account section with personal details
                _buildProfileInfoSection(context, l10n, user),
                const SizedBox(height: 12),

                // License section for license expiry management
                _buildLicenseSection(context, l10n, user),

                // Subscription section
                const SizedBox(height: 12),
                _buildSubscriptionSection(context, l10n),

                // Security section handling
                const SizedBox(height: 12),
                if (isGoogleUser)
                  // Google account note for Google users
                  _buildGoogleAuthNote(context)
                else
                  // Change password option for email users only
                  _buildSecuritySection(context, l10n),

                const SizedBox(height: 12),

                // Sign out button at the bottom
                _buildSignOutButton(context, l10n),
                const SizedBox(height: 32),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, UserModel user) {
    String? effectivePhotoUrl =
        _removePhotoSelected ? null : (_originalPhotoUrl ?? user.photoUrl);
    // Keep a stable hero tag during the widget life-cycle, update it only
    // when the image itself changes.
    final forceRefreshTimestamp = _originalPhotoUrl.hashCode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 24),
      // Removed background decoration for a clean, simple look
      decoration: null,
      child: Column(
        children: [
          // Profile image with edit button and premium crown
          Stack(
            alignment: Alignment.center,
            children: [
              // Profile image container
              AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                width: 90,
                height: 90,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: context.accentColor.withOpacity(0.2),
                    width: 2,
                  ),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Background/placeholder
                    CircleAvatar(
                      radius: 42,
                      backgroundColor: Colors.white.withOpacity(0.8),
                    ),

                    // Profile image or spinner
                    _isUpdatingImage
                        ? Center(
                            child: SizedBox(
                              height: 28,
                              width: 28,
                              child: CircularProgressIndicator(
                                color: context.accentColor,
                                strokeWidth: 2,
                              ),
                            ),
                          )
                        : Hero(
                            tag:
                                'profile-image-${user.id}-$forceRefreshTimestamp',
                            child: ClipOval(
                              child: ref
                                  .watch(imageCacheServiceProvider)
                                  .buildProfileImage(
                                    userId: user.id ?? '',
                                    imageUrl: effectivePhotoUrl,
                                    size: 90,
                                    backgroundColor:
                                        Colors.white.withOpacity(0.8),
                                    foregroundColor: context.accentColor,
                                    // Always force refresh when user removes photo or changes photo
                                    forceRefresh: true,
                                  ),
                            ),
                          ),
                  ],
                ),
              ),

              // Premium crown for premium subscribers
              Consumer(
                builder: (context, ref, child) {
                  final subscriptionState = ref.watch(subscriptionProvider);
                  if (subscriptionState.hasActiveSubscription) {
                    return Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: AppColors.gold.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          Icons.workspace_premium,
                          color: AppColors.gold,
                          size: 20,
                        ),
                      ),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),

              // Edit button
              Positioned(
                bottom: 0,
                right: 0,
                child: GestureDetector(
                  onTap: _isUpdatingImage
                      ? null
                      : () async {
                          final source = await _showImageSourceDialog();
                          if (_removePhotoSelected) {
                            setState(() => _isUpdatingImage = true);

                            try {
                              final userId =
                                  ref.read(authProvider).asData?.value?.id;
                              if (userId != null) {
                                // Clear image cache before updating the photo URL
                                final imageCacheService =
                                    ref.read(imageCacheServiceProvider);
                                await imageCacheService
                                    .forceRefreshProfileImage(
                                        userId, _originalPhotoUrl);

                                // Update Firebase Auth and Firestore
                                await ref
                                    .read(authProvider.notifier)
                                    .updateUserPhotoUrl('');

                                // Also update Firestore directly to ensure consistency
                                try {
                                  await FirebaseFirestore.instance
                                      .collection('users')
                                      .doc(userId)
                                      .update({'photoUrl': ''});
                                  dev.log(
                                      'Firestore record updated - photo removed');
                                } catch (firestoreError) {
                                  dev.log(
                                      'Error updating Firestore for photo removal: $firestoreError');
                                  // Continue anyway - if Auth worked but Firestore failed, we'll show a warning
                                }

                                // Clear cache again after updates
                                await imageCacheService
                                    .forceRefreshProfileImage(userId, '');

                                setState(() {
                                  _removePhotoSelected = false;
                                  _originalPhotoUrl = '';
                                  _isUpdatingImage = false;
                                });

                                // Force refresh the UI and auth state
                                ref.invalidate(authProvider);

                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content:
                                        Text(S.of(context).profileImageUpdated),
                                    backgroundColor: Colors.green,
                                    behavior: SnackBarBehavior.floating,
                                    duration: const Duration(seconds: 2),
                                  ),
                                );
                              }
                            } catch (e) {
                              dev.log('Error removing profile photo: $e');
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text(S.of(context).errorOccurred),
                                  backgroundColor: Colors.red,
                                  behavior: SnackBarBehavior.floating,
                                ),
                              );
                            } finally {
                              if (mounted) {
                                setState(() => _isUpdatingImage = false);
                              }
                            }
                          } else if (source != null) {
                            await _updateProfileImage(imageSource: source);
                          }
                        },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 36,
                    height: 36,
                    decoration: BoxDecoration(
                      color: _isUpdatingImage
                          ? Colors.grey.shade300
                          : context.accentColor,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // User name with animation
          SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.5),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: _animationController,
              curve: const Interval(0.2, 0.6, curve: Curves.easeOutQuad),
            )),
            child: FadeTransition(
              opacity: _animationController,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    user.displayName,
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      // Focus on the display name field
                      final targetCtx = _formKey.currentContext;
                      if (targetCtx != null) {
                        Scrollable.ensureVisible(
                          targetCtx,
                          duration: const Duration(milliseconds: 500),
                          alignment: 0.2,
                        );
                      }
                      _displayNameFocusNode.requestFocus();
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: context.accentColor.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.edit,
                        size: 18,
                        color: context.accentColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 8),

          // Email with animation
          SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.5),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: _animationController,
              curve: const Interval(0.3, 0.7, curve: Curves.easeOutQuad),
            )),
            child: FadeTransition(
              opacity: Tween<double>(begin: 0, end: 1).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: const Interval(0.3, 0.7),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.email_outlined,
                    size: 18,
                    color: context.secondaryTextColor,
                  ),
                  const SizedBox(width: 8),
                  Flexible(
                    child: Text(
                      user.email,
                      style: TextStyle(
                        fontSize: 16,
                        color: context.secondaryTextColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // AI Usage Progress Bar (compact version)
          const SizedBox(height: 12),
          _buildCompactAIUsageProgressBar(context),

          // Countdown under AI bar for trial users
          _buildTrialCountdown(context),

          // Email verification status
          if (!user.isEmailVerified &&
              !((user.providerId == 'google.com') ||
                  (user.providerData
                          ?.any((p) => p['providerId'] == 'google.com') ??
                      false)))
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Builder(builder: (context) {
                // Check if user signed in with Google
                final isGoogleSignIn = user.providerData?.any(
                        (provider) => provider['providerId'] == 'google.com') ??
                    false;

                // Don't show verification button for Google users
                if (isGoogleSignIn) {
                  return const SizedBox.shrink();
                }

                final l10n = S.of(context);

                return InkWell(
                  onTap: () async {
                    try {
                      await ref
                          .read(authProvider.notifier)
                          .sendEmailVerification();
                      if (!mounted) return;

                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(S.of(context).verificationEmailSent),
                          backgroundColor: Colors.green,
                          behavior: SnackBarBehavior.floating,
                          duration: const Duration(seconds: 4),
                          margin: const EdgeInsets.all(16),
                        ),
                      );
                    } catch (e) {
                      if (!mounted) return;
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content:
                              Text(S.of(context).errorSendingVerificationEmail),
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                          duration: const Duration(seconds: 4),
                          margin: const EdgeInsets.all(16),
                        ),
                      );
                    }
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.amber.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.amber, width: 1),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.warning_amber_rounded,
                          size: 16,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          l10n.verifyEmail,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileInfoSection(
      BuildContext context, S l10n, UserModel user) {
    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.person_outline,
                    color: context.accentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.profile,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // Display name form
            Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    l10n.displayName,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextFormField(
                    controller: _displayNameController,
                    style: TextStyle(color: context.primaryTextColor),
                    decoration: InputDecoration(
                      hintText: l10n.displayName,
                      hintStyle: TextStyle(
                          color: context.secondaryTextColor.withOpacity(0.6)),
                      prefixIcon: Icon(Icons.badge_outlined,
                          color: context.accentColor),
                      filled: true,
                      fillColor: context.isDarkMode
                          ? Colors.black.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide: BorderSide.none,
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(16),
                        borderSide:
                            BorderSide(color: context.accentColor, width: 2),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                          vertical: 16, horizontal: 16),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return l10n.displayNameRequired;
                      }
                      return null;
                    },
                    focusNode: _displayNameFocusNode,
                  ),

                  const SizedBox(height: 20),

                  // Email information
                  Text(
                    l10n.email,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: context.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 10),
                  Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    decoration: BoxDecoration(
                      color: context.isDarkMode
                          ? Colors.black.withOpacity(0.1)
                          : Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.transparent,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.email_outlined,
                          size: 20,
                          color: context.accentColor,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            user.email,
                            style: TextStyle(
                              fontSize: 16,
                              color: context.primaryTextColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ),
                        if (user.isEmailVerified)
                          Icon(
                            Icons.verified,
                            size: 20,
                            color: Colors.green,
                          )
                        else
                          Icon(
                            Icons.warning_amber_rounded,
                            size: 20,
                            color: Colors.amber,
                          ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isUpdatingProfile ? null : _updateProfile,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 2,
                      ),
                      child: _isUpdatingProfile
                          ? SizedBox(
                              height: 24,
                              width: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(Icons.save, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  l10n.saveChanges,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLicenseSection(BuildContext context, S l10n, UserModel user) {
    return Container(
      key: licenseSectionKey,
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.badge_outlined,
                    color: context.accentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.driverLicense,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // License expiry date with indicator
            Consumer(
              builder: (context, ref, child) {
                final expiryDate = ref.watch(licenseExpiryProvider);
                final notificationsEnabled =
                    ref.watch(licenseNotificationEnabledProvider);

                return Column(
                  children: [
                    // License expiry date
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: context.isDarkMode
                            ? Colors.black.withOpacity(0.15)
                            : Colors.grey.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // License expiry header and value
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: context.accentColor.withOpacity(0.2),
                                ),
                                child: Icon(
                                  Icons.calendar_today,
                                  size: 24,
                                  color: context.accentColor,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      l10n.licenseExpiryDate,
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                        color: context.primaryTextColor,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.visible,
                                    ),
                                    if (expiryDate != null) ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        DateFormat.yMMMMd(
                                                Localizations.localeOf(context)
                                                    .languageCode)
                                            .format(expiryDate),
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: _getLicenseExpiryColor(
                                              expiryDate, context),
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        TimeRemaining.calculate(expiryDate)
                                            .toDetailedString(l10n),
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: _getLicenseExpiryColor(
                                              expiryDate, context),
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ] else ...[
                                      const SizedBox(height: 8),
                                      Text(
                                        l10n.notSet,
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontStyle: FontStyle.italic,
                                          color: context.secondaryTextColor,
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 20),

                    // Update license button
                    ElevatedButton.icon(
                      onPressed: () =>
                          _showLicenseExpiryDialog(context, ref, expiryDate),
                      icon: const Icon(Icons.edit_calendar),
                      label: Text(expiryDate == null ? l10n.add : l10n.update),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: context.accentColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            vertical: 16, horizontal: 20),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Color _getLicenseExpiryColor(DateTime expiryDate, BuildContext context) {
    final timeRemaining = TimeRemaining.calculate(expiryDate);
    if (timeRemaining.isPast) {
      return Colors.red;
    }
    final totalDays = timeRemaining.years * 365 +
        timeRemaining.months * 30 +
        timeRemaining.weeks * 7 +
        timeRemaining.days;
    if (totalDays < 30) {
      return Colors.orange;
    }
    if (totalDays < 90) {
      return Colors.amber;
    }
    return context.accentColor;
  }

  Widget _buildSecuritySection(BuildContext context, S l10n) {
    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.security,
                    color: context.accentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.security,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Change Password option
            _buildSecurityOptionTile(
              context: context,
              icon: Icons.lock_outline,
              title: l10n.changePassword,
              subtitle: l10n.changePassword,
              onTap: () => context.push('/profile/change-password'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityOptionTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback? onTap,
    bool isDisabled = false,
  }) {
    return InkWell(
      onTap: isDisabled ? null : onTap,
      borderRadius: BorderRadius.circular(16),
      child: Opacity(
        opacity: isDisabled ? 0.5 : 1.0,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: context.isDarkMode
                ? Colors.black.withOpacity(0.15)
                : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.transparent,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: context.accentColor.withOpacity(0.15),
                ),
                child: Icon(
                  icon,
                  size: 22,
                  color: context.accentColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: context.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: context.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: context.secondaryTextColor,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSignOutButton(BuildContext context, S l10n) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ElevatedButton.icon(
        onPressed: () async {
          final confirmed = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: Text(l10n.signOut),
              content: Text(l10n.signOutConfirmation),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: Text(l10n.cancel),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context, true),
                  child: Text(l10n.yes),
                ),
              ],
            ),
          );

          if (confirmed == true && mounted) {
            await ref.read(authProvider.notifier).signOut();
            if (!mounted) return;
            context.go('/dashboard');
          }
        },
        icon: const Icon(Icons.exit_to_app),
        label: Text(l10n.signOut),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.redAccent.shade200,
          foregroundColor: Colors.white,
          minimumSize: const Size(double.infinity, 56),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, S l10n, String errorMessage) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.red.withOpacity(0.1),
            ),
            child: const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            l10n.error,
            style: TextStyle(
              color: context.primaryTextColor,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            errorMessage,
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          PrimaryButton(
            text: l10n.retry,
            onPressed: () => ref.refresh(authProvider),
            icon: Icons.refresh,
            fullWidth: true,
          ),
        ],
      ),
    );
  }

  // Add license expiry dialog method
  void _showLicenseExpiryDialog(
      BuildContext context, WidgetRef ref, DateTime? currentDate) {
    // Creating a local variable to avoid widget disposal issues
    DateTime selectedDate =
        currentDate ?? DateTime.now().add(const Duration(days: 365));
    bool notificationsEnabled = ref.read(licenseNotificationEnabledProvider);

    showDialog(
      context: context,
      builder: (dialogContext) => StatefulBuilder(
        builder: (dialogContext, setState) => AlertDialog(
          backgroundColor: dialogContext.containerBackgroundColor,
          title: Text(
            S.of(context).driverLicenseExpiryDate,
            style: TextStyle(
              color: dialogContext.accentColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).selectExpiryDate,
                style: TextStyle(color: dialogContext.primaryTextColor),
              ),
              const SizedBox(height: 12),
              InkWell(
                onTap: () async {
                  final picked = await showDatePicker(
                    context: dialogContext,
                    initialDate: selectedDate,
                    firstDate: DateTime.now()
                        .subtract(const Duration(days: 1)), // Allow today
                    // Allow up to 10 years for long-term licenses (common in some regions)
                    lastDate:
                        DateTime.now().add(const Duration(days: 365 * 10)),
                    builder: (context, child) => Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.dark(
                          primary: dialogContext.accentColor,
                          onPrimary: Colors.white,
                          surface: dialogContext.containerBackgroundColor,
                          onSurface: dialogContext.primaryTextColor,
                        ),
                      ),
                      child: child!,
                    ),
                  );

                  if (picked != null) {
                    setState(() {
                      selectedDate = picked;
                    });
                  }
                },
                child: Container(
                  width: double.infinity,
                  padding:
                      const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: dialogContext.borderColor),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat.yMMMMd().format(selectedDate),
                        style: TextStyle(color: dialogContext.primaryTextColor),
                      ),
                      Icon(Icons.calendar_today,
                          color: dialogContext.accentColor),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Checkbox(
                    value: notificationsEnabled,
                    activeColor: dialogContext.accentColor,
                    onChanged: (value) {
                      setState(() {
                        notificationsEnabled = value ?? false;
                      });
                    },
                  ),
                  Expanded(
                    child: Text(
                      S.of(context).enableExpiryNotifications,
                      style: TextStyle(color: dialogContext.primaryTextColor),
                    ),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).maybePop(),
              child: Text(
                S.of(context).cancel,
                style: TextStyle(color: dialogContext.secondaryTextColor),
              ),
            ),
            if (currentDate != null)
              TextButton(
                onPressed: () async {
                  try {
                    // Use the provider to update the license expiry date to null
                    await ref
                        .read(licenseNotifierProvider.notifier)
                        .updateLicenseExpiryDate(null);

                    // Also update notification setting if removing the date
                    if (notificationsEnabled) {
                      await ref
                          .read(licenseNotifierProvider.notifier)
                          .updateNotificationSetting(false);
                    }

                    Navigator.of(dialogContext).maybePop();

                    if (mounted) {
                      // Show an interstitial ad after updating license (with 50% probability)
                      ref
                          .read(interstitialAdManagerProvider.notifier)
                          .showAdAfterAction(context, 'license_delete');

                      // Force refresh providers to ensure UI updates
                      ref.invalidate(licenseExpiryProvider);
                      ref.invalidate(licenseNotificationEnabledProvider);
                      // Force refresh the auth provider too
                      ref.refresh(authProvider);

                      // Hide any existing SnackBar before showing a new one
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(S.of(context).success),
                          backgroundColor: Colors.green,
                          behavior: SnackBarBehavior.floating,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  } catch (e) {
                    Navigator.of(dialogContext).maybePop();

                    if (mounted) {
                      dev.log('Error removing license date: $e');
                      ScaffoldMessenger.of(context).hideCurrentSnackBar();
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(S.of(context).errorOccurred),
                          backgroundColor: Colors.red,
                          behavior: SnackBarBehavior.floating,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
                child: Text(
                  S.of(context).delete,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            ElevatedButton(
              onPressed: () async {
                try {
                  dev.log(
                      'Updating license expiry date to: ${selectedDate.toIso8601String()}');

                  // Update the license expiry date
                  await ref
                      .read(licenseNotifierProvider.notifier)
                      .updateLicenseExpiryDate(selectedDate);

                  // Update notification setting
                  await ref
                      .read(licenseNotifierProvider.notifier)
                      .updateNotificationSetting(notificationsEnabled);

                  Navigator.of(dialogContext).maybePop();

                  if (mounted) {
                    // Show an interstitial ad after updating license (with 50% probability)
                    ref
                        .read(interstitialAdManagerProvider.notifier)
                        .showAdAfterAction(context, 'license_update');

                    // Force refresh providers to ensure UI updates
                    ref.invalidate(licenseExpiryProvider);
                    ref.invalidate(licenseNotificationEnabledProvider);
                    // Force refresh the auth provider too
                    ref.refresh(authProvider);

                    // Hide any existing SnackBar before showing a new one
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).success),
                        backgroundColor: Colors.green,
                        behavior: SnackBarBehavior.floating,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                } catch (e) {
                  Navigator.of(dialogContext).maybePop();

                  if (mounted) {
                    dev.log('Error updating license date: $e');
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(S.of(context).errorOccurred),
                        backgroundColor: Colors.red,
                        behavior: SnackBarBehavior.floating,
                        duration: const Duration(seconds: 2),
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: dialogContext.accentColor,
                elevation: 2,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(S.of(context).save),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGoogleAuthNote(BuildContext context) {
    final l10n = S.of(context);
    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header with Google icon
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Image.asset(
                    'assets/images/google_logo.png',
                    width: 20,
                    height: 20,
                    errorBuilder: (context, error, stackTrace) => const Icon(
                      Icons.account_circle,
                      color: Colors.blue,
                      size: 20,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Google Account',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Google authentication info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.isDarkMode
                    ? Colors.black.withOpacity(0.15)
                    : Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.transparent,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.blue.withOpacity(0.15),
                    ),
                    child: const Icon(
                      Icons.info_outline,
                      size: 22,
                      color: Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Password Management',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Your account uses Google authentication. To change your password or manage account security, please visit your Google account settings.',
                          style: TextStyle(
                            fontSize: 14,
                            color: context.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Google account management link
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: TextButton.icon(
                onPressed: () {
                  // Display instructions for managing Google account
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Manage Your Google Account'),
                      content: const Text('To manage your Google account:\n\n'
                          '1. Open the Google app or visit google.com\n'
                          '2. Tap your profile picture in the top right\n'
                          '3. Tap "Manage your Google Account"\n'
                          '4. Go to "Security" to manage your password and account security'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: Text(S.of(context).close),
                        ),
                      ],
                    ),
                  );
                },
                icon: const Icon(Icons.open_in_new, color: Colors.blue),
                label: const Text(
                  'How to manage your Google account',
                  style: TextStyle(color: Colors.blue),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionSection(BuildContext context, S l10n) {
    final subscriptionState = ref.watch(subscriptionProvider);
    final subscription = subscriptionState.subscription;
    final hasSubscription = subscriptionState.hasActiveSubscription;
    final isTrialPeriod = subscription?.isInTrialPeriod() == true;

    dev.log(
        'Subscription Section - State: hasSubscription=$hasSubscription, isTrial=$isTrialPeriod');

    return Container(
      decoration: BoxDecoration(
        color: context.containerBackgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: context.accentColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Section header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.workspace_premium,
                    color: context.accentColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.subscriptions,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Subscription status
            SettingsTile(
              icon: Icons.diamond_outlined,
              iconColor: isTrialPeriod
                  ? Colors.orange
                  : (hasSubscription ? Colors.amber : Colors.grey),
              title: isTrialPeriod
                  ? 'Premium Trial'
                  : (hasSubscription
                      ? l10n.premiumSubscription
                      : l10n.freeVersion),
              subtitle: isTrialPeriod
                  ? 'Currently in trial period'
                  : (hasSubscription
                      ? l10n.youHaveActivePremium
                      : l10n.upgradeToRemoveAds),
              onTap: () => context.push('/subscription'),
            ),

            // Trial countdown
            if (isTrialPeriod) _buildTrialCountdown(context),

            // Cloud Backup Option
            _buildCloudBackupOption(context, l10n),

            // Manage subscription button
            if (hasSubscription && !isTrialPeriod)
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => context.push('/subscription'),
                    icon: const Icon(Icons.settings),
                    label: Text(l10n.manageSubscription),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              )
            else
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: () => context.push('/subscription'),
                    icon: const Icon(Icons.workspace_premium),
                    label: Text(isTrialPeriod
                        ? 'Upgrade Before Trial Ends'
                        : l10n.upgradeToPremium),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          isTrialPeriod ? Colors.orange : Colors.amber,
                      foregroundColor: Colors.black87,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ),

            // Restore purchases button (matching subscription screen style)
            Padding(
              padding: const EdgeInsets.only(top: 12),
              child: Center(
                child: OutlinedButton.icon(
                  onPressed: () => _restorePurchases(context),
                  icon: Icon(
                    Icons.restore,
                    color: context.secondaryAccentColor,
                    size: 20,
                  ),
                  label: Text(
                    l10n.restorePurchases,
                    style: TextStyle(
                      color: context.secondaryAccentColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 12),
                    side: BorderSide(color: context.secondaryAccentColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Method to restore purchases
  Future<void> _restorePurchases(BuildContext context) async {
    final l10n = S.of(context);

    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Center(
        child: CircularProgressIndicator(
          color: context.accentColor,
        ),
      ),
    );

    try {
      final result =
          await ref.read(subscriptionProvider.notifier).restorePurchases();

      // Pop loading dialog
      if (context.mounted) Navigator.pop(context);

      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            result ? l10n.purchasesRestored : l10n.noPurchasesFound,
          ),
          backgroundColor: result ? Colors.green : Colors.orange,
          behavior: SnackBarBehavior.floating,
          margin: const EdgeInsets.all(16),
        ),
      );
    } catch (e) {
      // Pop loading dialog
      if (context.mounted) Navigator.pop(context);

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.error),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    }
  }

  Widget _buildCloudBackupOption(BuildContext context, S l10n) {
    final hasCloudBackupAccess =
        ref.watch(featureGateProvider(PremiumFeature.cloudBackup));

    return ListTile(
      leading: Icon(
        Icons.cloud_sync,
        color: context.accentColor,
      ),
      title: Text(l10n.cloudBackup),
      subtitle: Text(hasCloudBackupAccess
          ? l10n.manageCloudBackups
          : l10n.cloudBackupPremiumFeature),
      trailing: hasCloudBackupAccess
          ? Icon(Icons.arrow_forward_ios, size: 16, color: context.accentColor)
          : Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: context.secondaryAccentColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                l10n.premium,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => const CloudBackupScreen()),
        );
      },
    );
  }

  Widget _buildCompactAIUsageProgressBar(BuildContext context) {
    return Consumer(builder: (context, ref, _) {
      final usageAsync = ref.watch(aiUsageProvider);
      final analyticsService = ref.watch(analyticsServiceProvider);
      final subscriptionAsync = ref.watch(subscriptionProvider);

      // Only show AI usage bar for users with active subscriptions or trials
      final subscription = subscriptionAsync.subscription;
      final hasActiveSubscription = subscriptionAsync.hasActiveSubscription;
      final isInTrialPeriod = subscription?.isInTrialPeriod() == true;

      // Don't show the bar if user doesn't have active subscription or trial
      if (!hasActiveSubscription && !isInTrialPeriod) {
        return const SizedBox.shrink();
      }

      return usageAsync.when(
        data: (usage) {
          final isTrialOrPremium = subscription?.isInTrialPeriod() == true ||
              subscription?.tier == SubscriptionTier.premium;

          // Use remote config values based on subscription status
          final int voiceCap = isTrialOrPremium
              ? analyticsService.getRemoteConfigValue<int>(
                  'ai_cap_premium_voice', 75)
              : analyticsService.getRemoteConfigValue<int>(
                  'ai_cap_free_voice', 20);
          final int chatCap = isTrialOrPremium
              ? analyticsService.getRemoteConfigValue<int>(
                  'ai_cap_premium_chat', 150)
              : analyticsService.getRemoteConfigValue<int>(
                  'ai_cap_free_chat', 40);

          final int used = usage.voiceCount + usage.chatCount;
          final int cap = voiceCap + chatCap;
          final double pct = cap == 0 ? 0 : used / cap;

          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: context.isDarkMode
                  ? Colors.white.withOpacity(0.15)
                  : context.accentColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: context.isDarkMode
                    ? Colors.white.withOpacity(0.3)
                    : context.accentColor.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.smart_toy_outlined,
                  color:
                      context.isDarkMode ? Colors.white : context.accentColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            'AI Usage',
                            style: TextStyle(
                              color: context.isDarkMode
                                  ? Colors.white
                                  : context.primaryTextColor,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            '${used}/${cap}',
                            style: TextStyle(
                              color: context.isDarkMode
                                  ? Colors.white70
                                  : context.secondaryTextColor,
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      ClipRRect(
                        borderRadius: BorderRadius.circular(2),
                        child: LinearProgressIndicator(
                          value: pct,
                          backgroundColor: context.isDarkMode
                              ? Colors.white.withOpacity(0.2)
                              : context.accentColor.withOpacity(0.2),
                          valueColor: AlwaysStoppedAnimation<Color>(
                            pct > 0.8
                                ? Colors.red.shade400
                                : (context.isDarkMode
                                    ? Colors.white
                                    : context.accentColor),
                          ),
                          minHeight: 3,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: context.isDarkMode
                ? Colors.white.withOpacity(0.15)
                : context.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.white.withOpacity(0.3)
                  : context.accentColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.smart_toy_outlined,
                color: context.isDarkMode ? Colors.white : context.accentColor,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'Loading AI Usage...',
                style: TextStyle(
                  color: context.isDarkMode
                      ? Colors.white70
                      : context.secondaryTextColor,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        error: (error, stackTrace) => const SizedBox.shrink(),
      );
    });
  }

  // Countdown under AI bar for trial users
  Widget _buildTrialCountdown(BuildContext context) {
    return Consumer(builder: (context, ref, _) {
      // Trigger rebuild every minute
      ref.watch(nowProvider);

      final subState = ref.watch(subscriptionProvider);
      final sub = subState.subscription;

      // Debug logging
      dev.log(
          'Trial Countdown - Subscription State: isLoading=${subState.isLoading}, hasActiveSubscription=${subState.hasActiveSubscription}');
      dev.log(
          'Trial Countdown - Subscription: isTrial=${sub?.isInTrialPeriod()}, expiryDate=${sub?.expiryDate}');

      if (sub?.isInTrialPeriod() != true || sub?.expiryDate == null) {
        dev.log(
            'Trial Countdown - Not showing countdown: isTrial=${sub?.isInTrialPeriod()}, expiryDate=${sub?.expiryDate}');
        return const SizedBox.shrink();
      }

      final remaining = sub!.expiryDate!.difference(DateTime.now());
      if (remaining.isNegative) {
        dev.log('Trial Countdown - Trial has expired: ${sub.expiryDate}');
        return const SizedBox.shrink();
      }

      final days = remaining.inDays;
      final hours = remaining.inHours % 24;
      final minutes = remaining.inMinutes % 60;

      String text;
      if (days > 0) {
        text = '$days d $hours h';
      } else if (hours > 0) {
        text = '$hours h $minutes m';
      } else {
        text = '${remaining.inMinutes} m';
      }

      dev.log('Trial Countdown - Showing countdown: $text left');

      return Padding(
        padding: const EdgeInsets.only(top: 6.0, left: 16, right: 16),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: context.isDarkMode
                ? Colors.white.withOpacity(0.15)
                : context.accentColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: context.isDarkMode
                  ? Colors.white.withOpacity(0.3)
                  : context.accentColor.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.hourglass_bottom_rounded,
                size: 16,
                color: context.isDarkMode ? Colors.white : context.accentColor,
              ),
              const SizedBox(width: 6),
              Text(
                'Trial ends in $text',
                style: TextStyle(
                  color: context.isDarkMode
                      ? Colors.white
                      : context.primaryTextColor,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
