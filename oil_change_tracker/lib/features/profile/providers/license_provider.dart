import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../auth/providers/auth_provider.dart'; // Assuming this is the correct path
import 'dart:developer' as dev;
import '../../../services/notification_service.dart'; // Assuming this is the correct path
import 'package:flutter/widgets.dart';

// Provider to track when we last refreshed user data to prevent excessive refreshing
final _lastUserDataRefreshProvider =
    StateProvider<Map<String, DateTime>>((ref) => {});

// Debounce duration for license expiry refreshes
const _refreshDebounceTime = Duration(minutes: 5);

final licenseExpiryProvider = Provider.autoDispose<DateTime?>((ref) {
  final userModel = ref.watch(authProvider).asData?.value;
  if (userModel == null) return null;

  // Return the license expiry date if available
  if (userModel.licenseExpiryDate != null) {
    return userModel.licenseExpiryDate;
  }

  // Get the map of last refresh times
  final lastRefreshMap = ref.read(_lastUserDataRefreshProvider);
  final userId = userModel.id;

  // Force refresh user data when needed - this helps when app is reinstalled
  // We do this in a post-frame callback to avoid triggering during build
  WidgetsBinding.instance.addPostFrameCallback((_) {
    // Only refresh if we're authed but have no license date
    if (userId != null) {
      final now = DateTime.now();
      final lastRefresh = lastRefreshMap[userId];

      // Only refresh if we haven't refreshed recently
      if (lastRefresh == null ||
          now.difference(lastRefresh) > _refreshDebounceTime) {
        // Log that we're doing this special refresh
        dev.log(
            'LicenseExpiryProvider: No license date found, scheduling user data refresh');

        // Update the last refresh time
        ref.read(_lastUserDataRefreshProvider.notifier).update((state) => {
              ...state,
              userId: now,
            });

        // Perform the refresh on the next micro-task to avoid triggering a
        // synchronous rebuild within the same frame (which causes the
        // "setState() or markNeedsBuild() called during build" exception).
        Future.microtask(() {
          ref.read(authProvider.notifier).forceRefreshUserData(userId);
        });
      } else {
        // Log that we're skipping the refresh due to debounce
        dev.log(
            'LicenseExpiryProvider: Skipping refresh, last refresh was ${now.difference(lastRefresh).inSeconds} seconds ago');
      }
    }
  });

  return null;
});

final licenseNotificationEnabledProvider = Provider.autoDispose<bool>((ref) {
  final userModel = ref.watch(authProvider).asData?.value;
  return userModel?.licenseExpiryNotificationEnabled ?? false;
});

class LicenseNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;
  final FirebaseFirestore _firestore;

  LicenseNotifier(this._ref, this._firestore)
      : super(const AsyncValue.data(null));

  Future<void> updateLicenseExpiryDate(DateTime? date) async {
    String? userId;

    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      // Validate the date if provided
      if (date != null) {
        final now = DateTime.now();
        final maxFutureDate = now.add(const Duration(days: 365 * 15)); // 15 years max
        final minPastDate = now.subtract(const Duration(days: 365 * 5)); // 5 years past max

        if (date.isBefore(minPastDate) || date.isAfter(maxFutureDate)) {
          dev.log('ERROR: Invalid license expiry date: $date (outside valid range)');
          if (!mounted) return;
          state = AsyncValue.error(
              Exception('License expiry date must be within a reasonable range'),
              StackTrace.current);
          return;
        }
      }

      try {
        final authModel = _ref.read(authProvider).asData?.value;
        userId = authModel?.id;

        if (userId == null || userId.isEmpty) {
          dev.log('ERROR: User ID is null or empty');
          if (!mounted) return;
          state = AsyncValue.error(
              Exception('User not authenticated'),
              StackTrace.current);
          return;
        }
      } catch (refError) {
        dev.log('ERROR: Failed to read from authProvider: $refError');
        if (!mounted) return;
        state = AsyncValue.error(
            Exception('Authentication error: ${refError.toString()}'),
            StackTrace.current);
        return;
      }

      dev.log('Starting license expiry date update for user $userId...');
      if (date != null) {
        dev.log(
            'New license expiry date will be: ${date.toIso8601String()} (${date.millisecondsSinceEpoch})');
      } else {
        dev.log('Removing license expiry date');
      }

      // Prepare update data - ALWAYS use millisecondsSinceEpoch for storage for consistent handling
      Map<String, dynamic> updateData = {
        'licenseExpiryDate':
            date != null ? date.millisecondsSinceEpoch : FieldValue.delete(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      dev.log('Update data prepared: $updateData');

      // Make sure the user document exists before updating
      try {
        final userDoc = await _firestore
            .collection('users')
            .doc(userId)
            .get(GetOptions(source: Source.server));
        dev.log('User doc exists check: ${userDoc.exists}');

        if (!userDoc.exists) {
          // Create the document with minimal fields if it doesn't exist
          await _firestore.collection('users').doc(userId).set({
            'id': userId,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));
          dev.log('Created user document for $userId since it did not exist');
        } else {
          // Log existing license data for debugging
          final userData = userDoc.data();
          if (userData != null && userData.containsKey('licenseExpiryDate')) {
            dev.log(
                'Existing licenseExpiryDate in Firestore: ${userData['licenseExpiryDate']} (type: ${userData['licenseExpiryDate'].runtimeType})');
          } else {
            dev.log('No existing licenseExpiryDate in Firestore');
          }
        }
      } catch (docError) {
        dev.log('Error checking user document: $docError');
        // Continue anyway - the update operation will create the doc if needed
      }

      bool updated = false;
      int retries = 0;
      const maxRetries = 3;
      Exception? lastError;

      // Use retry logic for better resilience
      while (!updated && retries < maxRetries) {
        retries++;
        try {
          dev.log(
              'Attempt $retries: Updating Firestore document users/$userId');

          // Always use set with merge to ensure document exists and handle both add/update/delete
          // Add timeout to prevent hanging operations
          await Future.any([
            _firestore
                .collection('users')
                .doc(userId)
                .set(updateData, SetOptions(merge: true)),
            Future.delayed(const Duration(seconds: 30)).then((_) =>
                throw Exception('Operation timed out after 30 seconds')),
          ]);

          updated = true;
          dev.log(
              '✅ License expiry date update SUCCESSFUL after $retries attempts');

          // Verify the update happened correctly
          try {
            final verifyDoc = await _firestore
                .collection('users')
                .doc(userId)
                .get(GetOptions(source: Source.server));
            final verifyData = verifyDoc.data();
            if (verifyData != null &&
                verifyData.containsKey('licenseExpiryDate')) {
              dev.log(
                  'Verified licenseExpiryDate in Firestore: ${verifyData['licenseExpiryDate']} (type: ${verifyData['licenseExpiryDate'].runtimeType})');

              // Additional checks for millisecondsSinceEpoch format
              if (verifyData['licenseExpiryDate'] is int) {
                dev.log(
                    'License expiry saved as expected integer timestamp: ${verifyData['licenseExpiryDate']}');

                // Double check the value matches what we sent
                if (date != null &&
                    verifyData['licenseExpiryDate'] !=
                        date.millisecondsSinceEpoch) {
                  dev.log(
                      'WARNING: Saved license value does not match expected timestamp: ${date.millisecondsSinceEpoch}');
                }
              } else {
                dev.log(
                    'WARNING: License expiry saved in unexpected format: ${verifyData['licenseExpiryDate'].runtimeType}');
              }
            } else if (date == null) {
              dev.log(
                  'Verified that licenseExpiryDate was deleted from Firestore');
            } else {
              dev.log(
                  'WARNING: licenseExpiryDate not found in Firestore after update!');
            }
          } catch (verifyError) {
            dev.log('Error verifying update: $verifyError');
          }
        } catch (e) {
          lastError = e is Exception ? e : Exception(e.toString());
          dev.log('Attempt $retries failed: $e');

          // Check for specific Firebase errors
          if (e.toString().contains('App Check') || e.toString().contains('attestation')) {
            dev.log('App Check error detected, continuing with retry...');
          } else if (e.toString().contains('permission-denied')) {
            dev.log('Permission denied error - stopping retries');
            break; // Don't retry permission errors
          } else if (e.toString().contains('network')) {
            dev.log('Network error detected, will retry...');
          }

          if (retries < maxRetries) {
            // Exponential backoff
            final delay = Duration(milliseconds: 300 * (1 << (retries - 1)));
            dev.log('Retrying in ${delay.inMilliseconds}ms...');
            await Future.delayed(delay);
          }
        }
      }

      if (!updated) {
        dev.log('❌ Failed to update after multiple attempts, throwing error');
        final errorMessage = lastError?.toString() ?? 'Unknown error';

        // Provide more specific error messages based on the error type
        String userFriendlyMessage;
        if (errorMessage.contains('App Check') || errorMessage.contains('attestation')) {
          userFriendlyMessage = 'Security verification failed. Please try again later.';
        } else if (errorMessage.contains('permission-denied')) {
          userFriendlyMessage = 'Permission denied. Please check your account permissions.';
        } else if (errorMessage.contains('network')) {
          userFriendlyMessage = 'Network error. Please check your internet connection.';
        } else {
          userFriendlyMessage = 'Failed to save license expiry date. Please try again.';
        }

        throw Exception(userFriendlyMessage);
      }

      // Handle notifications
      if (date != null && userId != null) {
        final notificationsEnabled =
            _ref.read(licenseNotificationEnabledProvider);
        if (notificationsEnabled) {
          dev.log('Scheduling license expiry notifications...');
          await _scheduleExpiryNotification(date, userId);
        }
      } else if (userId != null) {
        dev.log('License date was removed, canceling notifications...');
        await _cancelExpiryNotification(userId);

        // Also update notifications setting in Firestore
        try {
          await _firestore.collection('users').doc(userId).update({
            'licenseExpiryNotificationEnabled': false,
            'licenseExpiryNotificationsActive': false,
          });
          dev.log('Disabled notifications in Firestore since date was removed');
        } catch (e) {
          dev.log(
              'Warning: Error updating notification settings in Firestore: $e');
        }
      }

      // Force refresh user data & invalidate providers **after** the current
      // frame to avoid triggering rebuilds during build.
      Future.microtask(() async {
        try {
          dev.log('Refreshing user data to apply changes (deferred)...');

          // Invalidate providers first
          _ref.invalidate(licenseExpiryProvider);
          _ref.invalidate(licenseNotificationEnabledProvider);

          if (userId != null) {
            // Wait a bit to ensure Firestore has propagated the changes
            await Future.delayed(const Duration(milliseconds: 500));

            // Force refresh user data
            await _ref.read(authProvider.notifier).forceRefreshUserData(userId);
            dev.log('✅ User data refreshed successfully');

            // Invalidate auth provider after refresh to trigger UI updates
            _ref.invalidate(authProvider);
          }
        } catch (refreshError) {
          dev.log('Warning: Error refreshing user data: $refreshError');
          // Still invalidate to trigger UI updates even if refresh fails
          _ref.invalidate(authProvider);
        }
      });

      dev.log('✅ License expiry date update fully completed');
      if (!mounted) return;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      dev.log('❌ Error in updateLicenseExpiryDate: $e',
          error: e, stackTrace: stackTrace);
      if (!mounted) return;
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<void> updateNotificationSetting(bool enabled) async {
    String? userId;

    try {
      if (!mounted) return;
      state = const AsyncValue.loading();

      try {
        final authModel = _ref.read(authProvider).asData?.value;
        userId = authModel?.id;
      } catch (refError) {
        dev.log('ERROR: Failed to read from authProvider: $refError');
        if (!mounted) return;
        state = AsyncValue.error(
            Exception('Widget was disposed during operation'),
            StackTrace.current);
        return;
      }

      dev.log('Starting notification setting update for user $userId...');
      dev.log('New notification setting will be: $enabled');

      Map<String, dynamic> updateData = {
        'licenseExpiryNotificationEnabled': enabled,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      dev.log('Update data prepared: $updateData');

      bool updated = false;
      int retries = 0;
      const maxRetries = 3;
      Exception? lastError;

      while (!updated && retries < maxRetries) {
        retries++;
        try {
          dev.log(
              'Attempt $retries: Updating Firestore document users/$userId');
          if (userId != null) {
            await _firestore.collection('users').doc(userId).update(updateData);
          } else {
            throw Exception('User ID is null');
          }
          updated = true;
          dev.log(
              '✅ Notification setting update SUCCESSFUL after $retries attempts');
        } catch (e) {
          lastError = e is Exception ? e : Exception(e.toString());
          dev.log('Attempt $retries failed: $e');
          if (retries < maxRetries) {
            // Exponential backoff
            final delay = Duration(milliseconds: 300 * (1 << (retries - 1)));
            dev.log('Retrying in ${delay.inMilliseconds}ms...');
            await Future.delayed(delay);
          }
        }
      }

      if (!updated) {
        dev.log('❌ Failed to update after multiple attempts, throwing error');
        throw lastError ??
            Exception(
                'Failed to update notification setting after multiple attempts');
      }

      // Set the notification active flag in the user document
      dev.log('Setting notifications active flag in user document');
      try {
        if (enabled && userId != null) {
          // Get the license expiry date
          final expiryDate = _ref.read(licenseExpiryProvider);

          if (expiryDate != null) {
            // Schedule notifications
            await _scheduleExpiryNotification(expiryDate, userId);

            // Update the flag
            await _firestore.collection('users').doc(userId).update({
              'licenseExpiryNotificationsActive': true,
            });
          } else {
            dev.log('No license expiry date set, cannot enable notifications');
            // Update the setting to false since there's no date
            await _firestore.collection('users').doc(userId).update({
              'licenseExpiryNotificationEnabled': false,
              'licenseExpiryNotificationsActive': false,
            });
          }
        } else if (userId != null) {
          // Cancel notifications
          await _cancelExpiryNotification(userId);

          // Update the flag
          await _firestore.collection('users').doc(userId).update({
            'licenseExpiryNotificationsActive': false,
          });
        }
      } catch (e) {
        dev.log('Warning: Error updating notifications active flag: $e');
      }

      // Defer refresh/invalidation to next microtask to avoid build-phase
      // rebuild conflicts.
      final uid = userId; // capture
      Future.microtask(() async {
        try {
          dev.log(
              'Refreshing user data to reflect notification setting changes (deferred)...');
          _ref.invalidate(authProvider);
          if (uid != null) {
            await _ref.read(authProvider.notifier).forceRefreshUserData(uid);
          }
          dev.log('✅ Notification setting update fully completed');
        } catch (refreshError) {
          dev.log('Warning: Error refreshing user data: $refreshError');
          _ref.invalidate(authProvider);
        }
      });

      if (!mounted) return;
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      dev.log('❌ Error in updateNotificationSetting: $e',
          error: e, stackTrace: stackTrace);
      if (!mounted) return;
      state = AsyncValue.error(e, stackTrace);
      rethrow;
    }
  }

  Future<void> _scheduleExpiryNotification(
      DateTime expiryDate, String userId) async {
    // No direct ref access here, userId is passed in.
    try {
      NotificationService? notificationService;
      try {
        notificationService = _ref.read(notificationServiceProvider);
      } catch (e) {
        dev.log('Warning: Could not read notificationServiceProvider: $e');
        return;
      }

      final now = DateTime.now();
      final baseId = 'license_expiry_$userId'.hashCode & 0x7FFFFFFF;
      final reminders = <int, String>{
        30: 'Your driver\'s license will expire in 30 days. Consider renewing it soon.',
        14: 'Your driver\'s license will expire in 14 days. Don\'t forget to renew it.',
        7: 'Your driver\'s license expires in a week. Please renew it as soon as possible.',
        1: 'Your driver\'s license expires tomorrow. Renew it immediately if you haven\'t already.',
        0: 'Your driver\'s license expires today. Renew it immediately if you haven\'t already.',
      };
      int idx = 0;
      for (final entry in reminders.entries) {
        final daysBefore = entry.key;
        final body = entry.value;
        final scheduledDate = expiryDate.subtract(Duration(days: daysBefore));
        if (scheduledDate.isAfter(now)) {
          await notificationService?.scheduleLocalNotification(
            id: baseId + idx,
            title: 'Driver\'s License Expiry Reminder',
            body: body,
            scheduledDate: scheduledDate,
            type:
                'maintenance', // Consider a specific type like 'license_expiry'
            payload: 'license_expiry',
          );
        }
        idx++;
      }
      dev.log(
          'License expiry notifications scheduled for $userId (future dates only)');
    } catch (e) {
      dev.log('Error scheduling license expiry notifications for $userId: $e');
    }
  }

  Future<void> _cancelExpiryNotification(String userId) async {
    // No direct ref access here, userId is passed in.
    try {
      NotificationService? notificationService;
      try {
        notificationService = _ref.read(notificationServiceProvider);
      } catch (e) {
        dev.log('Warning: Could not read notificationServiceProvider: $e');
        return;
      }

      final baseId = 'license_expiry_$userId'.hashCode & 0x7FFFFFFF;
      for (int i = 0; i < 5; i++) {
        // Assuming up to 5 reminders
        await notificationService?.cancelNotification(baseId + i);
      }
      dev.log(
          'License expiry notifications canceled for $userId (IDs $baseId to ${baseId + 4})');
    } catch (e) {
      dev.log('Error canceling license expiry notifications for $userId: $e');
    }
  }
}

final licenseNotifierProvider =
    StateNotifierProvider<LicenseNotifier, AsyncValue<void>>((ref) {
  return LicenseNotifier(ref, FirebaseFirestore.instance);
});
