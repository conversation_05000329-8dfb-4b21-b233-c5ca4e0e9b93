import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../../core/services/api_key_service.dart';
import '../../ai_chat/models/chat_message.dart';
import '../../../core/models/maintenance_model.dart';
import '../../maintenance/providers/maintenance_provider.dart';
import '../../../core/providers/locale_provider.dart';
import 'dart:developer' as dev;
import '../../../core/services/analytics_service.dart';

enum AIProvider { openrouter, openai }

class AiChatService {
  static const String _openrouterBaseUrl =
      'https://openrouter.ai/api/v1/chat/completions';
  static const String _openaiBaseUrl =
      'https://api.openai.com/v1/chat/completions';
  final ApiKeyService _apiKeyService;
  final Ref _ref;

  AiChatService(this._apiKeyService, this._ref);

  /// System prompt used for both English & Arabic users.
  /// The assistant must mirror the user's language, so a single prompt is enough.
  String _getSystemPrompt() {
    return '''You are an expert automotive mechanic assistant specialized in diagnosing car problems and maintenance.

Your role is to help users with:
- Diagnosing car issues and problems
- Providing maintenance advice and recommendations
- Explaining potential causes of car problems
- Suggesting appropriate solutions and repairs

Important rules:
1. ALWAYS respond in **the same language as the CURRENT user message**, ignoring the language of earlier turns. If they write in English, respond in English. If they write in Arabic, respond in Arabic. If they write in any other language, respond in that language.
2. If the user asks about anything not related to cars, automotive maintenance, or vehicle problems, politely decline and briefly explain that you only specialize in automotive matters.
3. Be accurate in your diagnosis and ask for additional information when needed.
4. Keep responses helpful, clear, and professional.''';
  }

  Future<String> getChatResponse(
    String userMessage, {
    List<ChatMessage>? history,
    String? carId,
    String? contextSummary,
    List<MaintenanceModel>? maintenanceHistory,
  }) async {
    // Try OpenRouter (DeepSeek) first, then fallback to OpenAI
    String? response = await _tryProvider(
      AIProvider.openrouter,
      userMessage,
      history: history,
      carId: carId,
      contextSummary: contextSummary,
      maintenanceHistory: maintenanceHistory,
    );

    if (response != null) {
      return response;
    }

    // Fallback to OpenAI
    response = await _tryProvider(
      AIProvider.openai,
      userMessage,
      history: history,
      carId: carId,
      contextSummary: contextSummary,
      maintenanceHistory: maintenanceHistory,
    );

    if (response != null) {
      return response;
    }

    // If both fail, throw an error with locale-aware message
    final currentLocale = _ref.read(localeProvider);

    throw Exception(currentLocale.languageCode == 'ar'
        ? 'فشل في الاتصال بخدمة الذكاء الاصطناعي. يرجى المحاولة مرة أخرى.'
        : 'Failed to connect to AI service. Please try again.');
  }

  Future<String?> _tryProvider(
    AIProvider provider,
    String userMessage, {
    List<ChatMessage>? history,
    String? carId,
    String? contextSummary,
    List<MaintenanceModel>? maintenanceHistory,
  }) async {
    try {
      String apiKey;
      String baseUrl;
      String model;
      final analytics = _ref.read(analyticsServiceProvider);

      switch (provider) {
        case AIProvider.openrouter:
          apiKey = _apiKeyService.getOpenRouterApiKey();
          baseUrl = _openrouterBaseUrl;
          model = analytics.getRemoteConfigValue<String>(
              'ai_chat_model_openrouter', 'qwen/qwen3-30b-a3b:free');
          break;
        case AIProvider.openai:
          apiKey = _apiKeyService.getOpenAIApiKey();
          baseUrl = _openaiBaseUrl;
          model = analytics.getRemoteConfigValue<String>(
              'ai_chat_model_openai', 'gpt-3.5-turbo');
          break;
      }

      if (apiKey.isEmpty) {
        dev.log('AiChatService: ${provider.name} API key not configured');
        return null;
      }

      // Build system prompt based on user's locale
      String systemPrompt = _getSystemPrompt();
      dev.log(
          'AiChatService: Using system prompt for ${provider.name}: ${systemPrompt.substring(0, 100)}...');

      final currentLocale = _ref.read(localeProvider);

      if (contextSummary != null && contextSummary.isNotEmpty) {
        systemPrompt += '\n\nUser Information:\n$contextSummary';
      } else if (carId != null) {
        systemPrompt += '\n\nCurrent Car Information: $carId';
      }

      // Add maintenance history context if provided
      if (maintenanceHistory != null && maintenanceHistory.isNotEmpty) {
        systemPrompt += '\n\nUser\'s maintenance history for this car:';
        for (final maintenance in maintenanceHistory.take(10)) {
          // Limit to last 10 records
          systemPrompt +=
              '\n- ${maintenance.date.toString().split(' ')[0]}: ${maintenance.description} (${maintenance.maintenanceType}, ${maintenance.mileage} km, \$${maintenance.cost})';
          if (maintenance.notes.isNotEmpty) {
            systemPrompt += ' - Notes: ${maintenance.notes}';
          }
        }
      }

      // Build message list with conversation history (limit last 10 to save tokens)
      final List<Map<String, String>> messageList = [
        {
          'role': 'system',
          'content': systemPrompt,
        },
      ];

      if (history != null && history.isNotEmpty) {
        final tail = history.length > 10
            ? history.sublist(history.length - 10)
            : history;
        for (final m in tail) {
          messageList.add({
            'role': m.isUser ? 'user' : 'assistant',
            'content': m.content,
          });
        }
      }

      // Finally add current user prompt
      messageList.add({
        'role': 'user',
        'content': userMessage,
      });

      final response = await http.post(
        Uri.parse(baseUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
          if (provider == AIProvider.openrouter)
            'HTTP-Referer': 'https://oil-change-tracker.app',
          if (provider == AIProvider.openrouter)
            'X-Title': 'Oil Change Tracker',
        },
        body: json.encode({
          'model': model,
          'messages': messageList,
          'max_tokens': 1024,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final content = data['choices'][0]['message']['content'];
        dev.log(
            'AiChatService: Successfully got response from ${provider.name}');
        dev.log('AiChatService: Response content: $content');

        // Check if content is null or empty
        if (content == null || content.toString().trim().isEmpty) {
          dev.log(
              'AiChatService: Warning - received empty content from ${provider.name}');
          return null;
        }

        return content;
      } else {
        dev.log(
            'AiChatService: ${provider.name} API error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      dev.log('AiChatService: Error with ${provider.name}: $e');
      return null;
    }
  }
}

// Provider for the AI chat service
final aiChatServiceProvider = Provider<AiChatService>((ref) {
  final apiKeyService = ref.watch(apiKeyServiceProvider);
  return AiChatService(apiKeyService, ref);
});
