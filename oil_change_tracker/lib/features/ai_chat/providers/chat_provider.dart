import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../models/chat_message.dart';
import '../services/ai_chat_service.dart';
import '../../car_management/providers/car_provider.dart';
import '../../ai_usage/providers/ai_usage_providers.dart';
import '../../auth/providers/auth_provider.dart';
import '../../subscription/providers/feature_gate_provider.dart';
import '../../../core/providers/locale_provider.dart';
import '../../maintenance/data/repositories/maintenance_repository.dart';
import '../../../core/models/maintenance_model.dart';

class ChatNotifier extends StateNotifier<List<ChatMessage>> {
  ChatNotifier(this.ref) : super([]);

  final Ref ref;

  Future<void> sendMessage(String content, {String? carId}) async {
    // Check if user has access to AI chat feature
    final hasAiChatAccess =
        ref.read(featureGateProvider(PremiumFeature.aiChat));

    if (!hasAiChatAccess) {
      // Add premium required message based on locale
      final currentLocale = ref.read(localeProvider);
      final isArabic = currentLocale.languageCode == 'ar';

      final premiumMessage = ChatMessage(
        id: const Uuid().v4(),
        content: isArabic
            ? 'الدردشة مع الذكاء الاصطناعي ميزة مدفوعة. يرجى الترقية للمتابعة.'
            : 'AI Chat is a premium feature. Please upgrade to continue using this feature.',
        isUser: false,
        timestamp: DateTime.now(),
      );
      state = [...state, premiumMessage];
      return;
    }

    // Indicate loading state
    ref.read(chatLoadingProvider.notifier).state = true;

    final uuid = const Uuid();

    // Add user message
    final userMessage = ChatMessage(
      id: uuid.v4(),
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
    );

    state = [...state, userMessage];

    try {
      // Build context summary
      String contextSummary = '';
      try {
        final cars = await ref.read(carsProvider.future);
        for (final c in cars) {
          contextSummary +=
              '- ${c.year} ${c.make} ${c.model}: ${c.currentMileage} km, next oil change in ${c.kilometersUntilNextChange} km\n';
        }
      } catch (_) {}

      // Get AI response
      final aiService = ref.read(aiChatServiceProvider);

      // Get maintenance history for the car if carId is provided
      List<MaintenanceModel>? maintenanceHistory;
      if (carId != null && carId.isNotEmpty) {
        try {
          final maintenanceRepo = ref.read(maintenanceRepositoryProvider);
          maintenanceHistory =
              await maintenanceRepo.getMaintenanceForCar(carId).first;
        } catch (e) {
          // If maintenance data fails to load, continue without it
          maintenanceHistory = null;
        }
      }

      final aiResponse = await aiService.getChatResponse(
        content,
        history: state, // pass existing messages as context
        carId: carId,
        contextSummary: contextSummary,
        maintenanceHistory: maintenanceHistory,
      );

      // Add AI message
      final aiMessage = ChatMessage(
        id: uuid.v4(),
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );

      state = [...state, aiMessage];

      // Track usage (fire and forget)
      final user = ref.read(authProvider).asData?.value;
      if (user != null && user.id != null) {
        ref.read(aiUsageRepositoryProvider).incrementChat(user.id!);
      }
    } catch (e) {
      // Add error message based on locale
      final currentLocale = ref.read(localeProvider);
      final isArabic = currentLocale.languageCode == 'ar';

      final errorMessage = ChatMessage(
        id: uuid.v4(),
        content: isArabic
            ? 'عذراً، حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.'
            : 'Sorry, an error occurred while processing your request. Please try again.',
        isUser: false,
        timestamp: DateTime.now(),
      );

      state = [...state, errorMessage];
    }
    // Reset loading state
    ref.read(chatLoadingProvider.notifier).state = false;
  }

  void clearChat() {
    state = [];
  }
}

final chatProvider = StateNotifierProvider<ChatNotifier, List<ChatMessage>>(
  (ref) => ChatNotifier(ref),
);

// Provider for chat loading state
final chatLoadingProvider = StateProvider<bool>((ref) => false);
