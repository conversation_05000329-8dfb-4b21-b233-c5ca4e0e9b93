import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../generated/app_localizations.dart';
import '../../providers/chat_provider.dart';
import '../../models/chat_message.dart';
import 'chat_bubble.dart';
import 'chat_composer.dart';
import 'quick_prompt_chips.dart';
import 'typing_indicator.dart';

class CarAssistantChatSheet extends ConsumerStatefulWidget {
  final ScrollController? externalScrollController;

  const CarAssistantChatSheet({super.key, this.externalScrollController});

  @override
  ConsumerState<CarAssistantChatSheet> createState() =>
      _CarAssistantChatSheetState();
}

class _CarAssistantChatSheetState extends ConsumerState<CarAssistantChatSheet> {
  late final ScrollController _scrollController;
  final TextEditingController _textController = TextEditingController();
  bool _showScrollDown = false;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.externalScrollController ?? ScrollController();

    _scrollController.addListener(() {
      final shouldShow = _scrollController.offset > 400;
      if (shouldShow != _showScrollDown) {
        setState(() => _showScrollDown = shouldShow);
      }
    });
  }

  @override
  void dispose() {
    if (widget.externalScrollController == null) {
      _scrollController.dispose();
    }
    _textController.dispose();
    super.dispose();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendMessage(String message) {
    if (message.trim().isEmpty) return;

    ref.read(chatProvider.notifier).sendMessage(message.trim());
    _textController.clear();
    _scrollToBottom();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final messages = ref.watch(chatProvider);
    final isLoading = ref.watch(chatLoadingProvider);

    // Auto-scroll to bottom whenever new chat messages arrive
    ref.listen<List<ChatMessage>>(chatProvider, (previous, next) {
      if (previous == null || next.length > previous.length) {
        _scrollToBottom();
      }
    });

    return Stack(
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.75,
          decoration: BoxDecoration(
            color: context.containerBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: AnimatedPadding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeOut,
            child: Column(
              children: [
                // Chat area (always scrollable)
                Expanded(
                  child: ListView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    physics: const AlwaysScrollableScrollPhysics(),
                    keyboardDismissBehavior:
                        ScrollViewKeyboardDismissBehavior.onDrag,
                    children: [
                      if (messages.isEmpty) ...[
                        Center(
                          child: Column(
                            children: [
                              Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: context.accentColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(40),
                                ),
                                child: Icon(
                                  Icons.chat_bubble_outline_rounded,
                                  size: 40,
                                  color: context.accentColor,
                                ),
                              ),
                              const SizedBox(height: 24),
                              Text(
                                l10n.chatEmptyTitle,
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                  color: context.primaryTextColor,
                                ),
                                textAlign: TextAlign.center,
                              ),
                              const SizedBox(height: 8),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 32),
                                child: Text(
                                  l10n.chatEmptySubtitle,
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: context.secondaryTextColor,
                                    height: 1.4,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 32),
                              QuickPromptChips(onPromptSelected: _sendMessage),
                              const SizedBox(height: 24),
                              TextButton.icon(
                                onPressed: () {
                                  ref.read(chatProvider.notifier).clearChat();
                                },
                                style: TextButton.styleFrom(
                                  backgroundColor:
                                      context.accentColor.withOpacity(0.1),
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                icon: Icon(
                                  Icons.refresh_rounded,
                                  color: context.accentColor,
                                  size: 20,
                                ),
                                label: Text(
                                  l10n.clearChatTooltip,
                                  style: TextStyle(
                                    color: context.accentColor,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ] else ...[
                        for (final message in messages)
                          ChatBubble(
                            message: message,
                            isRTL:
                                Directionality.of(context) == TextDirection.rtl,
                          ),
                      ],
                    ],
                  ),
                ),

                // Loading indicator
                if (isLoading)
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                    child: Row(
                      children: [
                        const Icon(Icons.smart_toy, size: 20),
                        const SizedBox(width: 8),
                        TypingIndicator(color: context.accentColor),
                      ],
                    ),
                  ),

                // Message composer
                ChatComposer(
                  controller: _textController,
                  onSendMessage: _sendMessage,
                ),
              ],
            ),
          ),
        ),
        if (_showScrollDown)
          Positioned(
            bottom: 90,
            right: 16,
            child: FloatingActionButton.small(
              backgroundColor: context.accentColor,
              heroTag: 'scrollDown',
              onPressed: _scrollToBottom,
              child:
                  const Icon(Icons.arrow_downward_rounded, color: Colors.white),
            ),
          ),
      ],
    );
  }
}
