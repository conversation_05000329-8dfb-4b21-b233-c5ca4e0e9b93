import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../../../features/profile/providers/license_provider.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../main_navigation/presentation/screens/main_navigation_screen.dart'
    show mainNavIndexProvider;
import '../../../profile/presentation/screens/profile_screen.dart';

// Helper class for time remaining calculation
class TimeRemaining {
  final int years;
  final int months;
  final int weeks;
  final int days;
  final bool isPast;

  TimeRemaining({
    this.years = 0,
    this.months = 0,
    this.weeks = 0,
    this.days = 0,
    this.isPast = false,
  });

  static TimeRemaining calculate(DateTime futureDate) {
    final now = DateTime.now();
    if (futureDate.isBefore(now)) {
      final difference = now.difference(futureDate);
      return TimeRemaining(
        days: difference.inDays,
        isPast: true,
      );
    }

    DateTime tempDate = DateTime(now.year, now.month, now.day);
    int years = 0;
    int months = 0;
    int days = futureDate.difference(tempDate).inDays;

    if (days >= 365) {
      years = days ~/ 365;
      days %= 365;
    }

    if (days >= 30) {
      months = days ~/ 30; // Approximate months
      days %= 30;
    }

    int weeks = 0;
    if (days >= 7) {
      weeks = days ~/ 7;
      days %= 7;
    }

    return TimeRemaining(
        years: years, months: months, weeks: weeks, days: days);
  }

  String toDetailedString(S l10n) {
    if (isPast) {
      if (days == 0) return l10n.expiredToday;
      if (days == 1) return l10n.expiredYesterday;
      return l10n.expiredDaysAgo(days);
    }
    if (years == 0 && months == 0 && weeks == 0 && days == 0)
      return l10n.expiresToday;

    List<String> parts = [];
    if (years > 0) parts.add('$years ${years == 1 ? l10n.year : l10n.years}');
    if (months > 0)
      parts.add('$months ${months == 1 ? l10n.month : l10n.months}');
    if (weeks > 0) parts.add('$weeks ${weeks == 1 ? l10n.week : l10n.weeks}');
    if (days > 0) parts.add('$days ${days == 1 ? l10n.day : l10n.days}');

    if (parts.isEmpty)
      return l10n.expiresToday; // Should not happen if not past and not 0,0,0,0

    return l10n.expiresIn(parts.join(', '));
  }

  String toSimpleString(S l10n) {
    final totalDays = years * 365 + months * 30 + weeks * 7 + days;
    if (isPast) {
      if (this.days == 0) return l10n.expiredToday;
      if (this.days == 1) return l10n.expiredYesterday;
      return l10n.expiredDaysAgo(this.days);
    }
    if (totalDays == 0) return l10n.expiresToday;
    if (totalDays == 1) return l10n.expiresTomorrow;
    return l10n.expiresInDays(totalDays);
  }
}

class LicenseExpiryWidget extends ConsumerWidget {
  const LicenseExpiryWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expiryDate = ref.watch(licenseExpiryProvider);
    final licenseState = ref.watch(licenseNotifierProvider);
    final l10n = S.of(context);

    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      color: context.cardColor,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          // Navigate to profile screen
          ref.read(mainNavIndexProvider.notifier).state = 4;
          context.go('/main');

          // Scroll to license section after a short delay to ensure navigation is complete
          Future.delayed(const Duration(milliseconds: 300), () {
            final profileState = ref.read(profileScreenStateProvider);
            profileState?.scrollToLicenseSection();
          });
        },
        borderRadius: BorderRadius.circular(16),
        child: licenseState.isLoading
            ? Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                child: const Center(
                  child: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                    ),
                  ),
                ),
              )
            : _buildLicenseContent(context, expiryDate, l10n),
      ),
    );
  }

  Widget _buildLicenseContent(
      BuildContext context, DateTime? expiryDate, S l10n) {
    // Calculate days left and color
    final int? daysLeft;
    final Color daysColor;

    if (expiryDate != null) {
      final now = DateTime.now();
      daysLeft = expiryDate.difference(now).inDays;

      if (daysLeft < 0) {
        // Expired
        daysColor = Colors.red;
      } else if (daysLeft < 30) {
        // Less than 30 days
        daysColor = Colors.orange;
      } else {
        // More than 30 days
        daysColor = Colors.green;
      }
    } else {
      daysLeft = null;
      daysColor = context.accentColor;
    }

    final content = Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(
                Icons.drive_eta,
                size: 14,
                color: context.accentColor,
              ),
              const SizedBox(width: 3),
              Text(
                l10n.driverLicense,
                style: TextStyle(
                  fontSize: 11,
                  color: context.primaryTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 3),
          Row(
            children: [
              Expanded(
                child: expiryDate == null
                    ? Text(
                        l10n.tapToSetExpiryDate,
                        style: TextStyle(
                          fontSize: 10,
                          color: context.accentColor,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      )
                    : Row(
                        children: [
                          Expanded(
                            child: (daysLeft != null && daysLeft < 0)
                                ? Text(
                                    '${l10n.expired} - ${DateFormat('dd/MM/yyyy').format(expiryDate)}',
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: daysColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  )
                                : Text(
                                    daysLeft != null
                                        ? '$daysLeft ${l10n.days} - ${DateFormat('dd/MM/yyyy').format(expiryDate)}'
                                        : l10n.notSet,
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: daysColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                          ),
                        ],
                      ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 12,
                color: context.secondaryTextColor,
              ),
            ],
          ),
        ],
      ),
    );

    return SizedBox(height: 50, child: content);
  }
}
