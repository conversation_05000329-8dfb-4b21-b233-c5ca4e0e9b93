import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:oil_change_tracker/features/auth/providers/auth_provider.dart';
import 'package:oil_change_tracker/features/car_management/providers/car_provider.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../shared/widgets/primary_button.dart';
import '../widgets/dashboard_content_enhanced.dart';
import '../../../../shared/services/image_cache_service.dart';
import '../../../../core/mixins/analytics_mixin.dart';
import '../../../../core/services/notification_service.dart';
import 'dart:developer' as dev;
// Import BannerAdWidget
import '../../../../features/ads/presentation/widgets/banner_ad_widget.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/unified_app_bar.dart';
import '../../../../core/models/car_model.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
// Import voice input related functionality
import '../../../../features/voice_input/models/voice_form_type.dart';
import 'dart:async';
import '../../../../features/subscription/providers/feature_gate_provider.dart';
import '../../../../features/subscription/providers/subscription_provider.dart';
import '../../../../features/subscription/presentation/screens/subscription_screen.dart';
import '../../../../features/subscription/presentation/widgets/premium_features_list.dart';
import '../../../../features/voice_input/services/voice_command_service.dart';
// Import AI chat functionality
import '../../../../features/ai_chat/presentation/widgets/car_assistant_chat_sheet.dart';
import '../../../voice_input/presentation/widgets/listening_bottom_sheet.dart';

// Provider to access the current dashboard content
final dashboardContentProvider =
    StateProvider<DashboardContentEnhanced?>((ref) => null);

/// Global singleton function to show mileage update dialog from anywhere in the app
Future<void> showMileageUpdateDialog(
    BuildContext context, WidgetRef ref, CarModel car) async {
  if (!context.mounted) return;

  // Get DashboardScreen state if available (for fallback mechanism)
  final dashboardState = DashboardScreen.of(context);
  if (dashboardState != null) {
    return dashboardState.showMileageUpdateDialog(car);
  } else {
    // Direct fallback if not called from dashboard context
    return _showMileageUpdateDialogImpl(context, ref, car);
  }
}

/// Implementation of the mileage update dialog - the single source of truth
Future<void> _showMileageUpdateDialogImpl(
    BuildContext context, WidgetRef ref, CarModel car) async {
  final l10n = S.of(context);
  final scaffoldContext = context;

  // Initialize text editing controller
  final mileageController =
      TextEditingController(text: car.currentMileage.toString());

  // Track error message and loading state
  String? errorMessage;
  bool isLoading = false;

  // Always pop any existing dialogs first
  Navigator.of(context).popUntil((route) => route.isFirst);

  // Show the dialog
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (dialogContext) {
      return StatefulBuilder(
        builder: (context, setState) {
          // Position cursor at the end initially
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mileageController.text.isNotEmpty && context.mounted) {
              mileageController.selection = TextSelection.fromPosition(
                TextPosition(offset: mileageController.text.length),
              );
            }
          });

          // Handler for update button press
          void onUpdatePressed() {
            // Get and parse the input
            final input = mileageController.text.trim();
            final newMileage = int.tryParse(input);

            // Validate the input
            if (input.isEmpty) {
              setState(() {
                errorMessage = l10n.mileageRequired;
              });
              return;
            }

            if (newMileage == null) {
              setState(() {
                errorMessage = l10n.invalidMileage;
              });
              return;
            }

            if (newMileage <= car.currentMileage) {
              setState(() {
                errorMessage = l10n.mileageMustBeGreaterThan(
                    NumberFormat.decimalPattern().format(car.currentMileage));
              });
              return;
            }

            if (newMileage > 1000000) {
              setState(() {
                errorMessage = l10n.mileageTooHigh;
              });
              return;
            }

            // All validation passed - attempt to update
            setState(() {
              isLoading = true;
              errorMessage = null;
            });

            // Update the car mileage
            if (car.id != null) {
              ref
                  .read(carNotifierProvider(car.id!).notifier)
                  .updateCarMileage(car.id!, newMileage, scaffoldContext)
                  .then((_) {
                Navigator.of(dialogContext).pop();

                if (scaffoldContext.mounted) {
                  ScaffoldMessenger.of(scaffoldContext).showSnackBar(SnackBar(
                    content: Text(l10n.mileageUpdatedTo(
                      car.make,
                      car.model,
                      NumberFormat.decimalPattern().format(newMileage),
                    )),
                    backgroundColor: context.accentColor,
                    behavior: SnackBarBehavior.floating,
                  ));
                }
              }).catchError((e) {
                AppLogger.error('Error updating mileage: $e');
                setState(() {
                  isLoading = false;
                  errorMessage = e.toString().contains('must be greater')
                      ? l10n.mileageMustBeGreaterThan(
                          NumberFormat.decimalPattern()
                              .format(car.currentMileage))
                      : l10n.errorOccurred;
                });
              });
            } else {
              setState(() {
                isLoading = false;
                errorMessage = l10n.errorOccurred;
              });
            }
          }

          return AlertDialog(
            backgroundColor: context.containerBackgroundColor,
            titlePadding: const EdgeInsets.all(0),
            title: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.1),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 30,
                    backgroundColor: Colors.white.withOpacity(0.9),
                    child:
                        Icon(Icons.speed, color: context.accentColor, size: 36),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.updateMileage,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: context.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    l10n.pleaseEnterCurrentMileage,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: context.secondaryTextColor,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 0),
            content: Container(
              width: double.maxFinite,
              constraints: const BoxConstraints(maxHeight: 350),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(height: 20),
                    // Car info section
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: context.accentColor.withOpacity(0.1),
                          child: Icon(
                            Icons.directions_car,
                            color: context.accentColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${car.year} ${car.make} ${car.model}',
                                style: TextStyle(
                                  color: context.primaryTextColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Current: ${NumberFormat('#,###').format(car.currentMileage)} km',
                                style: TextStyle(
                                  color: context.secondaryTextColor,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Mileage input field
                    TextFormField(
                      controller: mileageController,
                      keyboardType: TextInputType.number,
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      autofocus: true,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      onChanged: (value) {
                        if (errorMessage != null) {
                          setState(() {
                            errorMessage = null;
                          });
                        }
                      },
                      onFieldSubmitted: (_) {
                        if (!isLoading) {
                          onUpdatePressed();
                        }
                      },
                      decoration: InputDecoration(
                        labelText: l10n.currentMileage,
                        labelStyle: TextStyle(
                          color: context.secondaryTextColor,
                        ),
                        hintText: l10n.currentMileage,
                        suffixText: 'km',
                        prefixIcon:
                            Icon(Icons.speed, color: context.accentColor),
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(
                              color: context.accentColor.withOpacity(0.3)),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: context.accentColor),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        errorBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: Colors.red),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        focusedErrorBorder: OutlineInputBorder(
                          borderSide: const BorderSide(color: Colors.red),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        filled: true,
                        fillColor: context.containerBackgroundColor,
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        errorStyle: const TextStyle(
                          height: 0,
                          color: Colors.transparent,
                        ),
                      ),
                    ),
                    if (errorMessage != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.red.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.error_outline,
                                color: Colors.red, size: 16),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                errorMessage!,
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 24),
                    Container(
                      margin: const EdgeInsets.only(bottom: 8, top: 8),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: isLoading
                                  ? null
                                  : () => Navigator.pop(dialogContext),
                              style: TextButton.styleFrom(
                                foregroundColor: context.secondaryTextColor,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                backgroundColor:
                                    context.accentColor.withOpacity(0.05),
                              ),
                              child: Text(
                                l10n.cancel,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: isLoading ? null : onUpdatePressed,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: context.accentColor,
                                foregroundColor: Colors.white,
                                disabledBackgroundColor:
                                    context.accentColor.withOpacity(0.3),
                                elevation: 0,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                              ),
                              child: isLoading
                                  ? const SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    )
                                  : Text(
                                      l10n.update,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            insetPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
          );
        },
      );
    },
  );
}

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();

  /// Returns the nearest DashboardScreen ancestor from the widget tree
  static _DashboardScreenState? of(BuildContext context) {
    final state = context.findAncestorStateOfType<_DashboardScreenState>();
    return state;
  }
}

class _DashboardScreenState extends ConsumerState<DashboardScreen>
    with SingleTickerProviderStateMixin, AnalyticsMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  // Remove voice-related state variables
  // ProviderSubscription? _voiceRecordingSubscription;
  // bool _sheetShown = false;
  // BuildContext? _sheetContext;

  // Flag to track if we've requested notification permissions
  bool _hasRequestedNotificationPermissions = false;

  @override
  void initState() {
    super.initState();

    // Initialize analytics tracking
    initAnalytics('dashboard_screen');

    // Animation setup
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    _animationController.forward();

    // Schedule a rebuild after mounting to ensure fresh data
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Refresh auth data
      ref.invalidate(authProvider);

      // Only clear image caches if this is the first time loading the screen
      // Don't clear on every rebuild as it causes images to reload unnecessarily
      if (!_hasClearedCache) {
        await ref.read(imageCacheServiceProvider).clearAllCaches();
        dev.log('DashboardScreen: Cleared all image caches on initial load');
        _hasClearedCache = true;
      }

      // Request notification permissions after the UI is built
      // Only do this once per session
      if (!_hasRequestedNotificationPermissions) {
        _requestNotificationPermissions();
        _hasRequestedNotificationPermissions = true;
      }
    });

    // Remove voice listener setup
    // _setupVoiceRecordingListener();
  }

  // Request notification permissions
  Future<void> _requestNotificationPermissions() async {
    try {
      final notificationService = ref.read(notificationServiceProvider);
      final permissionGranted = await notificationService.requestPermissions();

      AppLogger.info(
          'Notification permission request result: $permissionGranted');

      // Track analytics for permission request
      trackFeatureUsage('notification_permission_request',
          params: {'granted': permissionGranted});

      // If permission granted, schedule notifications for all cars
      if (permissionGranted == true && mounted) {
        // Get the cars data
        final carsAsync = ref.read(carsProvider);

        // When cars data is available, schedule notifications
        carsAsync.whenData((cars) async {
          if (cars.isNotEmpty && mounted) {
            try {
              // Get the notification service directly
              final notificationService = ref.read(notificationServiceProvider);

              // Make a defensive copy of cars and filter out any with null IDs
              final validCars = cars
                  .where((car) => car.id != null && car.id!.isNotEmpty)
                  .toList();

              if (validCars.isEmpty) {
                AppLogger.warning(
                    'No valid cars with IDs found for scheduling notifications');
                return;
              }

              // Schedule notifications for all cars with individual try/catch to prevent one failure from stopping others
              for (final car in validCars) {
                try {
                  await notificationService.scheduleOilChangeNotification(
                      car, context);

                  if (car.licenseExpiryDate != null) {
                    await notificationService.scheduleLicenseExpiryNotification(
                        car, context);
                  }
                } catch (carError) {
                  AppLogger.error(
                      'Error scheduling notification for car ${car.id}: $carError');
                  // Continue with next car
                }
              }

              AppLogger.info(
                  'Scheduled notifications for ${validCars.length} cars');

              // Track analytics
              trackFeatureUsage('notifications_scheduled',
                  params: {'car_count': validCars.length});
            } catch (e) {
              AppLogger.error('Error scheduling notifications: $e');
            }
          }
        });
      }
    } catch (e) {
      AppLogger.error('Error requesting notification permissions: $e');
    }
  }

  // Flag to track if we've already cleared the cache
  bool _hasClearedCache = false;

  @override
  void dispose() {
    _animationController.dispose();
    // Remove voice subscription cleanup
    // _voiceRecordingSubscription?.close();
    // _closeSheet();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final carsState = ref.watch(carsProvider);
    final l10n = S.of(context);

    // Use device safe-area insets to ensure equal horizontal margins
    final mediaPadding = MediaQuery.of(context).padding;

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBarFactory.dashboard(
        title: l10n.dashboard,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: l10n.settings,
            onPressed: () => context.push('/settings'),
          ),
        ],
      ),
      body: SafeArea(
        top: false,
        child: Column(
          children: [
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  // Refresh auth and cars data
                  ref.invalidate(authProvider);
                  ref.invalidate(carsProvider);

                  // Also refresh subscription data
                  await ref.read(subscriptionProvider.notifier).refresh();
                },
                color: context.accentColor,
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      authState.when(
                        data: (user) {
                          if (user == null) {
                            return _buildSignInPrompt(context, l10n);
                          }
                          return carsState.when(
                            data: (cars) {
                              // Create the dashboard content
                              final dashboardContent = DashboardContentEnhanced(
                                userName:
                                    user.displayName ?? (user.email ?? ''),
                                cars: cars,
                                onCarTap: (car) {},
                              );

                              // Store a reference to the dashboard content for later use
                              WidgetsBinding.instance.addPostFrameCallback((_) {
                                if (mounted) {
                                  ref
                                      .read(dashboardContentProvider.notifier)
                                      .state = dashboardContent;
                                }
                              });

                              return dashboardContent;
                            },
                            loading: () => const Center(
                              child: Padding(
                                padding: EdgeInsets.all(48.0),
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            error: (error, stackTrace) => _buildErrorView(
                              context,
                              l10n,
                              error.toString(),
                              () => ref.refresh(carsProvider),
                            ),
                          );
                        },
                        loading: () => const Center(
                          child: Padding(
                            padding: EdgeInsets.all(48.0),
                            child: CircularProgressIndicator(),
                          ),
                        ),
                        error: (error, stackTrace) => _buildErrorView(
                          context,
                          l10n,
                          error.toString(),
                          () => ref.refresh(authProvider),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Add banner ad at the bottom of the screen
            if (authState.valueOrNull !=
                null) // Only show ad for signed-in users
              const BannerAdWidget(adSize: BannerAdWidget.banner),
          ],
        ),
      ),
      // Removed local FloatingActionButtons in favor of the new bottom
      // navigation design. Voice and AI chat can now be accessed via the
      // dedicated "AI" tab.
      floatingActionButton: null,
    );
  }

  Widget _buildSignInPrompt(BuildContext context, S l10n) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 40),
          Icon(Icons.account_circle, color: context.accentColor, size: 64),
          const SizedBox(height: 32),
          Text(
            l10n.pleaseSignIn,
            style: TextStyle(
              color: context.primaryTextColor,
              fontSize: 22,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "You need to sign in to track your vehicles",
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: context.primaryTextColor,
                ),
          ),
          const SizedBox(height: 40),
          PrimaryButton(
            text: l10n.signIn,
            onPressed: () => context.push('/login'),
            icon: Icons.login,
            fullWidth: true,
          )
        ],
      ),
    );
  }

  Widget _buildErrorView(
      BuildContext context, S l10n, String errorMessage, VoidCallback onRetry) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 40),
          Icon(Icons.error_outline,
              color: context.secondaryAccentColor, size: 64),
          const SizedBox(height: 32),
          Text(
            l10n.error,
            style: TextStyle(
              color: context.secondaryAccentColor,
              fontSize: 22,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            style: TextStyle(
              color: context.secondaryTextColor,
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),
          PrimaryButton(
            text: l10n.retry,
            onPressed: onRetry,
            icon: Icons.refresh,
            fullWidth: true,
          )
        ],
      ),
    );
  }

  /// Public method to show the mileage update dialog - delegates to the shared implementation
  void showMileageUpdateDialog(CarModel car) {
    if (!mounted) return;
    _showMileageUpdateDialogImpl(context, ref, car);
  }

  // Show voice command options bottom sheet
  void _showVoiceInputOptions(BuildContext context) {
    final l10n = S.of(context);

    // Check if the user has access to voice input feature
    final hasVoiceAccess =
        ref.read(featureGateProvider(PremiumFeature.voiceInput));

    if (!hasVoiceAccess) {
      // Show subscription promotion dialog
      _showSubscriptionPromotion(context);
      return;
    }

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  l10n.voiceCommands,
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Text(
                  l10n.quickActions,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 16),

                // Oil Change Option
                ListTile(
                  leading: Icon(Icons.oil_barrel, color: context.accentColor),
                  title: Text(l10n.addOilChange),
                  onTap: () {
                    Navigator.pop(context);
                    Future.microtask(() {
                      _showVoiceOptionDialog(VoiceFormType.oilChange);
                    });
                  },
                ),

                // Maintenance Option
                ListTile(
                  leading: Icon(Icons.build, color: context.accentColor),
                  title: Text(l10n.addMaintenance),
                  onTap: () {
                    Navigator.pop(context);
                    Future.microtask(() {
                      _showVoiceOptionDialog(VoiceFormType.maintenance);
                    });
                  },
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  // Update the subscription promotion dialog
  void _showSubscriptionPromotion(BuildContext context) {
    final l10n = S.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.premiumFeature),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(l10n.premiumRequired),
            const SizedBox(height: 16),
            Text(l10n.upgradeToRemoveAds),
            const SizedBox(height: 12),
            const SimplePremiumFeaturesList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(l10n.cancel),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => const SubscriptionScreen()));
            },
            child: Text(l10n.upgradeNow),
          ),
        ],
      ),
    );
  }

  // Add method to handle voice input completion
  void _handleVoiceComplete(VoiceFormType formType, String transcription,
      Map<String, dynamic>? extractedData) {
    VLOG('ui', 'Voice complete for $formType: "$transcription"');

    // Determine carId if available in extracted data
    String? carId = extractedData?['carId']?.toString();

    String targetRoute;
    switch (formType) {
      case VoiceFormType.oilChange:
        if (carId != null && carId.isNotEmpty) {
          targetRoute = '/cars/$carId/oil-changes/add';
        } else {
          targetRoute = '/oil-changes/add';
        }
        break;
      case VoiceFormType.maintenance:
        if (carId != null && carId.isNotEmpty) {
          targetRoute = '/cars/$carId/maintenance/add';
        } else {
          targetRoute = '/maintenance/add';
        }
        break;
    }

    context.push(targetRoute, extra: extractedData);
  }

  void _showVoiceOptionDialog(VoiceFormType formType) {
    final locale = Localizations.localeOf(context);
    final langCode = locale.languageCode == 'ar' ? 'ar-EG' : 'en-US';

    // Obtain VoiceCommandService via Riverpod
    final voiceService = ref.read(voiceCommandServiceProvider);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (ctx) => ListeningBottomSheet(
        formType: formType,
        onCancel: () async {
          await voiceService.cancelRecording();
          if (ctx.mounted) Navigator.pop(ctx);
        },
        onStopAndSend: () async {
          // Process the recorded voice and extract data
          final result = await voiceService.stopAndProcess(
            VoiceContext(
              originScreen: formType,
              languageCode: langCode,
            ),
          );

          if (ctx.mounted) Navigator.pop(ctx);

          if (result.success && result.transcription != null) {
            _handleVoiceComplete(
              formType,
              result.transcription!,
              result.extractedData,
            );
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      result.error ?? S.of(context).couldNotUnderstandCommand),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }
        },
      ),
    );
  }

  // Show AI chat assistant bottom sheet
  void _showAiChatSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useSafeArea: true,
      builder: (context) => DraggableScrollableSheet(
        expand: false,
        initialChildSize: 0.75,
        minChildSize: 0.5,
        maxChildSize: 1.0,
        builder: (ctx, scrollController) {
          return CarAssistantChatSheet(
            externalScrollController: scrollController,
          );
        },
      ),
    );
  }
}
