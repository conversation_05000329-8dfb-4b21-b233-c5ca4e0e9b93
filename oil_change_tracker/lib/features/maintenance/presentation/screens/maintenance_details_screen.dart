import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:io';
import 'dart:developer' as dev;
import '../../../../core/models/maintenance_model.dart';
import '../../providers/maintenance_provider.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../car_management/providers/car_provider.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart';

class MaintenanceDetailsScreen extends ConsumerWidget {
  final String maintenanceId;
  final String carId;

  const MaintenanceDetailsScreen({
    super.key,
    required this.maintenanceId,
    required this.carId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);

    // Use only the notifier provider to avoid race conditions
    final maintenanceAsync = ref.watch(maintenanceNotifierProvider(carId));
    final carAsync = ref.watch(carProvider(carId));

    // Add post-frame callback to refresh data when the screen is shown
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Only refresh if in error state
      if (maintenanceAsync is AsyncError) {
        // Force refresh the providers
        ref.invalidate(maintenanceNotifierProvider(carId));
      }
    });

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.maintenanceDetails,
          style: TextStyle(color: context.accentColor),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Explicit navigation back to maintenance list
            context.go('/cars/$carId/maintenance');
          },
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: context.accentColor),
            onPressed: () {
              // Force refresh the data using only the notifier provider
              ref.invalidate(maintenanceNotifierProvider(carId));
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Refreshing...'),
                  duration: const Duration(seconds: 1),
                ),
              );
            },
          ),
          IconButton(
            icon: Icon(Icons.edit, color: context.accentColor),
            onPressed: () {
              // Navigate to edit maintenance screen
              context.push('/cars/$carId/maintenance/$maintenanceId/edit');
            },
          ),
          IconButton(
            icon: Icon(Icons.delete, color: context.accentColor),
            onPressed: () {
              _showDeleteConfirmation(context, ref);
            },
          ),
        ],
      ),
      body: maintenanceAsync.when(
        data: (maintenanceList) {
          // Check if the list is empty first
          if (maintenanceList.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: context.secondaryAccentColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.noMaintenanceRecords,
                    style: TextStyle(
                      color: context.secondaryAccentColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Go back to maintenance list
                      context.go('/cars/$carId/maintenance');
                    },
                    icon: const Icon(Icons.arrow_back),
                    label: Text(S.of(context).goBack),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          // Try to find the maintenance record
          final maintenance = maintenanceList.firstWhere(
            (m) => m.id == maintenanceId,
            orElse: () => MaintenanceModel(
              carId: carId,
              userId: '',
              description: '',
              maintenanceType: 'generalService',
              mileage: 0,
              cost: 0,
              serviceProvider: '',
              notes: '',
              date: DateTime.now(),
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          );

          if (maintenance.id == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.search_off,
                    size: 64,
                    color: context.secondaryAccentColor,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '${l10n.errorLoadingMaintenanceDetails}\nThe maintenance record may have been deleted.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: context.secondaryAccentColor,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton.icon(
                    onPressed: () {
                      // Go back to maintenance list
                      context.go('/cars/$carId/maintenance');
                    },
                    icon: const Icon(Icons.arrow_back),
                    label: Text(S.of(context).backToMaintenanceList),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.accentColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ],
              ),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic info card
                Card(
                  color: context.containerBackgroundColor,
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                    side: BorderSide(
                      color: context.accentColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: context.accentColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                _getMaintenanceIcon(
                                    maintenance.maintenanceType),
                                color: context.accentColor,
                                size: 32,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getLocalizedMaintenanceType(
                                        maintenance.maintenanceType, l10n),
                                    style: TextStyle(
                                      color: context.accentColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 20,
                                    ),
                                  ),
                                  Text(
                                    DateFormat.yMMMMd()
                                        .format(maintenance.date),
                                    style: TextStyle(
                                      color: context.secondaryTextColor,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Divider(
                          color: context.accentColor.withOpacity(0.2),
                          thickness: 1,
                        ),
                        const SizedBox(height: 16),
                        _buildInfoRow(
                          context,
                          Icons.speed,
                          l10n.mileage,
                          '${NumberFormat.decimalPattern().format(maintenance.mileage)} km',
                        ),
                        const SizedBox(height: 12),
                        _buildInfoRow(
                          context,
                          Icons.attach_money,
                          l10n.cost,
                          NumberFormat.currency(
                            symbol: '',
                            decimalDigits: 2,
                          ).format(maintenance.cost),
                        ),
                        if (maintenance.serviceProvider.isNotEmpty) ...[
                          const SizedBox(height: 12),
                          _buildInfoRow(
                            context,
                            Icons.store,
                            l10n.serviceProvider,
                            maintenance.serviceProvider,
                          ),
                        ],
                        carAsync.when(
                          data: (car) => car != null
                              ? Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const SizedBox(height: 12),
                                    _buildInfoRow(
                                      context,
                                      Icons.directions_car,
                                      l10n.car,
                                      '${car.year} ${car.make} ${car.model}',
                                    ),
                                  ],
                                )
                              : const SizedBox.shrink(),
                          loading: () => const SizedBox.shrink(),
                          error: (_, __) => const SizedBox.shrink(),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Description section
                if (maintenance.description.isNotEmpty) ...[
                  Text(
                    l10n.maintenanceDescription,
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: context.accentColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      maintenance.description,
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Notes section
                if (maintenance.notes.isNotEmpty) ...[
                  Text(
                    l10n.notes,
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: context.accentColor.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: context.accentColor.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      maintenance.notes,
                      style: TextStyle(
                        color: context.primaryTextColor,
                        fontSize: 16,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                ],

                // Photos section
                if (maintenance.photoUrls.isNotEmpty) ...[
                  Text(
                    '${l10n.receiptPhotos} (${maintenance.photoUrls.length})',
                    style: TextStyle(
                      color: context.accentColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: maintenance.photoUrls.length,
                      itemBuilder: (context, index) {
                        final photoUrl = maintenance.photoUrls[index];
                        return Padding(
                          padding: const EdgeInsets.only(right: 12),
                          child: GestureDetector(
                            onTap: () =>
                                _viewPhotoFullScreen(context, photoUrl),
                            child: Container(
                              width: 120,
                              height: 120,
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color:
                                        context.accentColor.withOpacity(0.3)),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: CachedNetworkImage(
                                  imageUrl: photoUrl,
                                  fit: BoxFit.cover,
                                  placeholder: (context, url) => Container(
                                    color:
                                        context.accentColor.withOpacity(0.05),
                                    child: Center(
                                      child: CircularProgressIndicator(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                                context.accentColor),
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                                  errorWidget: (context, url, error) {
                                    dev.log(
                                        'Error loading image thumbnail: $error, URL: $url');

                                    // Check if this is a network connectivity error
                                    if (error is SocketException) {
                                      return Container(
                                        color: context.accentColor
                                            .withOpacity(0.05),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.signal_wifi_off,
                                              color:
                                                  context.secondaryAccentColor,
                                              size: 24,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              'Offline',
                                              style: TextStyle(
                                                color:
                                                    context.secondaryTextColor,
                                                fontSize: 10,
                                              ),
                                              textAlign: TextAlign.center,
                                            ),
                                          ],
                                        ),
                                      );
                                    }

                                    return Container(
                                      color:
                                          context.accentColor.withOpacity(0.05),
                                      child: Icon(
                                        Icons.broken_image,
                                        color: context.secondaryAccentColor,
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],

                const SizedBox(height: 32),

                // Timestamps
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Created: ${DateFormat.yMMMd().add_jm().format(maintenance.createdAt)}',
                        style: TextStyle(
                          color: context.secondaryTextColor,
                          fontSize: 12,
                        ),
                      ),
                      if (maintenance.updatedAt != maintenance.createdAt)
                        Text(
                          'Updated: ${DateFormat.yMMMd().add_jm().format(maintenance.updatedAt)}',
                          style: TextStyle(
                            color: context.secondaryTextColor,
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
        // Use a custom loading indicator that fills the whole screen to prevent background content showing through
        loading: () => Container(
          color: context.containerBackgroundColor,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
        error: (error, _) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: context.secondaryAccentColor,
              ),
              const SizedBox(height: 16),
              SelectableText.rich(
                TextSpan(
                  children: [
                    TextSpan(
                      text: 'Error loading maintenance details\n',
                      style: TextStyle(
                        color: context.secondaryAccentColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    TextSpan(
                      text: error.toString(),
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  // Go back to maintenance list
                  context.go('/cars/$carId/maintenance');
                },
                icon: const Icon(Icons.arrow_back),
                label: Text(S.of(context).backToMaintenanceList),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.accentColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    IconData icon,
    String label,
    String value,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: context.accentColor,
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  color: context.secondaryTextColor,
                  fontSize: 14,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  color: context.primaryTextColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _viewPhotoFullScreen(BuildContext context, String photoUrl) {
    // Use a custom path with unique parameter to avoid navigation conflicts
    context.push('/photo-view?url=${Uri.encodeComponent(photoUrl)}',
        extra: photoUrl);
  }

  String _getLocalizedMaintenanceType(String type, S l10n) {
    switch (type) {
      case 'generalService':
        return l10n.generalService;
      case 'brakeService':
        return l10n.brakeService;
      case 'engineService':
        return l10n.engineService;
      case 'transmissionService':
        return l10n.transmissionService;
      case 'tireService':
        return l10n.tireService;
      case 'batteryService':
        return l10n.batteryService;
      case 'airConditioning':
        return l10n.airConditioning;
      case 'electricalSystem':
        return l10n.electricalSystem;
      case 'suspension':
        return l10n.suspension;
      case 'exhaustSystem':
        return l10n.exhaustSystem;
      case 'fuelSystem':
        return l10n.fuelSystem;
      case 'coolingSystem':
        return l10n.coolingSystem;
      case 'regularMaintenance':
        return l10n.regularMaintenance;
      case 'other':
        return l10n.other;
      default:
        return l10n.generalService;
    }
  }

  IconData _getMaintenanceIcon(String type) {
    switch (type) {
      case 'brakeService':
        return Icons.warning;
      case 'engineService':
        return Icons.precision_manufacturing;
      case 'transmissionService':
        return Icons.settings;
      case 'tireService':
        return Icons.circle;
      case 'batteryService':
        return Icons.battery_charging_full;
      case 'airConditioning':
        return Icons.ac_unit;
      case 'electricalSystem':
        return Icons.electrical_services;
      case 'suspension':
        return Icons.line_weight;
      case 'exhaustSystem':
        return Icons.air;
      case 'fuelSystem':
        return Icons.local_gas_station;
      case 'coolingSystem':
        return Icons.water_drop;
      case 'regularMaintenance':
        return Icons.calendar_month;
      default:
        return Icons.handyman;
    }
  }

  // Add delete confirmation dialog
  void _showDeleteConfirmation(BuildContext context, WidgetRef ref) {
    final l10n = S.of(context);

    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        title: Text(
          l10n.deleteMaintenance,
          style: TextStyle(
            color: context.accentColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          l10n.confirmDeleteMaintenance,
          style: TextStyle(color: context.primaryTextColor),
        ),
        actions: [
          TextButton(
            onPressed: () => context.pop(false),
            child: Text(
              l10n.cancel,
              style: TextStyle(color: context.secondaryTextColor),
            ),
          ),
          TextButton(
            onPressed: () => context.pop(true),
            child: Text(
              l10n.delete,
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    ).then((confirmed) async {
      if (confirmed == true) {
        try {
          // Show loading indicator
          if (context.mounted) {
            _showLoadingDialog(context);
          }

          // Delete the maintenance record using only the notifier provider
          final notifier =
              ref.read(maintenanceNotifierProvider(carId).notifier);
          notifier.setL10n(S.of(context));
          await notifier.deleteMaintenance(maintenanceId);

          // Close loading indicator if context is still mounted
          if (context.mounted) {
            // Pop the loading dialog
            context.pop();

            // Show success message
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(l10n.maintenanceDeleted),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );

            // Navigate back to the car details screen with maintenance tab selected
            if (context.mounted) {
              // Use go for navigation while adding a parameter to indicate the maintenance tab should be selected
              context.go('/cars/$carId?source=maintenance');
            }
          }
        } catch (e) {
          dev.log('Error deleting maintenance: $e');

          // Close loading indicator if it's still showing
          if (context.mounted) {
            // Pop the loading dialog
            context.pop();

            // Show error in a SelectableText.rich widget
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                backgroundColor: context.containerBackgroundColor,
                title: Text(
                  l10n.error,
                  style: TextStyle(
                    color: context.secondaryAccentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                content: SelectableText.rich(
                  TextSpan(
                    text: e.toString().split('Exception:').last.trim(),
                    style: const TextStyle(color: Colors.red),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => context.pop(),
                    child: Text(
                      l10n.ok,
                      style: TextStyle(color: context.accentColor),
                    ),
                  ),
                ],
              ),
            );
          }
        }
      }
    });
  }

  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: context.containerBackgroundColor,
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(context.accentColor),
            ),
            const SizedBox(width: 20),
            Text(
              S.of(context).loading,
              style: TextStyle(color: context.primaryTextColor),
            ),
          ],
        ),
      ),
    );
  }
}

/// Full-screen photo viewer screen with zooming capability
class _PhotoViewScreen extends StatelessWidget {
  final String photoUrl;

  const _PhotoViewScreen({required this.photoUrl});

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: Center(
        child: InteractiveViewer(
          minScale: 0.5,
          maxScale: 4.0,
          child: CachedNetworkImage(
            imageUrl: photoUrl,
            placeholder: (context, url) => const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            errorWidget: (context, url, error) {
              dev.log('Image loading error: $error, URL: $url');

              // Check if this is a network connectivity error
              if (error is SocketException) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.signal_wifi_off,
                        color: Colors.white,
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        l10n.networkError,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.checkInternetConnection,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 24),
                      OutlinedButton(
                        onPressed: () => context.pop(),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: Colors.white),
                          foregroundColor: Colors.white,
                        ),
                        child: Text(l10n.cancel),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.white,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.errorLoadingImage,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                ],
              );
            },
            fit: BoxFit.contain,
          ),
        ),
      ),
    );
  }
}
