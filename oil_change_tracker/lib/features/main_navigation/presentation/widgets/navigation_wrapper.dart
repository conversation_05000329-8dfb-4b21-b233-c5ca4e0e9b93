import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../../generated/app_localizations.dart';
import '../../../dashboard/presentation/screens/dashboard_screen.dart';
import '../../../car_management/presentation/screens/car_list_screen.dart';
import '../../../settings/presentation/screens/settings_screen.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../widgets/ai_root_screen.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../subscription/providers/subscription_provider.dart';
import '../screens/main_navigation_screen.dart';

/// A wrapper that provides bottom navigation for primary screens
class NavigationWrapper extends ConsumerStatefulWidget {
  final Widget child;
  final String currentRoute;

  const NavigationWrapper({
    super.key,
    required this.child,
    required this.currentRoute,
  });

  @override
  ConsumerState<NavigationWrapper> createState() => _NavigationWrapperState();
}

class _NavigationWrapperState extends ConsumerState<NavigationWrapper>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    final current = ref.read(mainNavIndexProvider);
    if (current != index) {
      ref.read(mainNavIndexProvider.notifier).state = index;
      _controller.forward(from: 0.0);
      
      // Navigate to the appropriate tab
      switch (index) {
        case 0:
          context.go('/main');
          break;
        case 1:
          context.go('/main'); // Will show cars tab
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref.read(mainNavIndexProvider.notifier).state = 1;
          });
          break;
        case 2:
          context.go('/main'); // Will show AI tab
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref.read(mainNavIndexProvider.notifier).state = 2;
          });
          break;
        case 3:
          context.go('/main'); // Will show settings tab
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref.read(mainNavIndexProvider.notifier).state = 3;
          });
          break;
        case 4:
          context.go('/main'); // Will show profile tab
          WidgetsBinding.instance.addPostFrameCallback((_) {
            ref.read(mainNavIndexProvider.notifier).state = 4;
          });
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    final currentIndex = ref.watch(mainNavIndexProvider);

    // Determine if this route should show bottom navigation
    final shouldShowBottomNav = _shouldShowBottomNavigation(widget.currentRoute);

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () async {
          final analytics = ref.read(analyticsServiceProvider);
          await analytics.refreshRemoteConfig();
          await ref.read(subscriptionProvider.notifier).refresh();
        },
        child: widget.child,
      ),
      bottomNavigationBar: shouldShowBottomNav ? Container(
        height: 65,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
          ),
        ),
        child: BottomNavigationBar(
          type: BottomNavigationBarType.fixed,
          currentIndex: currentIndex,
          onTap: _onItemTapped,
          backgroundColor: Colors.transparent,
          elevation: 0,
          selectedItemColor: colorScheme.primary,
          unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
          selectedFontSize: 12,
          unselectedFontSize: 12,
          items: [
            BottomNavigationBarItem(
              icon: const Icon(Icons.dashboard_outlined),
              activeIcon: const Icon(Icons.dashboard),
              label: l10n.dashboard,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.directions_car_outlined),
              activeIcon: const Icon(Icons.directions_car),
              label: l10n.cars,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.smart_toy_outlined),
              activeIcon: const Icon(Icons.smart_toy),
              label: l10n.ai,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.settings_outlined),
              activeIcon: const Icon(Icons.settings),
              label: l10n.settings,
            ),
            BottomNavigationBarItem(
              icon: const Icon(Icons.person_outline),
              activeIcon: const Icon(Icons.person),
              label: l10n.profile,
            ),
          ],
        ),
      ) : null,
    );
  }

  bool _shouldShowBottomNavigation(String route) {
    // Routes that should show bottom navigation
    final routesWithBottomNav = [
      '/main',
      '/cars/add',
      '/oil-changes/add',
      '/maintenance/add',
      '/main/profile/change-password',
      '/main/profile/notifications',
      '/main/profile/language',
      '/main/profile/about',
      '/main/subscription',
    ];

    return routesWithBottomNav.contains(route) || route.startsWith('/main/');
  }
}
