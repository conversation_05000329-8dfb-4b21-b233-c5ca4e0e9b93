import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../voice_input/presentation/widgets/listening_bottom_sheet.dart';
import '../../../voice_input/services/voice_command_service.dart';
import '../../../voice_input/models/voice_form_type.dart';
import '../../../ai_chat/presentation/widgets/car_assistant_chat_sheet.dart';
import 'package:go_router/go_router.dart';
import '../../../../generated/app_localizations.dart';
import '../../../../core/theme/theme_extensions.dart';
import '../../../subscription/providers/feature_gate_provider.dart';
import '../../../subscription/providers/subscription_provider.dart';
import '../../../subscription/presentation/screens/subscription_screen.dart';

/// Root screen for AI interactions – provides a TabBar with Voice and Chat.
class AiRootScreen extends ConsumerStatefulWidget {
  const AiRootScreen({super.key});

  @override
  ConsumerState<AiRootScreen> createState() => _AiRootScreenState();
}

class _AiRootScreenState extends ConsumerState<AiRootScreen>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _showVoiceOptionDialog(VoiceFormType formType) {
    final locale = Localizations.localeOf(context);
    final langCode = locale.languageCode == 'ar' ? 'ar-EG' : 'en-US';

    final voiceService = ref.read(voiceCommandServiceProvider);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (ctx) => ListeningBottomSheet(
        formType: formType,
        onCancel: () async {
          await voiceService.cancelRecording();
          if (ctx.mounted) Navigator.pop(ctx);
        },
        onStopAndSend: () async {
          final result = await voiceService.stopAndProcess(
            VoiceContext(originScreen: formType, languageCode: langCode),
          );
          if (ctx.mounted) Navigator.pop(ctx);
          if (result.success && result.transcription != null) {
            String? carId = result.extractedData?['carId']?.toString();
            String targetRoute;
            switch (formType) {
              case VoiceFormType.oilChange:
                targetRoute = '/oil-changes/add';
                break;
              case VoiceFormType.maintenance:
                targetRoute = '/maintenance/add';
                break;
            }
            if (mounted) context.push(targetRoute, extra: result.extractedData);
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      result.error ?? S.of(context).couldNotUnderstandCommand),
                ),
              );
            }
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);

    // Watch subscription state to handle loading
    final subscriptionState = ref.watch(subscriptionProvider);

    // Check if user has access to AI features
    final hasAiChatAccess =
        ref.watch(featureGateProvider(PremiumFeature.aiChat));
    final hasVoiceAccess =
        ref.watch(featureGateProvider(PremiumFeature.voiceInput));

    // Show loading indicator during subscription check to prevent flashing
    if (subscriptionState.isLoading) {
      return _buildLoadingScreen(context, l10n);
    }

    // If user doesn't have access to either feature, show subscription promotion
    if (!hasAiChatAccess && !hasVoiceAccess) {
      return _buildSubscriptionPromotionScreen(context, l10n);
    }

    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        title: FadeTransition(
          opacity: _fadeAnimation,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.accentColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.smart_toy_rounded,
                  color: context.accentColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                l10n.aiAssistant,
                style: TextStyle(
                  color: context.accentColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                ),
              ),
            ],
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: context.accentColor,
          unselectedLabelColor: context.secondaryAccentColor,
          indicatorColor: context.accentColor,
          indicatorWeight: 3,
          indicatorSize: TabBarIndicatorSize.label,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 14,
          ),
          tabs: [
            Tab(
              icon: Icon(Icons.mic_rounded),
              text: l10n.voiceCommands,
            ),
            Tab(
              icon: Icon(Icons.chat_bubble_rounded),
              text: l10n.aiChat,
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Voice Tab
          hasVoiceAccess
              ? _buildVoiceTab(context, l10n)
              : _buildFeatureLockedTab(context, l10n, l10n.voiceCommands),
          // Chat Tab
          hasAiChatAccess
              ? const CarAssistantChatSheet()
              : _buildFeatureLockedTab(context, l10n, l10n.aiChat),
        ],
      ),
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
    Color? accentColor,
  }) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(16),
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: context.cardColor,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: (accentColor ?? context.accentColor).withOpacity(0.2),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: (accentColor ?? context.accentColor)
                            .withOpacity(0.12),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        icon,
                        color: accentColor ?? context.accentColor,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: TextStyle(
                              color: context.primaryTextColor,
                              fontSize: 18,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            description,
                            style: TextStyle(
                              color: context.secondaryTextColor,
                              fontSize: 14,
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.arrow_forward_ios_rounded,
                        color: context.accentColor,
                        size: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubscriptionPromotionScreen(BuildContext context, S l10n) {
    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.smart_toy_rounded,
                color: context.accentColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              l10n.aiAssistant,
              style: TextStyle(
                color: context.accentColor,
                fontWeight: FontWeight.bold,
                fontSize: 22,
              ),
            ),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.smart_toy_outlined,
                    size: 48,
                    color: context.accentColor,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  l10n.aiFeatures,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.aiFeaturesPremiumDescription,
                  style: TextStyle(
                    fontSize: 14,
                    color: context.secondaryTextColor,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                _buildFeatureList(context, l10n),
                const SizedBox(height: 24),
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    color: context.accentColor,
                    boxShadow: [
                      BoxShadow(
                        color: context.accentColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const SubscriptionScreen(),
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                    ),
                    child: Text(
                      l10n.upgradeToPremium,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVoiceTab(BuildContext context, S l10n) {
    return Container(
      color: context.containerBackgroundColor,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome section
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: context.accentColor.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.mic_rounded,
                        size: 36,
                        color: context.accentColor,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        l10n.voiceCommandsWelcome,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.primaryTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        l10n.voiceCommandsSubtitle,
                        style: TextStyle(
                          fontSize: 13,
                          color: context.secondaryTextColor,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  l10n.quickActions,
                  style: TextStyle(
                    color: context.accentColor,
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const SizedBox(height: 12),
                _buildActionCard(
                  context,
                  icon: Icons.oil_barrel_rounded,
                  title: l10n.addOilChange,
                  description: l10n.recordOilChangeVoice,
                  onTap: () => _showVoiceOptionDialog(VoiceFormType.oilChange),
                  accentColor: const Color(0xFFE65100), // Orange for oil
                ),
                _buildActionCard(
                  context,
                  icon: Icons.build_rounded,
                  title: l10n.addMaintenance,
                  description: l10n.recordMaintenanceVoice,
                  onTap: () =>
                      _showVoiceOptionDialog(VoiceFormType.maintenance),
                  accentColor: const Color(0xFF1976D2), // Blue for maintenance
                ),
                const SizedBox(height: 16),
                // Help section
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: context.cardColor,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: context.secondaryAccentColor.withOpacity(0.2),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.help_outline_rounded,
                            color: context.accentColor,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Text(
                            l10n.voiceCommandsHelp,
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: context.primaryTextColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        l10n.voiceCommandsHelpDescription,
                        style: TextStyle(
                          fontSize: 13,
                          color: context.secondaryTextColor,
                          height: 1.4,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureLockedTab(
      BuildContext context, S l10n, String featureName) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: context.accentColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Icon(
                    Icons.lock_outlined,
                    size: 64,
                    color: context.accentColor,
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  l10n.featureLocked(featureName),
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: context.primaryTextColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.featureRequiresPremium,
                  style: TextStyle(
                    fontSize: 16,
                    color: context.secondaryTextColor,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: context.accentColor,
                    boxShadow: [
                      BoxShadow(
                        color: context.accentColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: () {
                      // Add a small delay to prevent accidental taps during keyboard dismissal
                      Future.delayed(const Duration(milliseconds: 100), () {
                        if (mounted) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (_) => const SubscriptionScreen(),
                            ),
                          );
                        }
                      });
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      l10n.upgradeNow,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureList(BuildContext context, S l10n) {
    final features = [
      {'icon': Icons.mic_rounded, 'text': l10n.featureVoiceCommands},
      {'icon': Icons.chat_bubble_rounded, 'text': l10n.featureAiChat},
      {'icon': Icons.block_flipped, 'text': l10n.featureAdFree},
      {
        'icon': Icons.directions_car_rounded,
        'text': l10n.featureUnlimitedVehicles
      },
      {'icon': Icons.cloud_upload_rounded, 'text': l10n.featureCloudBackup},
    ];

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: context.accentColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: context.accentColor.withOpacity(0.15),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star_rounded,
                color: context.accentColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                l10n.premiumFeatures,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: context.accentColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          ...features.map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: context.accentColor.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        feature['icon'] as IconData,
                        size: 20,
                        color: context.accentColor,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        feature['text'] as String,
                        style: TextStyle(
                          fontSize: 15,
                          color: context.primaryTextColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Icon(
                      Icons.check_circle_rounded,
                      size: 20,
                      color: Colors.green.shade600,
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  /// Build loading screen during subscription state check
  Widget _buildLoadingScreen(BuildContext context, S l10n) {
    return Scaffold(
      backgroundColor: context.containerBackgroundColor,
      appBar: AppBar(
        backgroundColor: context.containerBackgroundColor,
        elevation: 0,
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.accentColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.smart_toy_rounded,
                color: context.accentColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              l10n.aiAssistant,
              style: TextStyle(
                color: context.accentColor,
                fontWeight: FontWeight.bold,
                fontSize: 22,
              ),
            ),
          ],
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: context.accentColor,
            ),
            const SizedBox(height: 24),
            Text(
              'Loading AI features...',
              style: TextStyle(
                color: context.secondaryTextColor,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
