import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../../generated/app_localizations.dart';
import '../../../dashboard/presentation/screens/dashboard_screen.dart';
import '../../../car_management/presentation/screens/car_list_screen.dart';
import '../../../settings/presentation/screens/settings_screen.dart';
import '../../../profile/presentation/screens/profile_screen.dart';
import '../widgets/ai_root_screen.dart';
import '../../../../core/services/analytics_service.dart';
import '../../../subscription/providers/subscription_provider.dart';

/// Provider holding the current bottom navigation index
final mainNavIndexProvider = StateProvider<int>((ref) => 0);

/// Root screen that contains a Material 3 [NavigationBar] with five primary
/// destinations and keeps an [IndexedStack] of the corresponding screens so
/// navigation state is preserved.
class MainNavigationScreen extends ConsumerStatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  ConsumerState<MainNavigationScreen> createState() =>
      _MainNavigationScreenState();
}

class _MainNavigationScreenState extends ConsumerState<MainNavigationScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    // Only refresh Remote Config on app start, don't update trial duration
    Future.microtask(() async {
      final analytics = ref.read(analyticsServiceProvider);
      await analytics.refreshRemoteConfig();
      // Removed trial duration update and subscription refresh to prevent trial resets
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    final current = ref.read(mainNavIndexProvider);
    if (current != index) {
      ref.read(mainNavIndexProvider.notifier).state = index;
      _controller.forward(from: 0.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = S.of(context);
    final colorScheme = Theme.of(context).colorScheme;

    final pages = [
      const DashboardScreen(),
      const CarListScreen(),
      const AiRootScreen(),
      const SettingsScreen(),
      const ProfileScreen(),
    ];

    final currentIndex = ref.watch(mainNavIndexProvider);

    return Scaffold(
      // Pull-to-refresh wrapper
      body: RefreshIndicator(
        onRefresh: () async {
          final analytics = ref.read(analyticsServiceProvider);
          await analytics.refreshRemoteConfig();
          // Removed trial duration update to prevent trial resets
          await ref.read(subscriptionProvider.notifier).refresh();
        },
        child: CustomScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          slivers: [
            SliverFillRemaining(
              hasScrollBody: false,
              child: IndexedStack(
                index: currentIndex,
                children: pages,
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 65,
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border(
            top: BorderSide(
              color: colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
          ),
        ),
        child: SafeArea(
          top: false,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(5, (index) {
              final isSelected = currentIndex == index;

              IconData getIcon(int index) {
                switch (index) {
                  case 0:
                    return isSelected
                        ? Icons.home_rounded
                        : Icons.home_outlined;
                  case 1:
                    return isSelected
                        ? Icons.directions_car_rounded
                        : Icons.directions_car_outlined;
                  case 2:
                    return isSelected
                        ? Icons.smart_toy_rounded
                        : Icons.smart_toy_outlined;
                  case 3:
                    return isSelected
                        ? Icons.settings_rounded
                        : Icons.settings_outlined;
                  case 4:
                    return isSelected
                        ? Icons.person_rounded
                        : Icons.person_outline_rounded;
                  default:
                    return Icons.circle;
                }
              }

              String getLabel(int index) {
                switch (index) {
                  case 0:
                    return l10n.dashboard;
                  case 1:
                    return l10n.myCars;
                  case 2:
                    return 'AI';
                  case 3:
                    return l10n.settings;
                  case 4:
                    return l10n.profile;
                  default:
                    return '';
                }
              }

              return Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () => _onItemTapped(index),
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    curve: Curves.easeInOut,
                    padding: const EdgeInsets.symmetric(vertical: 6),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AnimatedScale(
                          scale: isSelected ? 1.15 : 1.0,
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeInOut,
                          child: Icon(
                            getIcon(index),
                            color: isSelected
                                ? colorScheme.onSurface
                                : colorScheme.onSurface.withOpacity(0.64),
                            size: 24,
                          ),
                        ),
                        const SizedBox(height: 2),
                        AnimatedDefaultTextStyle(
                          duration: const Duration(milliseconds: 200),
                          curve: Curves.easeInOut,
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall!
                              .copyWith(
                                fontSize: 10,
                                height: 1.2,
                                letterSpacing: 0.2,
                                color: isSelected
                                    ? colorScheme.onSurface
                                    : colorScheme.onSurface.withOpacity(0.64),
                                fontWeight: isSelected
                                    ? FontWeight.w700
                                    : FontWeight.w400,
                              ),
                          child: Text(
                            getLabel(index),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}
