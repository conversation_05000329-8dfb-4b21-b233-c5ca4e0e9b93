import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../../core/providers/auth_providers.dart' as core_auth;
import '../../core/providers/auth_providers.dart';
import '../../core/services/auth_service.dart';
import '../../core/providers/onboarding_provider.dart';
import '../../features/auth/presentation/screens/login_screen.dart';
import '../../features/auth/presentation/screens/signup_screen.dart';
import '../../features/auth/presentation/screens/welcome_screen.dart';
import '../../features/auth/presentation/screens/forgot_password_screen.dart';
import '../../features/auth/presentation/screens/email_verification_screen.dart';
import '../../features/dashboard/presentation/screens/dashboard_screen.dart';
import '../../features/car_management/presentation/screens/add_car_screen.dart';
import '../../features/car_management/presentation/screens/car_details_screen.dart';
import '../../features/oil_change/presentation/screens/add_oil_change_screen.dart';
import '../../features/profile/presentation/screens/profile_screen.dart';
import '../../features/oil_change/presentation/screens/oil_change_history_screen.dart';
import '../../features/oil_change/presentation/screens/oil_change_details_screen.dart';
import '../../features/maintenance/presentation/screens/add_maintenance_screen.dart';
import '../../features/settings/presentation/screens/settings_screen.dart';
import '../../features/settings/presentation/screens/privacy_policy_screen.dart';
import '../../features/settings/presentation/screens/terms_conditions_screen.dart';
import '../../features/maintenance/presentation/screens/maintenance_history_list.dart';
import '../../features/maintenance/presentation/screens/maintenance_details_screen.dart';
import '../../features/maintenance/presentation/screens/edit_maintenance_screen.dart';
import '../../features/maintenance/presentation/screens/photo_view_screen.dart';
import '../../core/theme/theme_extensions.dart';
import '../../features/profile/presentation/screens/notification_settings_screen.dart';
import '../../features/profile/presentation/screens/change_password_screen.dart';
import '../../features/profile/presentation/screens/language_settings_screen.dart';
import '../../features/profile/presentation/screens/about_screen.dart';
import '../../features/onboarding/presentation/screens/onboarding_screen.dart';
import '../../features/car_management/presentation/screens/edit_car_screen.dart';
import '../../features/car_management/presentation/screens/car_list_screen.dart';
import 'dart:developer' as dev;
import '../../core/providers/locale_provider.dart';
import '../../generated/app_localizations.dart';
import '../../features/ads/presentation/observers/ad_route_observer.dart';
import '../../features/subscription/presentation/screens/subscription_screen.dart';
import '../../features/subscription/presentation/screens/welcome_subscription_screen.dart';
import '../../features/settings/presentation/screens/subscription_policy_screen.dart';
import '../../features/settings/presentation/screens/refund_policy_screen.dart';
import '../../features/main_navigation/presentation/screens/main_navigation_screen.dart';

/// Provider for the AdRouteObserver
final adRouteObserverProvider = Provider<AdRouteObserver>((ref) {
  return AdRouteObserver(ref);
});

// Create a provider for the router that can access auth state
final routerProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authStateProvider);
  final authProcess = ref.watch(authProcessProvider);
  final onboardingCompleted = ref.watch(onboardingCompletedProvider);
  // Watch sharedPreferencesProvider to establish dependency
  ref.watch(sharedPreferencesProvider);
  // Watch shouldShowWelcomeSubscriptionProvider to ensure router rebuilds when it changes
  ref.watch(core_auth.shouldShowWelcomeSubscriptionProvider);
  final analytics = FirebaseAnalytics.instance;

  // Get the ad route observer
  final adObserver = ref.watch(adRouteObserverProvider);

  return GoRouter(
    initialLocation: '/',
    observers: [
      FirebaseAnalyticsObserver(analytics: analytics),
      adObserver, // Add the ad observer
    ],
    redirect: (context, state) {
      final isOnAuthScreen = state.matchedLocation == '/' ||
          state.matchedLocation == '/login' ||
          state.matchedLocation == '/signup';
      final isOnVerificationScreen =
          state.matchedLocation == '/email-verification';

      // Log the current navigation state for debugging
      dev.log('Router: Path=${state.matchedLocation}, isAuth=$isOnAuthScreen, '
          'authProcess=$authProcess, authState=$authState');

      // If we're in the process of auth, don't redirect
      if (authProcess != AuthProcess.idle) {
        dev.log(
            'Router: Auth process in progress, skipping redirections for path ${state.matchedLocation}');
        return null;
      }

      // If onboarding has not been completed, redirect to onboarding
      if (!onboardingCompleted && state.matchedLocation != '/onboarding') {
        return '/onboarding';
      }

      // If the user is logged in and tries to access auth screens, redirect to main navigation
      if (authState == AuthState.authenticated) {
        // Check if the user's email is verified
        final user = ref.read(authServiceProvider).currentUser;
        final isGoogleSignIn = user?.providerData.any((p) =>
                p.providerId == 'google.com' || p.providerId == 'google.com') ??
            false;

        // Skip email verification for Google sign-in users
        if (user != null &&
            !user.emailVerified &&
            !isOnVerificationScreen &&
            !isGoogleSignIn) {
          dev.log(
              'Router: User email not verified, redirecting to verification: ${user.email}');
          return '/email-verification';
        }

        // Check if user should see welcome subscription screen
        final shouldShowWelcome =
            ref.watch(core_auth.shouldShowWelcomeSubscriptionProvider);
        final isOnWelcomeSubscription =
            state.matchedLocation == '/welcome-subscription';

        if (shouldShowWelcome && !isOnWelcomeSubscription) {
          dev.log('Router: Redirecting user to welcome subscription screen');
          return '/welcome-subscription';
        }

        if (isOnAuthScreen && !shouldShowWelcome) {
          dev.log(
              'Router: User is authenticated, redirecting from auth screen to main navigation');
          return '/main';
        }

        // Ensure screens without bottom nav redirect to main navigation
        final screensToRedirect = [
          '/profile/change-password',
          '/profile/notifications',
          '/profile/language',
          '/profile/about',
          '/subscription',
          '/cars',
          '/cars/add',
          '/oil-changes/add',
          '/maintenance/add',
          '/settings'
        ];

        if (screensToRedirect.contains(state.matchedLocation)) {
          dev.log(
              'Router: Redirecting from screen ${state.matchedLocation} to main navigation');
          return '/main';
        }
      }

      // If the user is not logged in and tries to access protected screens, redirect to welcome
      if (authState == AuthState.unauthenticated) {
        final protectedPaths = [
          '/dashboard',
          '/cars',
          '/profile',
          '/settings',
          '/maintenance',
          '/oil-changes'
        ];
        if (protectedPaths
            .any((path) => state.matchedLocation.startsWith(path))) {
          dev.log(
              'Router: User is NOT authenticated, redirecting from protected area to welcome');
          return '/';
        }
      }

      return null;
    },
    routes: [
      GoRoute(
        path: '/',
        builder: (context, state) => const WelcomeScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      GoRoute(
        path: '/login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/email-verification',
        builder: (context, state) => const EmailVerificationScreen(),
      ),
      GoRoute(
        path: '/main',
        builder: (context, state) => const MainNavigationScreen(),
        routes: [
          // Secondary profile routes
          GoRoute(
            path: 'profile/change-password',
            builder: (context, state) => const ChangePasswordScreen(),
          ),
          GoRoute(
            path: 'profile/notifications',
            builder: (context, state) => const NotificationSettingsScreen(),
          ),
          GoRoute(
            path: 'profile/language',
            builder: (context, state) => const LanguageSettingsScreen(),
          ),
          GoRoute(
            path: 'profile/about',
            builder: (context, state) => const AboutScreen(),
          ),
          GoRoute(
            path: 'subscription',
            builder: (context, state) => const SubscriptionScreen(),
          ),
          // Primary screens that should maintain bottom navigation
          GoRoute(
            path: 'cars/add',
            builder: (context, state) => const AddCarScreen(),
          ),
          GoRoute(
            path: 'oil-changes/add',
            builder: (context, state) => AddOilChangeScreen(
              voiceData: state.extra as Map<String, dynamic>?,
            ),
          ),
          GoRoute(
            path: 'maintenance/add',
            builder: (context, state) => AddMaintenanceScreen(
              voiceData: state.extra as Map<String, dynamic>?,
            ),
          ),
        ],
      ),
      GoRoute(
        path: '/dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      GoRoute(
        path: '/cars',
        builder: (context, state) => const CarListScreen(),
      ),
      GoRoute(
        path: '/cars/add',
        builder: (context, state) => const AddCarScreen(),
      ),
      GoRoute(
        path: '/cars/:id',
        builder: (context, state) => CarDetailsScreen(
          carId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/cars/:id/edit',
        builder: (context, state) => EditCarScreen(
          carId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/cars/:id/oil-changes',
        builder: (context, state) => OilChangeHistoryScreen(
          carId: state.pathParameters['id']!,
        ),
      ),
      GoRoute(
        path: '/cars/:id/oil-changes/add',
        builder: (context, state) => AddOilChangeScreen(
          carId: state.pathParameters['id']!,
          voiceData: state.extra as Map<String, dynamic>?,
        ),
      ),
      GoRoute(
        path: '/cars/:id/oil-changes/:oilChangeId',
        builder: (context, state) => OilChangeDetailsScreen(
          carId: state.pathParameters['id']!,
          oilChangeId: state.pathParameters['oilChangeId']!,
        ),
      ),
      GoRoute(
        path: '/oil-changes/add',
        builder: (context, state) => AddOilChangeScreen(
          voiceData: state.extra as Map<String, dynamic>?,
        ),
      ),
      GoRoute(
        path: '/cars/:id/maintenance',
        builder: (context, state) {
          final carId = state.pathParameters['id']!;
          return Scaffold(
            appBar: AppBar(
              title: Text(
                S.of(context).maintenanceHistory,
                style: TextStyle(
                  color: context.accentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              backgroundColor: context.containerBackgroundColor,
              leading: BackButton(
                color: context.accentColor,
                onPressed: () {
                  // Navigate back to car details screen
                  context.go('/cars/$carId');
                },
              ),
            ),
            body: MaintenanceHistoryList(carId: carId),
          );
        },
        routes: [
          GoRoute(
            path: 'add',
            builder: (context, state) {
              final carId = state.pathParameters['id']!;
              return AddMaintenanceScreen(
                carId: carId,
                voiceData: state.extra as Map<String, dynamic>?,
              );
            },
          ),
          GoRoute(
            path: ':maintenanceId',
            builder: (context, state) {
              final carId = state.pathParameters['id']!;
              final maintenanceId = state.pathParameters['maintenanceId']!;
              return MaintenanceDetailsScreen(
                carId: carId,
                maintenanceId: maintenanceId,
              );
            },
            routes: [
              GoRoute(
                path: 'edit',
                builder: (context, state) {
                  final carId = state.pathParameters['id']!;
                  final maintenanceId = state.pathParameters['maintenanceId']!;
                  return EditMaintenanceScreen(
                    carId: carId,
                    maintenanceId: maintenanceId,
                  );
                },
              ),
            ],
          ),
        ],
      ),

      GoRoute(
        path: '/settings',
        builder: (context, state) => const SettingsScreen(),
      ),
      GoRoute(
        path: '/privacy-policy',
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),
      GoRoute(
        path: '/terms-conditions',
        builder: (context, state) => const TermsConditionsScreen(),
      ),
      GoRoute(
        path: '/subscription-policy',
        builder: (context, state) => const SubscriptionPolicyScreen(),
      ),
      GoRoute(
        path: '/photo-view',
        builder: (context, state) {
          final photoUrl = state.extra as String;
          return PhotoViewScreen(photoUrl: photoUrl);
        },
      ),
      GoRoute(
        path: '/maintenance/add',
        builder: (context, state) => AddMaintenanceScreen(
          voiceData: state.extra as Map<String, dynamic>?,
        ),
      ),

      GoRoute(
        path: '/welcome-subscription',
        builder: (context, state) => const WelcomeSubscriptionScreen(),
      ),
      GoRoute(
        path: '/refund-policy',
        builder: (context, state) => const RefundPolicyScreen(),
      ),
    ],
  );
}, dependencies: [
  authStateProvider,
  authProcessProvider,
  onboardingCompletedProvider,
  sharedPreferencesProvider,
  core_auth.shouldShowWelcomeSubscriptionProvider,
]);
