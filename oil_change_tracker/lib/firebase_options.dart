import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'MacOS platform is not supported for this app.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'Windows platform is not supported for this app.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'Linux platform is not supported for this app.',
        );
      default:
        throw UnsupportedError(
          'Unknown platform $defaultTargetPlatform',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyB-8g3o13i2zctPuq6jmsTGjkpDJDgE_Vk',
    appId: '1:952433993522:web:8663ed9879c1ecca26d92b',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
    authDomain: 'oiltracker-73d6c.firebaseapp.com',
    measurementId: 'G-HHRP184LPC',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD0FWVWgn2X2y-d6B7JCTI5to7X-Q8wCxU',
    appId: '1:952433993522:android:ca6320b3da134fe226d92b',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
    androidClientId:
        '952433993522-n20kinijtgv48qjlsuvffs3h4orgaidl.apps.googleusercontent.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyB-2uVnELUQsUKtQs7FLGj1aGFzHqvGO5g',
    appId: '1:952433993522:ios:fd8762b3c6a0c41c26d92b',
    messagingSenderId: '952433993522',
    projectId: 'oiltracker-73d6c',
    storageBucket: 'oiltracker-73d6c.firebasestorage.app',
    iosClientId:
        '952433993522-8lo4dg56ch0mj9iuv8c7thj2g3dr2k8a.apps.googleusercontent.com',
    iosBundleId: 'com.maximummdeia.oil-change-tracker',
  );
}
