import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:developer' as dev;
import 'package:oil_change_tracker/generated/app_localizations.dart';

/// Handles permission requests for the app
class AppPermissionHandler {
  /// Request camera permission with better error handling
  static Future<bool> requestCameraPermission(BuildContext context) async {
    try {
      dev.log('AppPermissionHandler: Requesting camera permission');
      PermissionStatus status = await Permission.camera.status;

      // Log current status
      dev.log(
          'AppPermissionHandler: Current camera permission status: $status');

      if (status.isDenied) {
        dev.log(
            'AppPermissionHandler: Camera permission is denied, requesting...');
        status = await Permission.camera.request();
        dev.log(
            'AppPermissionHandler: Camera permission request result: $status');
      }

      // Handle permanently denied case
      if (status.isPermanentlyDenied) {
        dev.log(
            'AppPermissionHandler: Camera permission is permanently denied');
        _showPermissionDialog(
          context,
          'Camera Permission Required',
          'To take photos, please enable camera permission in your device settings.',
        );
        return false;
      }

      return status.isGranted;
    } catch (e) {
      dev.log('AppPermissionHandler: Error requesting camera permission: $e');
      return false;
    }
  }

  /// Request storage permissions with better handling for different Android versions
  static Future<bool> requestStoragePermission(BuildContext context) async {
    try {
      dev.log('AppPermissionHandler: Requesting storage permission');

      // For Android 13+, use the specific photo picker permission
      if (await Permission.photos.request().isGranted) {
        dev.log('AppPermissionHandler: Photos permission granted');
        return true;
      }

      // For older Android versions, fall back to storage permissions
      PermissionStatus status = await Permission.storage.status;
      dev.log(
          'AppPermissionHandler: Current storage permission status: $status');

      if (status.isDenied) {
        dev.log(
            'AppPermissionHandler: Storage permission is denied, requesting...');
        status = await Permission.storage.request();
        dev.log(
            'AppPermissionHandler: Storage permission request result: $status');
      }

      // Handle permanently denied case
      if (status.isPermanentlyDenied) {
        dev.log(
            'AppPermissionHandler: Storage permission is permanently denied');
        _showPermissionDialog(
          context,
          'Storage Permission Required',
          'To access photos, please enable storage access in your device settings.',
        );
        return false;
      }

      return status.isGranted;
    } catch (e) {
      dev.log('AppPermissionHandler: Error requesting storage permission: $e');
      return false;
    }
  }

  /// Request both camera and storage permissions at once
  static Future<bool> requestCameraAndStoragePermissions(
      BuildContext context) async {
    try {
      dev.log(
          'AppPermissionHandler: Requesting both camera and storage permissions');

      // iOS only needs camera permission for capturing a photo using image_picker.
      // Requesting Photo/Storage permission on iOS confuses users because the
      // Settings screen will then only show "Photos" (no Camera toggle), even
      // though we never actually need library write access. Therefore we only
      // request camera permission on iOS.

      if (Platform.isIOS) {
        final granted = await requestCameraPermission(context);
        dev.log(
            'AppPermissionHandler: Camera permission on iOS granted: $granted');
        return granted;
      }

      // On Android we still need both permissions (camera & external storage).
      final cameraGranted = await requestCameraPermission(context);
      if (!cameraGranted) {
        dev.log('AppPermissionHandler: Failed to get camera permission');
        return false;
      }

      final storageGranted = await requestStoragePermission(context);
      if (!storageGranted) {
        dev.log('AppPermissionHandler: Failed to get storage permission');
        return false;
      }

      dev.log(
          'AppPermissionHandler: Both camera and storage permissions granted');
      return true;
    } catch (e) {
      dev.log(
          'AppPermissionHandler: Error requesting camera and storage permissions: $e');
      return false;
    }
  }

  /// Show a dialog explaining why a permission is needed and how to grant it
  static void _showPermissionDialog(
    BuildContext context,
    String title,
    String content,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(S.of(context).cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: Text('Open Settings'),
          ),
        ],
      ),
    );
  }

  /// Check if the device is running Android 13 or above
  static Future<bool> _isAndroid13OrAbove() async {
    if (Platform.isAndroid) {
      final deviceInfo = DeviceInfoPlugin();
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.version.sdkInt >= 33; // API level 33 is Android 13
    }
    return false;
  }
}
