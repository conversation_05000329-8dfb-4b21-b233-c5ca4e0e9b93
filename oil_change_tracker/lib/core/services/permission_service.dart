import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../utils/logger.dart';
import 'package:oil_change_tracker/generated/app_localizations.dart';

part 'permission_service.g.dart';

/// Permission service to handle runtime permissions in a centralized way
@riverpod
class PermissionService extends _$PermissionService {
  @override
  Future<void> build() async {
    // Initial build does nothing
    return;
  }

  /// Request camera permission with proper handling
  Future<bool> requestCameraPermission(BuildContext context) async {
    return _requestPermission(
      permission: Permission.camera,
      context: context,
      permissionName: 'Camera',
      rationaleMessage:
          'We need camera access to let you take photos of your vehicles',
    );
  }

  /// Request location permission with proper handling
  Future<bool> requestLocationPermission(BuildContext context) async {
    return _requestPermission(
      permission: Permission.locationWhenInUse,
      context: context,
      permissionName: 'Location',
      rationaleMessage:
          'We need location access to show you local weather information',
    );
  }

  /// Request notification permission with proper handling
  Future<bool> requestNotificationPermission(BuildContext context) async {
    return _requestPermission(
      permission: Permission.notification,
      context: context,
      permissionName: 'Notification',
      rationaleMessage:
          'We need permission to send you important reminders about your vehicle maintenance',
      showRationaleFirst: true,
    );
  }

  /// Request storage permission with proper handling
  Future<bool> requestStoragePermission(BuildContext context) async {
    bool result = false;

    // Choose the appropriate permissions based on Android version
    if (await Permission.photos.isRestricted) {
      // Android 13+ uses granular permissions
      result = await _requestPermission(
        permission: Permission.photos,
        context: context,
        permissionName: 'Photo Library',
        rationaleMessage:
            'We need access to your photos to let you upload car images',
        showRationaleFirst: true,
      );
    } else {
      // Older Android versions use storage permission
      result = await _requestPermission(
        permission: Permission.storage,
        context: context,
        permissionName: 'Storage',
        rationaleMessage: 'We need storage access to let you upload car images',
        showRationaleFirst: true,
      );
    }

    return result;
  }

  /// Internal method to request permission with consistent UI feedback
  Future<bool> _requestPermission({
    required Permission permission,
    required BuildContext context,
    required String permissionName,
    required String rationaleMessage,
    bool showRationaleFirst = false,
  }) async {
    // Check current status
    var status = await permission.status;

    // If already granted, return true
    if (status.isGranted) {
      return true;
    }

    // If denied before but can request again
    if (status.isDenied) {
      // Show pre-permission rationale dialog if needed
      if (showRationaleFirst) {
        final bool shouldProceed = await _showRationaleDialog(
          context: context,
          permissionName: permissionName,
          message: rationaleMessage,
        );

        if (!shouldProceed) {
          // User declined at our custom dialog
          return false;
        }
      }

      // Request the permission
      status = await permission.request();
      AppLogger.info('Permission $permissionName request result: $status');
      return status.isGranted;
    }

    // If permanently denied, guide user to settings
    if (status.isPermanentlyDenied) {
      await _showSettingsDialog(
        context: context,
        permissionName: permissionName,
      );
      return false;
    }

    // Limited or restricted cases
    if (status.isLimited || status.isRestricted) {
      AppLogger.warning(
          'Permission $permissionName is limited or restricted: $status');
      return true; // Limited access is still usable
    }

    // Fallback case
    AppLogger.info('Unhandled permission status for $permissionName: $status');
    return false;
  }

  /// Show rationale dialog before requesting permission
  Future<bool> _showRationaleDialog({
    required BuildContext context,
    required String permissionName,
    required String message,
  }) async {
    if (!context.mounted) return false;

    try {
      return await showDialog<bool>(
            context: context,
            barrierDismissible: false,
            builder: (BuildContext context) {
              return AlertDialog(
                title: Text('$permissionName Access Needed'),
                content: Text(message),
                actions: <Widget>[
                  TextButton(
                    child: Text(S.of(context).skip),
                    onPressed: () => Navigator.of(context).pop(false),
                  ),
                  TextButton(
                    child: Text(S.of(context).continueAction),
                    onPressed: () => Navigator.of(context).pop(true),
                  ),
                ],
              );
            },
          ) ??
          false;
    } catch (e) {
      AppLogger.error('Error showing rationale dialog', e);
      return false;
    }
  }

  /// Show settings dialog when permission is permanently denied
  Future<void> _showSettingsDialog({
    required BuildContext context,
    required String permissionName,
  }) async {
    if (!context.mounted) return;

    try {
      await showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('$permissionName Permission Required'),
            content: Text(
                'The $permissionName permission is required for this feature. '
                'Please open settings and grant permission.'),
            actions: <Widget>[
              TextButton(
                child: Text(S.of(context).cancel),
                onPressed: () => Navigator.of(context).pop(),
              ),
              TextButton(
                child: Text(S.of(context).settings ?? 'Settings'),
                onPressed: () {
                  Navigator.of(context).pop();
                  openAppSettings();
                },
              ),
            ],
          );
        },
      );
    } catch (e) {
      AppLogger.error('Error showing settings dialog', e);
    }
  }
}
