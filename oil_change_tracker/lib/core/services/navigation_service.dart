import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'dart:developer' as dev;

/// Service to handle navigation state and ensure proper bottom navigation preservation
class NavigationService extends ChangeNotifier with WidgetsBindingObserver {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  BuildContext? _context;
  bool _isInitialized = false;

  /// Initialize the navigation service with the app context
  void initialize(BuildContext context) {
    if (!_isInitialized) {
      _context = context;
      WidgetsBinding.instance.addObserver(this);
      _isInitialized = true;
      dev.log('NavigationService: Initialized');
    }
  }

  /// Dispose the navigation service
  void dispose() {
    if (_isInitialized) {
      WidgetsBinding.instance.removeObserver(this);
      _isInitialized = false;
      _context = null;
      dev.log('NavigationService: Disposed');
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    if (state == AppLifecycleState.resumed && _context != null) {
      _handleAppResume();
    }
  }

  /// Handle app resume and ensure proper navigation state
  void _handleAppResume() {
    if (_context == null) return;

    try {
      final currentLocation = GoRouterState.of(_context!).matchedLocation;
      dev.log('NavigationService: App resumed, current location: $currentLocation');

      // List of secondary screens that should redirect to main navigation
      final secondaryScreens = [
        '/profile/change-password',
        '/profile/notifications', 
        '/profile/language',
        '/profile/about',
        '/subscription',
        '/edit-profile',
        '/privacy-policy',
        '/terms-conditions',
        '/subscription-policy',
        '/refund-policy'
      ];

      // Check if we're on a secondary screen without bottom navigation
      if (secondaryScreens.contains(currentLocation)) {
        dev.log('NavigationService: Detected secondary screen, redirecting to main navigation');
        
        // Use post frame callback to ensure the navigation happens after the current frame
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_context != null && _context!.mounted) {
            _context!.go('/main');
          }
        });
      }
    } catch (e) {
      dev.log('NavigationService: Error handling app resume: $e');
    }
  }

  /// Navigate to a secondary screen while preserving main navigation context
  void navigateToSecondaryScreen(BuildContext context, String route) {
    try {
      // For secondary screens, use the nested routes under main
      final nestedRoutes = {
        '/profile/change-password': '/main/profile/change-password',
        '/profile/notifications': '/main/profile/notifications',
        '/profile/language': '/main/profile/language',
        '/profile/about': '/main/profile/about',
        '/subscription': '/main/subscription',
      };

      final targetRoute = nestedRoutes[route] ?? route;
      context.push(targetRoute);
      
      dev.log('NavigationService: Navigated to secondary screen: $targetRoute');
    } catch (e) {
      dev.log('NavigationService: Error navigating to secondary screen: $e');
      // Fallback to original route
      context.push(route);
    }
  }

  /// Check if the current route is a secondary screen
  bool isSecondaryScreen(String route) {
    final secondaryScreens = [
      '/profile/change-password',
      '/profile/notifications', 
      '/profile/language',
      '/profile/about',
      '/subscription',
      '/edit-profile',
      '/privacy-policy',
      '/terms-conditions',
      '/subscription-policy',
      '/refund-policy'
    ];
    
    return secondaryScreens.contains(route);
  }
}

/// Provider for the navigation service
final navigationServiceProvider = Provider<NavigationService>((ref) {
  return NavigationService();
});
