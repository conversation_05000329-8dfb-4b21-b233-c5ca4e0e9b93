import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/auth_service.dart';
import 'dart:developer' as dev;
import '../services/storage_service.dart';
import '../../features/subscription/providers/subscription_provider.dart';

/// Provider for the AuthService instance
final authServiceProvider = Provider<AuthService>((ref) {
  final service = AuthService(container: ref.container);
  ref.onDispose(() => service.dispose());
  return service;
});

/// Provider for authentication state
final authStateProvider =
    StateNotifierProvider<AuthStateNotifier, AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthStateNotifier(authService);
});

/// Provider for authentication process
final authProcessProvider =
    StateNotifierProvider<AuthProcessNotifier, AuthProcess>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthProcessNotifier(authService);
});

/// State notifier for auth state
class AuthStateNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthStateNotifier(this._authService) : super(_authService.currentState) {
    // Listen for auth state changes from Firebase
    FirebaseAuth.instance.authStateChanges().listen((_) {
      // Update our state with the current state from the AuthService
      state = _authService.currentState;
      dev.log('AuthStateNotifier: State updated to $state');
    });
  }
}

/// State notifier for auth process
class AuthProcessNotifier extends StateNotifier<AuthProcess> {
  final AuthService _authService;

  AuthProcessNotifier(this._authService) : super(_authService.currentProcess) {
    // We don't have a direct stream for auth process,
    // so we rely on periodic checks or updates through the auth functions
  }

  // Update state after an auth operation
  void updateProcess() {
    state = _authService.currentProcess;
    dev.log('AuthProcessNotifier: Process updated to $state');
  }
}

/// Provider for current Firebase user
final currentUserProvider = Provider<User?>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.currentUser;
});

/// Provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState == AuthState.authenticated;
});

/// Provider that indicates whether an auth process is in progress
final isAuthInProgressProvider = Provider<bool>((ref) {
  final authProcess = ref.watch(authProcessProvider);
  return authProcess != AuthProcess.idle;
});

/// Provider to track if user just signed up and should see welcome subscription
final isNewSignupProvider = StateProvider<bool>((ref) => false);

/// Notifier for tracking welcome subscription shown state
class WelcomeSubscriptionShownNotifier extends StateNotifier<bool> {
  final StorageService _storage;

  WelcomeSubscriptionShownNotifier(this._storage)
      : super(_storage.getBool('welcome_subscription_shown_v1'));

  Future<void> markAsShown() async {
    await _storage.setBool('welcome_subscription_shown_v1', true);
    state = true;
  }
}

/// Provider to track whether welcome subscription has been shown before
final welcomeSubscriptionShownProvider =
    StateNotifierProvider<WelcomeSubscriptionShownNotifier, bool>((ref) {
  final storage = ref.watch(storageServiceProvider);
  return WelcomeSubscriptionShownNotifier(storage);
}, dependencies: [
  storageServiceProvider,
]);

/// Provider to check if user should see welcome subscription screen
final shouldShowWelcomeSubscriptionProvider = Provider<bool>((ref) {
  final isNewSignup = ref.watch(isNewSignupProvider);
  final authState = ref.watch(authStateProvider);
  final hasShown = ref.watch(welcomeSubscriptionShownProvider);

  // Check if user already has active subscription or trial
  final subscriptionState = ref.watch(subscriptionProvider);

  // If we're still checking subscription state, don't decide yet
  if (subscriptionState.isLoading) {
    return false;
  }

  final hasSubscription = subscriptionState.hasActiveSubscription ||
      (subscriptionState.subscription?.isInTrialPeriod() ?? false);

  // Show welcome subscription if:
  // 1. User just signed up, or hasn't seen it before
  // 2. User is authenticated
  // 3. User does NOT have an active subscription/trial
  final shouldShow = authState == AuthState.authenticated &&
      !hasSubscription &&
      (isNewSignup || !hasShown);

  dev.log('shouldShowWelcomeSubscriptionProvider: '
      'authState=$authState, '
      'hasSubscription=$hasSubscription, '
      'subscriptionLoading=${subscriptionState.isLoading}, '
      'subscriptionError=${subscriptionState.error}, '
      'subscription=${subscriptionState.subscription?.toJson()}, '
      'isNewSignup=$isNewSignup, '
      'hasShown=$hasShown, '
      'shouldShow=$shouldShow');

  return shouldShow;
}, dependencies: [
  subscriptionProvider,
  isNewSignupProvider,
  authStateProvider,
  welcomeSubscriptionShownProvider,
  storageServiceProvider,
]);
