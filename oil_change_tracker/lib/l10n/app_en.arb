{"appName": "Oil Plus", "welcomeMessage": "Track your vehicle's oil changes and maintenance easily", "login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "emailRequired": "Email is required", "invalidEmail": "Please enter a valid email", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 6 characters", "passwordsDoNotMatch": "Passwords do not match", "nameRequired": "Name is required", "signInWithGoogle": "Sign in with Google", "noAccountSignUp": "Don't have an account? Sign up now", "haveAccountLogin": "Already have an account? Log in", "signUpSuccess": "Account created successfully! Please log in.", "dashboard": "Dashboard", "error": "Error", "ok": "OK", "cancel": "Cancel", "yes": "Yes", "no": "No", "loading": "Loading...", "confirmPasswordRequired": "Please confirm your password", "alreadyHaveAccount": "Already have an account? Log in", "welcomeBack": "Welcome to your dashboard", "oilConsumption": "Oil Consumption", "used": "Used", "kmUntilNextOilChange": "{km} km until next oil change", "@kmUntilNextOilChange": {"placeholders": {"km": {"type": "Object"}}}, "oilChangeOverdue": "Oil Change Overdue!", "recordOilChange": "Record Oil Change", "nextOilChange": "Next Oil Change", "@nextOilChange": {"placeholders": {"mileage": {"type": "String"}}}, "overdue": "Overdue", "close": "Close", "profile": "Profile", "settings": "Settings", "language": "Language", "notifications": "Notifications", "reminders": "Reminders", "oilChangeReminders": "Oil Change Reminders", "oilChangeRemindersDescription": "Receive notifications when your car needs an oil change", "maintenanceReminders": "Maintenance Reminders", "maintenanceRemindersDescription": "Receive notifications about scheduled maintenance", "mileageReminders": "Mileage Reminders", "mileageRemindersDescription": "Receive notifications about mileage updates", "notificationSettings": "Manage notification preferences", "about": "About", "aboutApp": "About Oil Plus", "aboutDescription": "Oil Plus helps you track your vehicle's maintenance schedule and oil changes. Keep your vehicle healthy with timely reminders and a detailed service record.", "personalInformation": "Personal Information", "security": "Security", "appSettings": "App Settings", "editProfile": "Edit Profile", "changePassword": "Change Password", "signOut": "Sign Out", "signOutConfirmation": "Are you sure you want to sign out?", "unknownUser": "Unknown User", "comingSoon": "Coming Soon!", "recordMaintenance": "Record Maintenance", "selectCar": "Select Car", "maintenanceType": "Maintenance Type", "currentMileage": "Current Mileage", "date": "Date", "cost": "Cost", "serviceProvider": "Service Provider", "notes": "Notes", "optional": "Optional", "pleaseSelectCar": "Please select a car", "pleaseEnterMileage": "Please enter current mileage", "pleaseEnterCost": "Please enter cost", "saveMaintenance": "Save Maintenance", "maintenanceAddedSuccess": "Maintenance record added successfully", "noCarsFound": "No cars found", "addCarFirst": "Add a car first to record maintenance", "addCar": "Add Car", "errorLoadingCars": "Error loading cars", "retry": "Retry", "generalService": "General Service", "brakeService": "Brake Service", "engineService": "Engine Service", "transmissionService": "Transmission Service", "tireService": "Tire Service", "batteryService": "Battery Service", "airConditioning": "Air Conditioning", "electricalSystem": "Electrical System", "suspension": "Suspension", "exhaustSystem": "Exhaust System", "fuelSystem": "Fuel System", "coolingSystem": "Cooling System", "regularMaintenance": "Regular Maintenance", "other": "Other", "myCars": "My Cars", "noCarsAddedYet": "No cars added yet", "addFirstCarMessage": "Add your first car to start tracking oil changes", "totalCars": "Total Cars", "needOilChange": "Need Oil Change", "carsDueForOilChange": "Cars due for oil change", "viewAllCars": "View all cars", "noUpcomingMaintenance": "No upcoming maintenance", "allCarsUpToDate": "All cars are up to date with their maintenance schedule", "nextOilChangeInDays": "Next oil change in {days} days", "overdueDays": "Overdue by {days} days", "daysUntilNextChange": "{days} days until next change", "viewAll": "View All", "goodMorning": "Good Morning", "goodAfternoon": "Good Afternoon", "goodEvening": "Good Evening", "subscriptions": "Subscriptions", "activeSubscription": "Active Subscription", "billingPeriod": "Billing Period", "monthly": "Monthly", "yearly": "Yearly", "save30Percent": "SAVE 30%", "perMonth": "per month", "perYear": "per year", "subscribe": "Subscribe", "popular": "POPULAR", "restorePurchases": "<PERSON><PERSON> Purchases", "cancelSubscription": "Cancel Subscription", "cancelSubscriptionTitle": "Cancel Subscription?", "cancelSubscriptionMessage": "Your subscription will remain active until the end of the current billing period.", "addNewCar": "Add New Car", "enterCarDetails": "Please fill in your car details", "saveSuccess": "Saved Successfully", "pleaseSignIn": "Please sign in to view the dashboard", "quickActions": "Quick Actions", "addMaintenance": "Add Maintenance", "update": "Update", "updateMileage": "Update Mileage", "mileageUpdated": "Mileage updated successfully", "invalidMileage": "Invalid mileage value", "mileageRequired": "Mileage is required", "mileageMustBeNumber": "Mileage must be a number", "mileageMustBeGreaterThan": "Mileage must be greater than {currentMileage}", "@mileageMustBeGreaterThan": {"placeholders": {"currentMileage": {"type": "String"}}}, "mileageTooHigh": "Mileage seems too high, please check your input", "lastOilChange": "Last oil change at", "kilometersElapsed": "Kilometers elapsed", "appTitle": "Oil Plus", "welcomeText": "Welcome to Oil Plus", "signIn": "Sign In", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "sendResetLink": "Send Reset Link", "noAccount": "Don't have an account?", "haveAccount": "Already have an account?", "cars": "Cars", "logout": "Logout", "darkMode": "Dark Mode", "lightMode": "Light Mode", "darkTheme": "Dark Theme", "lightTheme": "Light Theme", "themeSettings": "Theme Settings", "systemTheme": "System Theme", "english": "English", "arabic": "Arabic", "enable": "Enable", "disable": "Disable", "version": "Version", "emailInvalid": "Please enter a valid email", "passwordLength": "Password must be at least 6 characters", "passwordMatch": "Passwords do not match", "phoneNumber": "Phone Number", "theme": "Theme", "phoneInvalid": "Please enter a valid phone number", "errorOccurred": "An error occurred", "success": "Success", "continueAction": "Continue", "done": "Done", "changeProfilePhoto": "Change Photo", "camera": "Camera", "gallery": "Gallery", "removePhoto": "Remove Photo", "updateProfile": "Update Profile", "errorUpdatingProfile": "Error updating profile", "profileUpdated": "Profile updated successfully", "car": "Car", "deleteCar": "Delete Car", "deleteCarConfirmation": "Are you sure you want to delete this car? This action cannot be undone.", "delete": "Delete", "oilChangeStatus": "Oil Change Status", "lastOilChangeMileage": "Last Oil Change Mileage", "inKilometers": "in {km} km", "carDetails": "Car Details", "make": "Make", "model": "Model", "year": "year", "maintenanceIntervals": "Maintenance Intervals", "oilChangeInterval": "Oil Change Interval", "maintenanceHistory": "Maintenance History", "oilChanges": "Oil Changes", "errorLoadingCarDetails": "Error loading car details", "errorLastMileageGreater": "Last oil change mileage cannot be greater than current mileage", "errorAddingCar": "Error adding car", "pleaseEnterCarMake": "Please enter car make", "pleaseEnterCarModel": "Please enter car model", "pleaseEnterCarYear": "Please enter car year", "pleaseEnterValidYear": "Please enter a valid year", "yearRangeError": "Please enter a valid year between 1900 and current year", "pleaseEnterLastOilChangeMileage": "Please enter last oil change mileage", "pleaseEnterOilEndurance": "Please enter the oil's endurance in km (e.g., 5000)", "distance": "Distance (km)", "timeMonths": "Time (months)", "months": "months", "saveCar": "Save Car", "saveChanges": "Save Changes", "editCar": "Edit Car", "errorUpdatingCar": "Error updating car", "upcomingMaintenance": "Upcoming Maintenance", "kmDriven": "{km} km driven", "kmRemaining": "km remaining {km}", "@kmRemaining": {"placeholders": {"km": {"type": "int"}}}, "oilChangeProgress": "Oil Change Progress", "pleaseEnterCurrentMileage": "Please enter current mileage", "oilType": "Oil Type", "oilQuantity": "Oil Quantity (L)", "oilEnduranceKm": "Oil Endurance (km)", "pleaseEnterOilType": "Please enter oil type", "pleaseEnterOilQuantity": "Please enter oil quantity", "filterType": "Filter Type", "pleaseEnterFilterType": "Please enter filter type", "oilChangeDate": "Oil Change Date", "save": "Save", "driven": "Driven", "remaining": "Remaining", "noOilChangesRecorded": "No oil changes recorded yet", "tapPlusToAddOilChange": "Tap + to add an oil change", "mileage": "Mileage", "deleteOilChange": "Delete Oil Change", "deleteOilChangeConfirmation": "Are you sure you want to delete this oil change record?", "oilChangeDeleted": "Oil change deleted", "vehicleStatistics": "Vehicle Statistics", "averageMileage": "Average Mileage", "allGood": "All Good", "noMaintenanceAlerts": "No maintenance alerts", "noUpcomingReminders": "No upcoming reminders", "allMaintenanceUpToDate": "All maintenance up to date", "oilChangeDueInDays": "Oil change due in {days} days", "nextOilChangeStatus": "Next Oil Change: {mileage} km or {days} days", "currentMileageStatus": "Current Mileage: {mileage} km", "oilChangeHistory": "Oil Change History", "noOilChangesFound": "No oil change records found", "recordOilChangeFirst": "Record your first oil change to start tracking maintenance history", "oilChangeDetails": "Oil Change Details", "errorLoadingOilChangeDetails": "Error loading oil change details", "overview": "Overview", "maintenance": "Maintenance", "noMaintenanceRecords": "No maintenance records found", "maintenanceDescription": "Description", "maintenanceDate": "Date", "maintenanceCost": "Cost", "maintenanceProvider": "Service Provider", "maintenanceNotes": "Notes", "maintenanceAdded": "Maintenance record added successfully", "maintenanceDeleted": "Maintenance deleted successfully", "maintenanceUpdated": "Maintenance record updated successfully", "errorAddingMaintenance": "Error adding maintenance record", "errorDeletingMaintenance": "Error deleting maintenance", "errorUpdatingMaintenance": "Error updating maintenance record", "errorLoadingMaintenance": "Error loading maintenance records", "@daysUntilNextChange": {"placeholders": {"days": {"type": "int"}}}, "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "updatePassword": "Update Password", "passwordUpdatedSuccessfully": "Password updated successfully", "displayName": "Display Name", "displayNameRequired": "Please enter display name", "profileUpdatedSuccessfully": "Profile updated successfully", "chooseImageSource": "Choose Image Source", "profileImageUpdated": "Profile image updated successfully", "errorUpdatingProfileImage": "Error updating profile image. Please check your permissions.", "permissionRequired": "Permission Required", "cameraPermissionDenied": "Camera permission is required to take photos. Please enable it in settings.", "galleryPermissionDenied": "Storage permission is required to select photos. Please enable it in settings.", "openSettings": "Open Settings", "maintenanceTitle": "Maintenance Title", "orContinueWith": "Or continue with", "@orContinueWith": {"description": "Text shown between email login and social login options"}, "dontHaveAccount": "Don't have an account?", "@dontHaveAccount": {"description": "Text shown to prompt users to sign up"}, "preferences": "Preferences", "selectLanguage": "Select Language", "next": "Next", "skip": "<PERSON><PERSON>", "getStarted": "Get Started!", "onboardingTitle1": "Never Miss an Oil Change Again", "onboardingDesc1": "Forgetting oil changes can damage your engine and cost thousands. Oil Plus tracks everything automatically and reminds you when it's time.", "onboardingTitle2": "Everything You Need in One App", "onboardingDesc2": "Manage all your cars, trucks, and motorcycles. Get smart reminders before you're overdue. Keep complete records with photos and receipts.", "onboardingTitle3": "Start Protecting Your Investment", "onboardingDesc3": "Begin with essential tracking features for free. Upgrade to Premium for AI assistance, unlimited vehicles, and advanced features. Try Premium risk-free for 7 days.", "onboardingTitle4": "Track All Maintenance", "onboardingDesc4": "Keep records of all maintenance activities for your vehicles.", "onboardingTitle5": "Voice Commands & AI Assistant", "onboardingDesc5": "Use voice input and chat with our AI assistant for quick tasks and support.", "onboardingTitle6": "Cloud Backup & Premium", "onboardingDesc6": "Secure cloud backup, premium features and ad-free experience with a subscription.", "onboardingProblem": "Forgetting oil changes can damage your engine", "onboardingSolution": "Oil Plus tracks everything automatically", "onboardingFeature1": "Track Multiple Vehicles", "onboardingFeature1Desc": "Manage all your cars, trucks, and motorcycles", "onboardingFeature2": "Smart Reminders", "onboardingFeature2Desc": "Get notified before you're overdue", "onboardingFeature3": "Complete Records", "onboardingFeature3Desc": "Photos, receipts, and maintenance history", "onboardingCta": "Start Protecting Your Investment", "onboardingFreeStart": "Free to Start", "onboardingFreeStartDesc": "Begin with essential tracking features", "onboardingPremiumBenefits": "Premium Benefits", "onboardingPremiumBenefitsDesc": "AI assistance and unlimited vehicles", "onboardingTrialOffer": "7-Day Trial", "onboardingTrialOfferDesc": "Try Premium features risk-free", "deleteMaintenance": "Delete Maintenance", "confirmDeleteMaintenance": "Are you sure you want to delete this maintenance record? This action cannot be undone.", "offlineMode": "You are offline - some features may be limited", "noData": "No data available", "details": "Details", "selectRecommendedInterval": "Select Recommended Interval", "selected": "Selected: {value}", "@selected": {"placeholders": {"value": {"type": "String"}}}, "oilChangeRemainingMessage": "Only {km} km remaining until oil change", "@oilChangeRemainingMessage": {"placeholders": {"km": {"type": "String"}}}, "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "termsAndConditions": "Terms and Conditions", "legal": "Legal", "signInToTrackVehicles": "You need to sign in to track your vehicles", "carSavedAnyway": "Image upload failed but car will be updated", "errorUploadingImage": "Image upload failed", "addCarImage": "Add Car Image", "addCarImageHint": "Choose a photo of your car", "weatherUnavailable": "Weather Unavailable", "oilChangeRemindersDesc": "Get notified when your car needs an oil change", "maintenanceRemindersDesc": "Get notified about scheduled maintenance", "mileageRemindersDesc": "Get notified about mileage updates", "failedToRefreshToken": "Failed to refresh FCM token", "refreshToken": "Refresh <PERSON>", "permissions": "Permissions", "manageAppPermissions": "Manage app permissions", "notificationsPermissionDesc": "Receive timely reminders about oil changes and maintenance", "locationPermissionDesc": "Show local weather conditions for better maintenance recommendations", "cameraPermissionDesc": "Take photos of your vehicles for better tracking", "storagePermissionDesc": "Save and access photos of your vehicles", "location": "Location", "storage": "Storage", "timeBasedOilChangeInterval": "Time-based Oil Change Interval", "timeBasedIntervalExplanation": "Even with low mileage, oil should be changed periodically due to deterioration over time.", "timeBasedIntervalDescription": "Set how often you want to change your oil based on time, regardless of mileage.", "notificationExplanation": "You will receive notifications when your oil change is due based on either time or mileage, whichever comes first.", "invalidMonthRange": "Please enter a value between 1-12 months", "oilFilter": "Oil Filter", "didYouChangeFilter": "Did you change the filter?", "enterFilterType": "Enter filter type/brand", "costs": "Costs", "oilCost": "Oil Cost", "filterCost": "<PERSON><PERSON>st", "currencySymbol": "$", "add": "Add", "addNew": "Add New", "termsAcceptance": "Acceptance of Terms", "termsAcceptanceText": "By accessing or using Oil Plus, you agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you may not use our application.", "appUsage": "Application Usage", "appUsageText": "Oil Plus provides tools for tracking vehicle maintenance. The application and all content, features, and functionality are provided for your information and personal use only, and on an 'as is', 'as available' basis, without any warranties of any kind, either express or implied. While we strive for accuracy, we do not guarantee that any information provided through the application is accurate, complete, or current. We expressly disclaim all warranties, express or implied, including without limitation, warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.", "userAccounts": "User Accounts", "userAccountsText": "You are responsible for maintaining the confidentiality of your account information and password and for restricting access to your device. You accept responsibility for all activities that occur under your account. You must notify us immediately of any breach of security or unauthorized use of your account. We reserve the right to refuse service, terminate accounts, remove or edit content, or cancel orders at our sole discretion. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.", "userContent": "User Content", "userContentText": "You retain all rights in any content you submit, post, or display on or through the application ('User Content'). By submitting, posting, or displaying User Content on or through the application, you grant us a worldwide, non-exclusive, royalty-free license (with the right to sublicense) to use, copy, reproduce, process, adapt, modify, publish, transmit, display, and distribute such content in any and all media or distribution methods. You represent and warrant that: (1) you own the User Content or have the right to use and license it to us; and (2) the User Content does not violate the privacy rights, publicity rights, intellectual property rights, or any other rights of any person.", "intellectualProperty": "Intellectual Property", "intellectualPropertyText": "Oil Plus and its original content, features, and functionality are and will remain the exclusive property of Oil Plus and its licensors. The application is protected by copyright, trademark, and other laws of the United States and foreign countries. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Oil Plus. All content included in or made available through the application, including text, graphics, logos, images, data compilations, and software, is the property of Oil Plus or its content suppliers and protected by intellectual property laws.", "disclaimerWarranties": "Disclaimer of Warranties", "disclaimerWarrantiesText": "TO THE MAXIMUM EXTENT PERMITTED BY APPL<PERSON>ABLE LAW, THE APPLICATION AND ALL CONTENT, SERVICES, AND PRODUCTS ASSOCIATED WITH THE APPLICATION ARE PROVIDED TO YOU ON AN 'AS-IS' AND 'AS AVAILABLE' BASIS. OIL PLUS MAKES NO R<PERSON><PERSON><PERSON>NTATIONS OR WA<PERSON><PERSON>NTIES OF ANY KIND, EXPRESS OR <PERSON><PERSON><PERSON>IED, AS TO THE OPERATION OF THE APPLICATION, OR THE INFORMATION, CONTENT, MATERIALS, OR PRODUCTS INCLUDED ON THE APPLICATION. YOU EXPRESSLY AGREE THAT YOUR USE OF THE APPLICATION IS AT YOUR SOLE RISK. OIL PLUS DOES NOT WARRANT THAT THE APPLICATION, ITS SERVERS, OR EMAIL SENT FROM OIL PLUS ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS.", "limitationLiability": "Limitation of Liability", "limitationLiabilityText": "TO THE FULLEST EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL OIL PLUS, ITS AFFILIATES, OFFICERS, DIRECTORS, EMPLOYEES, AGENTS, S<PERSON><PERSON>IERS, OR LICENSORS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IA<PERSON>, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, <PERSON><PERSON><PERSON>LL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM: (1) YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE APPLICATION; (2) ANY CONDUCT OR CONTENT OF ANY THIRD PARTY ON THE APPLICATION; (3) ANY CONTENT OBTAINED FROM THE APPLICATION; AND (4) UNAUTHORIZED ACCESS, USE, OR ALTERATION OF YOUR TRANSMISSIONS OR CONTENT, WH<PERSON><PERSON><PERSON> BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE), OR ANY OTHER LEGAL THEORY, WH<PERSON>HER OR NOT WE HAVE BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGE. IN JURISDICTIONS WHERE THE EXCLUSION OR LIMITATION OF LIABILITY FOR CONSEQUENTIAL OR INCIDENTAL DAMAGES IS NOT ALLOWED, OUR LIABILITY IS LIMITED TO THE MAXIMUM EXTENT PERMITTED BY LAW.", "termsModifications": "Modifications to Terms", "termsModificationsText": "We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion. By continuing to access or use our application after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use the application.", "governingLaw": "Governing Law", "governingLawText": "These Terms and your use of the application shall be governed by and construed in accordance with the laws of the jurisdiction in which we operate, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect. These Terms constitute the entire agreement between us regarding our application, and supersede and replace any prior agreements we might have had between us regarding the application.", "contactUs": "Contact Us", "contactUsText": "If you have any questions about these Terms and Conditions or our Privacy Policy, please contact <NAME_EMAIL>.", "lastUpdated": "Last Updated", "privacyPolicyIntro": "Introduction", "privacyPolicyIntroText": "Welcome to Oil Plus! This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application. Please read this Privacy Policy carefully. By using the application, you consent to the data practices described in this statement.", "privacyPolicyInfo": "Information We Collect", "privacyPolicyInfoText": "We may collect several types of information from and about users of our application, including: (1) Personal information you voluntarily provide when using our application, such as email address, name, and profile details; (2) Information about your vehicle(s) including make, model, year, maintenance history, and images; (3) Usage data and analytics information about how you interact with our application; (4) Device information including operating system, hardware version, device settings, file and software names, battery and signal strength; (5) Location information when you enable this functionality; and (6) Information from third-party services if you choose to link them with your account.", "privacyPolicyUse": "How We Use Your Information", "privacyPolicyUseText": "We use information that we collect about you or that you provide to us, including any personal information: (1) To provide, maintain, and improve our Services; (2) To process and complete transactions, and send related information including confirmations and reminders; (3) To personalize your experience with our application; (4) To communicate with you, including for customer service, updates, security alerts, and support messages; (5) To analyze usage patterns and trends, and to better understand how users interact with our application; (6) To enhance security, monitor and verify identity, and prevent fraud; (7) For compliance purposes, including enforcing our Terms of Service; and (8) For any other purpose with your consent.", "privacyPolicyStorage": "Data Storage and Security", "privacyPolicyStorageText": "The security of your personal information is important to us. We implement and maintain reasonable security measures appropriate to the nature of the information we store in order to protect it from unauthorized access, destruction, use, modification, or disclosure. However, please be aware that no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee the absolute security of any information. Your data is stored on Firebase, a Google Cloud platform, with industry-standard encryption and security protocols. We retain personal information only for as long as necessary to fulfill the purposes for which it was collected and as required by applicable laws or regulations.", "privacyPolicyRights": "Your Rights", "privacyPolicyRightsText": "Depending on your location, you may have certain rights regarding your personal information. These may include: (1) The right to access personal information we hold about you; (2) The right to request correction of inaccurate data; (3) The right to request deletion of your data; (4) The right to restrict or object to our processing of your data; (5) The right to data portability; and (6) The right to withdraw consent. Please contact us if you wish to exercise any of these rights. We will respond to your request within the timeframe required by applicable law.", "privacyPolicyChildren": "Children's Privacy", "privacyPolicyChildrenText": "Our Services are not directed to children under the age of 13, and we do not knowingly collect personal information from children under 13. If we learn that we have collected personal information from a child under 13, we will take steps to delete such information as quickly as possible. If you believe we might have any information from or about a child under 13, please contact us immediately.", "privacyPolicyThirdParty": "Third-Party Services", "privacyPolicyThirdPartyText": "Our application may contain links to or integrate with third-party websites, services, or applications. We are not responsible for the privacy practices or content of these third parties. The collection, use, and disclosure of your information by these third parties are subject to their respective privacy policies, not this Privacy Policy. We encourage you to read the privacy policies of all third-party websites, services, or applications you visit or use.", "privacyPolicyChanges": "Changes to This Privacy Policy", "privacyPolicyChangesText": "We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the 'Last Updated' date. You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page. Your continued use of the application after we post any modifications to the Privacy Policy will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified Privacy Policy.", "indemnification": "Indemnification", "indemnificationText": "You agree to defend, indemnify, and hold harmless Oil Plus, its parent, subsidiaries, affiliates, and their respective directors, officers, employees, agents, service providers, contractors, licensors, suppliers, successors, and assigns from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys' fees) arising out of or relating to your violation of these Terms or your use of the application, including, but not limited to, any use of the application's content, services, and products other than as expressly authorized in these Terms or your use of any information obtained from the application.", "disputeResolution": "Dispute Resolution", "disputeResolutionText": "Any legal action or proceeding relating to your access to, or use of, the application or these Terms shall be instituted in a state or federal court in the jurisdiction where we operate. You and Oil Plus agree to submit to the personal jurisdiction of such courts, and to waive any and all objections to the exercise of jurisdiction over the parties by such courts and to venue in such courts.", "severability": "Severability", "severabilityText": "If any provision of these Terms is held by a court or other tribunal of competent jurisdiction to be invalid, illegal, or unenforceable for any reason, such provision shall be eliminated or limited to the minimum extent such that the remaining provisions of the Terms will continue in full force and effect.", "notifyChanges": "Notification of Changes", "notifyChangesText": "We reserve the right to make changes to our Privacy Policy and Terms at any time. If we decide to change our Privacy Policy or Terms, we will post those changes on this page so that you are always aware of what information we collect, how we use it, and under what circumstances we disclose it. Changes will be effective immediately upon posting to the application. Your continued use of the application after we post any modifications to the Privacy Policy or Terms will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified policy or terms.", "account": "Account", "clearCache": "<PERSON>ache", "clearLocalImageCache": "Clear local image cache", "cacheCleared": "<PERSON><PERSON> cleared successfully", "errorClearingCache": "Error clearing cache", "checkConnection": "Check your connection", "locationNeeded": "Location Access Needed", "tapToSetLocation": "Tap to set location", "locationPermissionGranted": "Location permission granted", "locationPermissionRationale": "We need location permission to show you local weather data. The weather information helps you plan oil changes based on local conditions.", "weatherOptions": "Weather Options", "useDeviceLocation": "Use Device Location", "locationExplanation": "Get precise weather for your current location", "useIpLocation": "Use Approximate Location", "ipLocationExplanation": "Estimate location based on your IP address", "refreshWeather": "Refresh Weather", "refreshWeatherExplanation": "Update weather data with your current settings", "authenticationError": "Authentication error: Please sign in again", "signInAgain": "Sign In", "errorFcmToken": "Error: FCM token not available", "errorGeneric": "Error: {message}", "@errorGeneric": {"placeholders": {"message": {"type": "String"}}}, "carUpdatedSuccess": "Car updated successfully", "imageUploadFailed": "Image upload failed but car will be saved", "addAnotherPhoto": "Add Another Photo", "showCarFromDifferentAngles": "Show your car from different angles", "selectRecommendedIntervalHint": "Select recommended interval", "onboardingReset": "Onboarding reset. Restart the app to see onboarding again.", "resetOnboardingTooltip": "Reset Onboarding (Testing Only)", "oilChangeNotificationSentSuccessfully": "Oil change notification sent successfully!", "failedToSendOilChangeNotification": "Failed to send oil change notification", "scheduledRemindersTriggeredSuccessfully": "Scheduled reminders triggered successfully!", "failedToTriggerScheduledReminders": "Failed to trigger scheduled reminders", "tokenRefreshed": "FCM Token refreshed: {token}...", "@tokenRefreshed": {"placeholders": {"token": {"type": "String"}}}, "viewPrivacyPolicy": "View our Privacy Policy", "viewTermsAndConditions": "View our Terms & Conditions", "mileageUpdatedTo": "{make} {model} mileage updated to {mileage} km", "@mileageUpdatedTo": {"description": "Success message after updating mileage", "placeholders": {"make": {"type": "String", "example": "Toyota"}, "model": {"type": "String", "example": "Cam<PERSON>"}, "mileage": {"type": "String", "example": "120,000"}}}, "totalMaintenanceCost": "Total Maintenance Cost", "totalRecords": "Total Records", "maintenanceDetails": "Maintenance Details", "errorLoadingMaintenanceDetails": "Error loading maintenance details", "editMaintenance": "Edit Maintenance", "existingPhotos": "Existing Photos", "addNewPhotos": "Add New Photos", "noMaintenanceRecordsYet": "No maintenance records yet", "receiptPhotos": "Receipt Photos", "addReceiptPhotosDesc": "Add photos of your maintenance receipts or invoices", "photos": "Photos", "viewPhoto": "View Photo", "errorLoadingImage": "Failed to load image", "failedToTakePhoto": "Failed to take photo", "failedToSelectPhoto": "Failed to select photo", "title": "Title", "enterTitle": "Enter maintenance title", "titleIsRequired": "Title is required", "enterOdometer": "Enter current odometer reading", "odometerMustBeNumber": "Odometer must be a number", "description": "Description", "enterDescription": "Enter maintenance description", "enterCost": "Enter maintenance cost", "costMustBeNumber": "Cost must be a number", "shopName": "Shop Name", "enterShopName": "Enter shop or service provider name", "dateIsRequired": "Date is required", "selectDate": "Select date", "odometer": "Odometer", "emailNotRegistered": "Email address is not registered", "forgotPasswordDescription": "Enter your email address and we will send you instructions to reset your password.", "resetPasswordEmailSent": "Email <PERSON>!", "resetPasswordCheckEmail": "Please check your email for instructions to reset your password.", "backToLogin": "Back to Login", "emailVerification": "Email Verification", "pleaseVerifyEmail": "Please verify your email address to access all features", "resendVerificationEmail": "Resend Verification Email", "emailVerificationSent": "Verification email sent", "emailVerificationFailed": "Failed to send verification email", "verifiedMyEmail": "I've Verified My Email", "checkingEmailVerification": "Checking verification status...", "emailNotVerifiedYet": "Email not verified yet. Please check your inbox.", "verificationEmailSentTo": "We have sent a verification email to:", "driverLicense": "Driver's License", "driverLicenseExpiryDate": "Driver's License Expiry Date", "tapToSetExpiryDate": "Tap to set expiry date", "selectExpiryDate": "Select when your driver's license expires", "expiresOn": "Expires", "expired": "Expired", "enableExpiryNotifications": "Enable expiry notifications", "receiveRemindersBeforeExpiry": "Receive reminders before your license expires", "today": "Today", "tomorrow": "Tomorrow", "days": "days", "expiredToday": "Expired today", "expiredYesterday": "Expired yesterday", "expiredDaysAgo": "Expired {days} days ago", "@expiredDaysAgo": {"placeholders": {"days": {"type": "int"}}}, "expiresToday": "Expires today", "expiresTomorrow": "Expires tomorrow", "expiresInDays": "Expires in {days} days", "@expiresInDays": {"placeholders": {"days": {"type": "int"}}}, "years": "years", "month": "month", "expiresIn": "Expires in {details}", "@expiresIn": {"placeholders": {"details": {"type": "String"}}}, "notificationScheduleInfo": "If enabled, reminders are sent 30, 14, 7, 1 day(s) before expiry, and on the expiry day.", "week": "week", "weeks": "weeks", "day": "day", "licenseExpiryDate": "License Expiry Date", "notSet": "Not Set", "licenseExpiredTitle": "License Expired", "licenseExpiredBody": "{year} {make} {model} license has expired.", "@licenseExpiredBody": {"placeholders": {"year": {"type": "String"}, "make": {"type": "String"}, "model": {"type": "String"}}}, "licenseExpiryReminderTitle": "License Expiry Reminder", "licenseExpiringSoonTitle": "License Expiring Soon", "licenseExpiringSoonBody": "{year} {make} {model} license expires in {days} days.", "@licenseExpiringSoonBody": {"placeholders": {"year": {"type": "String"}, "make": {"type": "String"}, "model": {"type": "String"}, "days": {"type": "String"}}}, "licenseExpiresTodayTitle": "License Expires Today!", "licenseExpiresTodayBody": "{year} {make} {model} license expires today.", "@licenseExpiresTodayBody": {"placeholders": {"year": {"type": "String"}, "make": {"type": "String"}, "model": {"type": "String"}}}, "isOverdueForOilChange": "is overdue for an oil change", "oilChangeDueSoon": "Oil Change Due Soon", "oilChangeDueSoonBody": "{year} {make} {model} will need an oil change in {days} days", "@oilChangeDueSoonBody": {"placeholders": {"year": {"type": "String"}, "make": {"type": "String"}, "model": {"type": "String"}, "days": {"type": "String"}}}, "oilChangeDueTodayTitle": "Oil Change Due Today", "oilChangeDueTodayBody": "{year} {make} {model} is due for an oil change today", "@oilChangeDueTodayBody": {"placeholders": {"year": {"type": "String"}, "make": {"type": "String"}, "model": {"type": "String"}}}, "takingPhoto": "Taking photo...", "noPhotoTaken": "No photo taken", "selectingPhoto": "Selecting photo...", "noPhotoSelected": "No photo selected", "networkError": "Network Connection Error", "checkInternetConnection": "Please check your internet connection", "networkConnectionError": "Network error. Please check your internet connection and try again.", "networkConnectionLost": "Network connection lost. Please check your internet connection and try again.", "unlockPremiumFeatures": "Unlock Premium Features", "chooseYourPlan": "Choose the plan that works for you", "saveWithYearly": "Save up to 30% with yearly subscription", "current": "Current", "free": "Free", "currentPlan": "Current Plan", "selectPlan": "Select Plan", "subscriptionActivated": "Subscription activated successfully", "subscriptionFailed": "Failed to activate subscription", "purchasesRestored": "Purchases restored successfully", "noPurchasesFound": "No purchases found", "subscriptionTerms": "Subscriptions can be managed and canceled through your Google Play account settings. No automatic renewals occur without your explicit consent.", "voiceCommands": "Voice Commands", "voiceCommandsDescription": "Use voice commands to quickly add records", "premiumFeature": "Premium Feature", "premiumRequired": "This feature requires a premium subscription", "upgradeNow": "Upgrade Now", "notNow": "Not Now", "tryFree": "Try Free for 7 Days", "familySharing": "Family Sharing", "familySharingDescription": "Share with up to 5 family members", "unlimitedVehicles": "Unlimited Vehicles", "adFreeExperience": "Ad-Free Experience", "enhancedAnalytics": "Enhanced Analytics", "prioritySupport": "Priority Support", "trialStarted": "Trial started successfully", "trialFailed": "Failed to start trial", "trialExpires": "Trial expires on {date}", "@trialExpires": {"placeholders": {"date": {"type": "String"}}}, "daysRemaining": "{days} days remaining", "@daysRemaining": {"placeholders": {"days": {"type": "int"}}}, "manageSubscription": "Manage Subscription", "subscriptionExpires": "Subscription expires on {date}", "@subscriptionExpires": {"placeholders": {"date": {"type": "String"}}}, "voiceCommandsHelp": "Voice Commands Help", "voiceCommandsHelpDescription": "Try these example commands with the voice input feature:", "addOilChange": "Add Oil Change", "productNotAvailable": "Product not available", "checkConnectionAndTryAgain": "Please check your internet connection and try again", "premium": "Premium", "upgradeToPremiun": "Upgrade to Premium", "subscriptionDetails": "Subscription Details", "premiumSubscription": "Premium Subscription", "freeVersion": "Free Version", "youHaveActivePremium": "You have an active premium subscription", "upgradeToRemoveAds": "Upgrade to premium to remove ads and unlock all features", "upgradeToPremium": "Upgrade to Premium", "viewCarDetails": "View Car Details", "unknownCommand": "Unknown Command", "youSaid": "You said", "extractedInformation": "Extracted Information", "confirm": "Confirm", "couldNotUnderstandCommand": "Could not understand the voice command. Please try again.", "noSpeechDetected": "No speech detected. Please try again.", "microphonePermissionDenied": "Microphone permission is required to use voice commands.", "cloudBackup": "Cloud Backup", "cloudBackupPremiumFeature": "Cloud Backup is a Premium Feature", "cloudBackupDescription": "Securely backup and restore your vehicle data, oil change history, and settings to the cloud.", "backupSuccessful": "Backup created successfully", "backupFailed": "Failed to create backup", "confirmRestore": "Confirm <PERSON>ore", "firstCarWelcomeMessage": "Let's add your first vehicle to start tracking oil changes and maintenance!", "firstCarStepIndicator": "Step 1 of 1: Add Your First Car", "quickTips": "Quick Tips:", "tipAddPhotos": "Add photos to easily identify your car", "tipEnterMileage": "Enter current mileage for accurate tracking", "tipSetIntervals": "Set oil change intervals based on your driving habits", "congratulations": "🎉 Congratulations!", "firstCarSuccessMessage": "You've successfully added your first car! You're now ready to track oil changes and maintenance.", "whatsNext": "What's Next:", "nextStepRecordOil": "Record your next oil change", "nextStepReminders": "Get reminders for maintenance", "nextStepTrackHealth": "Track your vehicle's health", "tapToAddPhotos": "Tap above to add photos of your car", "dashboardWelcomeSubtitle": "Start tracking your vehicle's oil changes and maintenance with ease", "whatYoullGet": "What you'll get:", "smartReminders": "Smart Reminders", "smartRemindersDesc": "Never miss an oil change with intelligent notifications", "trackHistory": "Track History", "trackHistoryDesc": "Monitor your vehicle's maintenance history and costs", "mileageTracking": "Mileage Tracking", "mileageTrackingDesc": "Keep track of your vehicle's mileage automatically", "restoreWarning": "This will replace all your current data with the backup data. This action cannot be undone.", "restore": "Rest<PERSON>", "restoreSuccessful": "Data restored successfully", "restoreFailed": "Failed to restore data", "automaticBackups": "Automatic Backups", "automaticBackupsDescription": "Automatically backup your data daily to ensure you never lose your records.", "enableAutomaticBackups": "Enable automatic backups", "automaticBackupsEnabled": "Automatic backups enabled", "automaticBackupsDisabled": "Automatic backups disabled", "manualBackup": "Manual Backup", "createBackup": "Create Backup", "backupHistory": "Backup History", "noBackupsFound": "No backups found", "manageCloudBackups": "Manage your cloud backups and restore data", "chatAssistantTitle": "Car Assistant 🔧", "clearChatTooltip": "Clear chat", "chatEmptyTitle": "Ask about any problem in your car", "chatEmptySubtitle": "I will help with diagnostics and solutions", "assistantThinking": "Assistant is thinking...", "chatFaqTitle": "FAQs:", "chatPromptEngineNoStart": "Engine won't start", "chatPromptAbsLight": "ABS light is on", "chatPromptOilLight": "Oil warning light", "chatPromptEngineNoise": "Strange engine noise", "chatPromptVibration": "Car vibrates while driving", "chatPromptBrakes": "Brake problem", "chatPromptHighTemp": "Engine temperature high", "chatPromptBattery": "Battery issue", "premiumRemoveAdsDescription": "Get access to all premium features and remove ads", "monthlyPremium": "Monthly Premium", "annualPremium": "Annual Premium", "basePrice": "Base price: {price}", "@basePrice": {"placeholders": {"price": {"type": "String"}}}, "monthlyEquivalent": "Monthly equivalent: {price}", "@monthlyEquivalent": {"placeholders": {"price": {"type": "String"}}}, "premiumFeaturesTitle": "Premium Features", "featureVoiceCommands": "Voice commands for quick entry", "featureUnlimitedVehicles": "Unlimited Vehicles", "featureCloudBackup": "Cloud Backup & Sync", "featureAdvancedAnalytics": "Advanced analytics", "freeTrialTitle": "Free Trial", "tryPremiumFeaturesFree": "Try Premium Features Free", "freeTrialDescription": "Enjoy a 7-day free trial with no commitment. Cancel anytime.", "selectPlanToTry": "Select a plan to try:", "freeTrialAgreement": "I understand that after the trial period ends, I can choose to subscribe if I wish to continue using premium features.", "startFreeTrial": "Start Free Trial", "freeTrialTerms": "By starting a free trial, you agree to our Terms of Service and Privacy Policy. The trial will end automatically after 7 days with no charges. To continue using premium features, you can manually subscribe.", "loadingPrice": "Loading price...", "trialStartedSuccess": "Trial started successfully!", "trialStartFailed": "Failed to start trial. Please try again.", "freeForSevenDaysThen": "Free for 7 days. After trial: {price} / {perMonth} (manual subscription required)", "@freeForSevenDaysThen": {"placeholders": {"price": {"type": "String"}, "perMonth": {"type": "String"}}}, "subscriptionPolicy": "Subscription Policy", "viewSubscriptionPolicy": "View subscription details", "refundPolicy": "Refund Policy", "viewRefundPolicy": "View refund policy", "aiAssistant": "AI Assistant", "aiChat": "AI Chat", "aiFeatures": "AI Features", "aiFeaturesPremiumDescription": "Voice commands and AI chat assistant are premium features that help you manage your vehicle maintenance more efficiently.", "voiceCommandsWelcome": "Voice Commands", "voiceCommandsSubtitle": "Use your voice to quickly record maintenance and oil changes", "recordOilChangeVoice": "Record oil changes using voice commands", "recordMaintenanceVoice": "Record maintenance tasks using voice commands", "featureLocked": "{featureName} Locked", "@featureLocked": {"placeholders": {"featureName": {"type": "String"}}}, "featureRequiresPremium": "This feature requires a premium subscription to unlock.", "premiumFeatures": "Premium Features", "featureAiChat": "AI Chat Assistant", "featureAdFree": "Ad-Free Experience", "verificationEmailSent": "Verification email sent", "errorSendingVerificationEmail": "Error sending verification email", "verifyEmail": "<PERSON><PERSON><PERSON>", "viewCar": "View Car", "noParametersDetected": "No parameters detected", "notSpecified": "Not specified", "restartApp": "Restart App", "adminNotifications": "Admin Notifications", "resetAllTokens": "Reset All Tokens", "sendToAllUsers": "Send to All Users", "notificationType": "Notification Type", "promotional": "Promotional", "oilChange": "Oil Change", "notificationTitle": "Notification Title", "notificationBody": "Notification Body", "enterNotificationTitle": "Enter notification title", "enterNotificationMessage": "Enter notification message", "titleBodyRequired": "Title and body are required", "accessDeniedAdmin": "Access denied. Admin privileges required.", "processing": "Processing...", "verificationResults": "Verification Results", "googleAccountDialogTitle": "Manage Your Google Account", "googleAccountDialogDescription": "To manage your Google account:\n\n1. Go to Settings > Google\n2. Select your account\n3. Manage your preferences", "goBack": "Go Back", "backToMaintenanceList": "Back to Maintenance List", "listening": "Listening...", "noVoiceDetected": "No voice detected.", "failedToStartRecording": "Failed to start recording: {error}", "errorMissingCarId": "Error: Car ID is missing.", "developerSettings": "Developer Settings", "premiumFeatureDescription": "Free users are limited to 3 vehicles. Upgrade to Premium for unlimited vehicles.", "voiceInputFeature1": "Voice input for quick data entry", "voiceInputFeature2": "Ad-free experience", "voiceInputFeature3": "Unlimited vehicles", "voiceInputFeature4": "Enhanced analytics and insights", "voiceInputFeature5": "Cloud backup and sync", "resettingAndRefreshingTokens": "Resetting and refreshing all tokens...", "@failedToStartRecording": {"placeholders": {"error": {}}}}