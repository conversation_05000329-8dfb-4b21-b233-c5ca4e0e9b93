import 'dart:async';
import 'dart:ui';
import 'dart:developer' as dev;
import 'dart:io' show Platform;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart' show kDebugMode, kReleaseMode;
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:oil_change_tracker/app/routes/app_router.dart';
import 'package:oil_change_tracker/core/providers/locale_provider.dart'
    hide sharedPreferencesProvider;
import 'package:oil_change_tracker/core/services/storage_service.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart' show BindingBase;
import 'package:oil_change_tracker/core/providers/locale_provider.dart'
    as locale_provider;
import 'package:oil_change_tracker/core/services/initialization_service.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:oil_change_tracker/core/services/app_check_debug_helper.dart';
// Ensure this path is correct for NotificationService, it might be in core/services
import 'package:oil_change_tracker/core/services/notification_service.dart';
import 'package:timezone/data/latest.dart'
    as tz_data; // Import for timezone data
import 'package:timezone/timezone.dart' as tz; // Import for timezone functions
import 'package:oil_change_tracker/core/services/api_key_service.dart'; // Import ApiKeyService
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';

import 'app.dart';
import 'core/config/firebase_options.dart';
import 'core/services/app_check_service.dart';
import 'core/utils/logger.dart';
import 'generated/app_localizations.dart';

// Global navigator key for error handling
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// No need for a global appOpenAdManager variable anymore - we'll use the provider

Future<void> _configureAppCheck() async {
  try {
    // Use the helper with new token e7debe73-145b-44ae-a6ad-be0a15c2bc2c
    if (kDebugMode) {
      dev.log('Setting up App Check in debug mode with updated token');
      final success = await AppCheckDebugHelper.setupDebugToken();

      if (success) {
        dev.log('App Check debug setup successful with new token');
      } else {
        // Troubleshoot if setup failed
        final troubleshooting =
            await AppCheckDebugHelper.troubleshootAppCheck();
        dev.log(troubleshooting);

        // Fallback to basic setup
        dev.log('Falling back to basic debug setup');
        await FirebaseAppCheck.instance.activate(
          androidProvider: AndroidProvider.debug,
        );
      }
    } else {
      // Production mode setup
      await FirebaseAppCheck.instance.activate(
        androidProvider: AndroidProvider.playIntegrity,
      );
    }
  } catch (e) {
    dev.log('Error configuring App Check: $e');
    // Continue app initialization despite errors
  }
}

Future<void> main() async {
  // Defer Flutter binding initialization until inside the zone

  // Make zone mismatch errors fatal in debug mode
  if (kDebugMode) {
    // This will help identify zone issues early
    BindingBase.debugZoneErrorsAreFatal = true;

    dev.log('DEBUG MODE: Enabling enhanced logging');
    // We'll set up FlutterError.onError inside the zone
  }

  await runZonedGuarded(() async {
    try {
      // Initialize Flutter bindings in the same zone as runApp
      WidgetsFlutterBinding.ensureInitialized();
      dev.log('Main: Flutter bindings initialized');

      // Initialize timezone database for notification scheduling
      tz_data.initializeTimeZones(); // Correct way to initialize timezones
      tz.setLocalLocation(tz.getLocation(
          'Africa/Cairo')); // Example: Set your default local timezone if needed
      dev.log('Main: Timezone initialized');

      // Set up error handling for Flutter errors
      if (kDebugMode) {
        FlutterError.onError = (FlutterErrorDetails details) {
          FlutterError.presentError(details);
          dev.log('Flutter Error: ${details.exception}',
              error: details.exception, stackTrace: details.stack);
        };
      }

      // Initialize Firebase - required before anything else
      dev.log('Main: Initializing Firebase...');
      try {
        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );
        dev.log('Main: Firebase initialized successfully');
      } catch (e) {
        if (e.toString().contains('duplicate-app')) {
          dev.log('Main: Firebase already initialized, continuing...');
        } else {
          rethrow;
        }
      }

      // Enable support for pending purchases on Android (required by Play Billing)
      if (Platform.isAndroid) {
        try {
          final androidAddition = InAppPurchase.instance
              .getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();
          // Using dynamic to be compatible across plugin versions
          (androidAddition as dynamic).enablePendingPurchases();
          dev.log('Main: Pending purchases enabled successfully');
        } catch (e) {
          dev.log('Main: Failed to enable pending purchases: $e');
        }
      }

      // Configure App Check immediately after Firebase init
      dev.log('Main: Configuring App Check...');
      await _configureAppCheck();
      dev.log('Main: App Check configured');

      // Initialize SharedPreferences first
      dev.log('Main: Initializing SharedPreferences...');
      final sharedPreferences = await SharedPreferences.getInstance();
      dev.log('Main: SharedPreferences initialized');

      // Create ProviderContainer for early service access
      final container = ProviderContainer(
        overrides: [
          // Override for storage_service.dart sharedPreferencesProvider
          sharedPreferencesProvider.overrideWithValue(sharedPreferences),

          // Override for locale_provider.dart sharedPreferencesProvider
          locale_provider.sharedPreferencesProvider
              .overrideWithValue(sharedPreferences),
        ],
        observers: [
          if (!kDebugMode) CrashlyticsLogger(),
        ],
      );

      // Initialize Hive for local storage
      dev.log('Main: Initializing Hive...');
      await Hive.initFlutter();
      dev.log('Main: Hive initialized');

      // Configure Firestore for offline persistence
      dev.log('Main: Configuring Firestore...');
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: 100 * 1024 * 1024, // 100MB
      );
      dev.log('Main: Firestore configured');

      // Initialize Crashlytics for production builds
      if (!kDebugMode) {
        await FirebaseCrashlytics.instance
            .setCrashlyticsCollectionEnabled(true);
        FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

        // Also capture uncaught async errors
        PlatformDispatcher.instance.onError = (error, stack) {
          FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
          return true;
        };
      }

      // Setup initialization service
      dev.log('Main: Setting up initialization service...');
      final initService = container.read(initializationServiceProvider);

      // Initialize only critical services before showing UI
      await initService.initializeCriticalServices();
      dev.log('Main: Critical services initialized');

      // Initialize NotificationService but don't request permissions yet
      dev.log('Main: Initializing notification service...');
      final notificationService = container.read(notificationServiceProvider);
      await notificationService.initialize(); // Ensure it's initialized
      // Permissions will be requested from the dashboard screen
      dev.log('Main: Notification service initialized');

      // Run the app with Riverpod
      dev.log('Main: Starting app...');
      runApp(
        ProviderScope(
          parent: container,
          observers: [InitializationObserver(initService)],
          overrides: [
            // Override for storage_service.dart sharedPreferencesProvider
            sharedPreferencesProvider.overrideWithValue(sharedPreferences),

            // Override for locale_provider.dart sharedPreferencesProvider
            locale_provider.sharedPreferencesProvider
                .overrideWithValue(sharedPreferences),
          ],
          child: const App(),
        ),
      );
      dev.log('Main: App started successfully');
    } catch (e, stack) {
      dev.log('Main: CRITICAL ERROR during initialization: $e',
          error: e, stackTrace: stack);

      // Show a simple error screen instead of white screen
      runApp(
        MaterialApp(
          home: Scaffold(
            backgroundColor: Colors.red,
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, color: Colors.white, size: 64),
                  const SizedBox(height: 16),
                  const Text(
                    'App Initialization Error',
                    style: TextStyle(color: Colors.white, fontSize: 24),
                  ),
                  const SizedBox(height: 8),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Error: $e',
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Still report to Crashlytics if available
      if (!kDebugMode) {
        try {
          FirebaseCrashlytics.instance.recordError(e, stack, fatal: true);
        } catch (_) {
          // Ignore if Crashlytics isn't available
        }
      }
    }
  }, (error, stack) {
    // Handle uncaught errors globally
    AppLogger.error('Main: Unhandled exception', error, stack);
    dev.log('Main: UNHANDLED ERROR: $error', error: error, stackTrace: stack);

    // Use Crashlytics in non-debug mode
    if (!kDebugMode) {
      try {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      } catch (_) {
        // Ignore if Crashlytics isn't available
      }
    }
  });
}

/// Observer that triggers background initialization when app is built
class InitializationObserver extends ProviderObserver {
  final InitializationService _initService;
  bool _hasTriggeredInit = false;

  InitializationObserver(this._initService);

  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    // Wait until app is built before starting background initialization
    if (!_hasTriggeredInit && provider.name == 'App') {
      _hasTriggeredInit = true;
      // Delay slightly to ensure UI is responsive first
      Future.delayed(const Duration(milliseconds: 100), () {
        _initService.startBackgroundInitialization();
      });
    }

    // Forward to Crashlytics in non-debug mode
    if (!kDebugMode) {
      FirebaseCrashlytics.instance.log(
        'Provider "${provider.name ?? provider.runtimeType}" updated from $previousValue to $newValue',
      );
    }
  }

  @override
  void providerDidFail(
    ProviderBase provider,
    Object error,
    StackTrace stackTrace,
    ProviderContainer container,
  ) {
    if (!kDebugMode) {
      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        reason: 'Provider ${provider.name ?? provider.runtimeType} failed',
      );
    }
  }
}

// Riverpod observer for logging state changes to Crashlytics
class CrashlyticsLogger extends ProviderObserver {
  @override
  void didUpdateProvider(
    ProviderBase provider,
    Object? previousValue,
    Object? newValue,
    ProviderContainer container,
  ) {
    FirebaseCrashlytics.instance.log(
      'Provider "${provider.name ?? provider.runtimeType}" updated from $previousValue to $newValue',
    );
  }

  @override
  void providerDidFail(
    ProviderBase provider,
    Object error,
    StackTrace stackTrace,
    ProviderContainer container,
  ) {
    FirebaseCrashlytics.instance.recordError(
      error,
      stackTrace,
      reason: 'Provider ${provider.name ?? provider.runtimeType} failed',
    );
  }
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch App Check initialization state
    final appCheckInit = ref.watch(appCheckInitializationProvider);

    // Log App Check initialization state
    appCheckInit.whenOrNull(
      data: (_) => AppLogger.info('MyApp: App Check initialization completed'),
      error: (e, _) =>
          AppLogger.error('MyApp: App Check initialization error', e),
      loading: () =>
          AppLogger.info('MyApp: App Check initialization in progress'),
    );

    final locale = ref.watch(localeProvider);
    final isArabic = locale.languageCode == 'ar';
    final analytics = FirebaseAnalytics.instance;

    return MaterialApp.router(
      title: 'Oil Plus',
      debugShowCheckedModeBanner: false,
      routerConfig: ref.watch(routerProvider),
      localizationsDelegates: const [
        S.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: S.supportedLocales,
      locale: locale,
      theme: ThemeData.dark().copyWith(
        textTheme: (isArabic
                ? GoogleFonts.tajawalTextTheme()
                : GoogleFonts.robotoTextTheme())
            .apply(bodyColor: Colors.white),
        colorScheme: const ColorScheme.dark(
          primary: Color(0xFFD8A25E), // Gold as primary
          secondary: Color(0xFFA04747), // Burgundy as secondary
          surface: Color(0xFF343131), // Slightly lighter dark gray
          error: Colors.red,
        ),
        appBarTheme: const AppBarTheme(
          backgroundColor: Color(0xFF343131),
          elevation: 0,
          centerTitle: false,
          iconTheme: IconThemeData(color: Color(0xFFD8A25E)),
          titleTextStyle: TextStyle(
            color: Color(0xFFD8A25E),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        cardTheme: CardThemeData(
          color: const Color(0xFF343131),
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        dialogTheme: DialogThemeData(
          backgroundColor: const Color(0xFF343131),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFD8A25E),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ),
    );
  }
}
