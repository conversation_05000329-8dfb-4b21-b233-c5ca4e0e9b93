import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S of(BuildContext context) {
    return Localizations.of<S>(context, S)!;
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// No description provided for @appName.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus'**
  String get appName;

  /// No description provided for @welcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Track your vehicle\'s oil changes and maintenance easily'**
  String get welcomeMessage;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signup;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @emailRequired.
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequired;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get invalidEmail;

  /// No description provided for @passwordRequired.
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequired;

  /// No description provided for @passwordTooShort.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordTooShort;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @nameRequired.
  ///
  /// In en, this message translates to:
  /// **'Name is required'**
  String get nameRequired;

  /// No description provided for @signInWithGoogle.
  ///
  /// In en, this message translates to:
  /// **'Sign in with Google'**
  String get signInWithGoogle;

  /// No description provided for @noAccountSignUp.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account? Sign up now'**
  String get noAccountSignUp;

  /// No description provided for @haveAccountLogin.
  ///
  /// In en, this message translates to:
  /// **'Already have an account? Log in'**
  String get haveAccountLogin;

  /// No description provided for @signUpSuccess.
  ///
  /// In en, this message translates to:
  /// **'Account created successfully! Please log in.'**
  String get signUpSuccess;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @confirmPasswordRequired.
  ///
  /// In en, this message translates to:
  /// **'Please confirm your password'**
  String get confirmPasswordRequired;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account? Log in'**
  String get alreadyHaveAccount;

  /// No description provided for @welcomeBack.
  ///
  /// In en, this message translates to:
  /// **'Welcome to your dashboard'**
  String get welcomeBack;

  /// No description provided for @oilConsumption.
  ///
  /// In en, this message translates to:
  /// **'Oil Consumption'**
  String get oilConsumption;

  /// No description provided for @used.
  ///
  /// In en, this message translates to:
  /// **'Used'**
  String get used;

  /// No description provided for @kmUntilNextOilChange.
  ///
  /// In en, this message translates to:
  /// **'{km} km until next oil change'**
  String kmUntilNextOilChange(Object km);

  /// No description provided for @oilChangeOverdue.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Overdue!'**
  String get oilChangeOverdue;

  /// No description provided for @recordOilChange.
  ///
  /// In en, this message translates to:
  /// **'Record Oil Change'**
  String get recordOilChange;

  /// No description provided for @nextOilChange.
  ///
  /// In en, this message translates to:
  /// **'Next Oil Change'**
  String nextOilChange(String mileage);

  /// No description provided for @overdue.
  ///
  /// In en, this message translates to:
  /// **'Overdue'**
  String get overdue;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @reminders.
  ///
  /// In en, this message translates to:
  /// **'Reminders'**
  String get reminders;

  /// No description provided for @oilChangeReminders.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Reminders'**
  String get oilChangeReminders;

  /// No description provided for @oilChangeRemindersDescription.
  ///
  /// In en, this message translates to:
  /// **'Receive notifications when your car needs an oil change'**
  String get oilChangeRemindersDescription;

  /// No description provided for @maintenanceReminders.
  ///
  /// In en, this message translates to:
  /// **'Maintenance Reminders'**
  String get maintenanceReminders;

  /// No description provided for @maintenanceRemindersDescription.
  ///
  /// In en, this message translates to:
  /// **'Receive notifications about scheduled maintenance'**
  String get maintenanceRemindersDescription;

  /// No description provided for @mileageReminders.
  ///
  /// In en, this message translates to:
  /// **'Mileage Reminders'**
  String get mileageReminders;

  /// No description provided for @mileageRemindersDescription.
  ///
  /// In en, this message translates to:
  /// **'Receive notifications about mileage updates'**
  String get mileageRemindersDescription;

  /// No description provided for @notificationSettings.
  ///
  /// In en, this message translates to:
  /// **'Manage notification preferences'**
  String get notificationSettings;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @aboutApp.
  ///
  /// In en, this message translates to:
  /// **'About Oil Plus'**
  String get aboutApp;

  /// No description provided for @aboutDescription.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus helps you track your vehicle\'s maintenance schedule and oil changes. Keep your vehicle healthy with timely reminders and a detailed service record.'**
  String get aboutDescription;

  /// No description provided for @personalInformation.
  ///
  /// In en, this message translates to:
  /// **'Personal Information'**
  String get personalInformation;

  /// No description provided for @security.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get security;

  /// No description provided for @appSettings.
  ///
  /// In en, this message translates to:
  /// **'App Settings'**
  String get appSettings;

  /// No description provided for @editProfile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get editProfile;

  /// No description provided for @changePassword.
  ///
  /// In en, this message translates to:
  /// **'Change Password'**
  String get changePassword;

  /// No description provided for @signOut.
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get signOut;

  /// No description provided for @signOutConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to sign out?'**
  String get signOutConfirmation;

  /// No description provided for @unknownUser.
  ///
  /// In en, this message translates to:
  /// **'Unknown User'**
  String get unknownUser;

  /// No description provided for @comingSoon.
  ///
  /// In en, this message translates to:
  /// **'Coming Soon!'**
  String get comingSoon;

  /// No description provided for @recordMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Record Maintenance'**
  String get recordMaintenance;

  /// No description provided for @selectCar.
  ///
  /// In en, this message translates to:
  /// **'Select Car'**
  String get selectCar;

  /// No description provided for @maintenanceType.
  ///
  /// In en, this message translates to:
  /// **'Maintenance Type'**
  String get maintenanceType;

  /// No description provided for @currentMileage.
  ///
  /// In en, this message translates to:
  /// **'Current Mileage'**
  String get currentMileage;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @cost.
  ///
  /// In en, this message translates to:
  /// **'Cost'**
  String get cost;

  /// No description provided for @serviceProvider.
  ///
  /// In en, this message translates to:
  /// **'Service Provider'**
  String get serviceProvider;

  /// No description provided for @notes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get notes;

  /// No description provided for @optional.
  ///
  /// In en, this message translates to:
  /// **'Optional'**
  String get optional;

  /// No description provided for @pleaseSelectCar.
  ///
  /// In en, this message translates to:
  /// **'Please select a car'**
  String get pleaseSelectCar;

  /// No description provided for @pleaseEnterMileage.
  ///
  /// In en, this message translates to:
  /// **'Please enter current mileage'**
  String get pleaseEnterMileage;

  /// No description provided for @pleaseEnterCost.
  ///
  /// In en, this message translates to:
  /// **'Please enter cost'**
  String get pleaseEnterCost;

  /// No description provided for @saveMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Save Maintenance'**
  String get saveMaintenance;

  /// No description provided for @maintenanceAddedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Maintenance record added successfully'**
  String get maintenanceAddedSuccess;

  /// No description provided for @noCarsFound.
  ///
  /// In en, this message translates to:
  /// **'No cars found'**
  String get noCarsFound;

  /// No description provided for @addCarFirst.
  ///
  /// In en, this message translates to:
  /// **'Add a car first to record maintenance'**
  String get addCarFirst;

  /// No description provided for @addCar.
  ///
  /// In en, this message translates to:
  /// **'Add Car'**
  String get addCar;

  /// No description provided for @errorLoadingCars.
  ///
  /// In en, this message translates to:
  /// **'Error loading cars'**
  String get errorLoadingCars;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @generalService.
  ///
  /// In en, this message translates to:
  /// **'General Service'**
  String get generalService;

  /// No description provided for @brakeService.
  ///
  /// In en, this message translates to:
  /// **'Brake Service'**
  String get brakeService;

  /// No description provided for @engineService.
  ///
  /// In en, this message translates to:
  /// **'Engine Service'**
  String get engineService;

  /// No description provided for @transmissionService.
  ///
  /// In en, this message translates to:
  /// **'Transmission Service'**
  String get transmissionService;

  /// No description provided for @tireService.
  ///
  /// In en, this message translates to:
  /// **'Tire Service'**
  String get tireService;

  /// No description provided for @batteryService.
  ///
  /// In en, this message translates to:
  /// **'Battery Service'**
  String get batteryService;

  /// No description provided for @airConditioning.
  ///
  /// In en, this message translates to:
  /// **'Air Conditioning'**
  String get airConditioning;

  /// No description provided for @electricalSystem.
  ///
  /// In en, this message translates to:
  /// **'Electrical System'**
  String get electricalSystem;

  /// No description provided for @suspension.
  ///
  /// In en, this message translates to:
  /// **'Suspension'**
  String get suspension;

  /// No description provided for @exhaustSystem.
  ///
  /// In en, this message translates to:
  /// **'Exhaust System'**
  String get exhaustSystem;

  /// No description provided for @fuelSystem.
  ///
  /// In en, this message translates to:
  /// **'Fuel System'**
  String get fuelSystem;

  /// No description provided for @coolingSystem.
  ///
  /// In en, this message translates to:
  /// **'Cooling System'**
  String get coolingSystem;

  /// No description provided for @regularMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Regular Maintenance'**
  String get regularMaintenance;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @myCars.
  ///
  /// In en, this message translates to:
  /// **'My Cars'**
  String get myCars;

  /// No description provided for @noCarsAddedYet.
  ///
  /// In en, this message translates to:
  /// **'No cars added yet'**
  String get noCarsAddedYet;

  /// No description provided for @addFirstCarMessage.
  ///
  /// In en, this message translates to:
  /// **'Add your first car to start tracking oil changes'**
  String get addFirstCarMessage;

  /// No description provided for @totalCars.
  ///
  /// In en, this message translates to:
  /// **'Total Cars'**
  String get totalCars;

  /// No description provided for @needOilChange.
  ///
  /// In en, this message translates to:
  /// **'Need Oil Change'**
  String get needOilChange;

  /// No description provided for @carsDueForOilChange.
  ///
  /// In en, this message translates to:
  /// **'Cars due for oil change'**
  String get carsDueForOilChange;

  /// No description provided for @viewAllCars.
  ///
  /// In en, this message translates to:
  /// **'View all cars'**
  String get viewAllCars;

  /// No description provided for @noUpcomingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'No upcoming maintenance'**
  String get noUpcomingMaintenance;

  /// No description provided for @allCarsUpToDate.
  ///
  /// In en, this message translates to:
  /// **'All cars are up to date with their maintenance schedule'**
  String get allCarsUpToDate;

  /// No description provided for @nextOilChangeInDays.
  ///
  /// In en, this message translates to:
  /// **'Next oil change in {days} days'**
  String nextOilChangeInDays(Object days);

  /// No description provided for @overdueDays.
  ///
  /// In en, this message translates to:
  /// **'Overdue by {days} days'**
  String overdueDays(Object days);

  /// No description provided for @daysUntilNextChange.
  ///
  /// In en, this message translates to:
  /// **'{days} days until next change'**
  String daysUntilNextChange(int days);

  /// No description provided for @viewAll.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get viewAll;

  /// No description provided for @goodMorning.
  ///
  /// In en, this message translates to:
  /// **'Good Morning'**
  String get goodMorning;

  /// No description provided for @goodAfternoon.
  ///
  /// In en, this message translates to:
  /// **'Good Afternoon'**
  String get goodAfternoon;

  /// No description provided for @goodEvening.
  ///
  /// In en, this message translates to:
  /// **'Good Evening'**
  String get goodEvening;

  /// No description provided for @subscriptions.
  ///
  /// In en, this message translates to:
  /// **'Subscriptions'**
  String get subscriptions;

  /// No description provided for @activeSubscription.
  ///
  /// In en, this message translates to:
  /// **'Active Subscription'**
  String get activeSubscription;

  /// No description provided for @billingPeriod.
  ///
  /// In en, this message translates to:
  /// **'Billing Period'**
  String get billingPeriod;

  /// No description provided for @monthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @save30Percent.
  ///
  /// In en, this message translates to:
  /// **'SAVE 30%'**
  String get save30Percent;

  /// No description provided for @perMonth.
  ///
  /// In en, this message translates to:
  /// **'per month'**
  String get perMonth;

  /// No description provided for @perYear.
  ///
  /// In en, this message translates to:
  /// **'per year'**
  String get perYear;

  /// No description provided for @subscribe.
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get subscribe;

  /// No description provided for @popular.
  ///
  /// In en, this message translates to:
  /// **'POPULAR'**
  String get popular;

  /// No description provided for @restorePurchases.
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get restorePurchases;

  /// No description provided for @cancelSubscription.
  ///
  /// In en, this message translates to:
  /// **'Cancel Subscription'**
  String get cancelSubscription;

  /// No description provided for @cancelSubscriptionTitle.
  ///
  /// In en, this message translates to:
  /// **'Cancel Subscription?'**
  String get cancelSubscriptionTitle;

  /// No description provided for @cancelSubscriptionMessage.
  ///
  /// In en, this message translates to:
  /// **'Your subscription will remain active until the end of the current billing period.'**
  String get cancelSubscriptionMessage;

  /// No description provided for @addNewCar.
  ///
  /// In en, this message translates to:
  /// **'Add New Car'**
  String get addNewCar;

  /// No description provided for @enterCarDetails.
  ///
  /// In en, this message translates to:
  /// **'Please fill in your car details'**
  String get enterCarDetails;

  /// No description provided for @saveSuccess.
  ///
  /// In en, this message translates to:
  /// **'Saved Successfully'**
  String get saveSuccess;

  /// No description provided for @pleaseSignIn.
  ///
  /// In en, this message translates to:
  /// **'Please sign in to view the dashboard'**
  String get pleaseSignIn;

  /// No description provided for @quickActions.
  ///
  /// In en, this message translates to:
  /// **'Quick Actions'**
  String get quickActions;

  /// No description provided for @addMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Add Maintenance'**
  String get addMaintenance;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @updateMileage.
  ///
  /// In en, this message translates to:
  /// **'Update Mileage'**
  String get updateMileage;

  /// No description provided for @mileageUpdated.
  ///
  /// In en, this message translates to:
  /// **'Mileage updated successfully'**
  String get mileageUpdated;

  /// No description provided for @invalidMileage.
  ///
  /// In en, this message translates to:
  /// **'Invalid mileage value'**
  String get invalidMileage;

  /// No description provided for @mileageRequired.
  ///
  /// In en, this message translates to:
  /// **'Mileage is required'**
  String get mileageRequired;

  /// No description provided for @mileageMustBeNumber.
  ///
  /// In en, this message translates to:
  /// **'Mileage must be a number'**
  String get mileageMustBeNumber;

  /// No description provided for @mileageMustBeGreaterThan.
  ///
  /// In en, this message translates to:
  /// **'Mileage must be greater than {currentMileage}'**
  String mileageMustBeGreaterThan(String currentMileage);

  /// No description provided for @mileageTooHigh.
  ///
  /// In en, this message translates to:
  /// **'Mileage seems too high, please check your input'**
  String get mileageTooHigh;

  /// No description provided for @lastOilChange.
  ///
  /// In en, this message translates to:
  /// **'Last oil change at'**
  String get lastOilChange;

  /// No description provided for @kilometersElapsed.
  ///
  /// In en, this message translates to:
  /// **'Kilometers elapsed'**
  String get kilometersElapsed;

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus'**
  String get appTitle;

  /// No description provided for @welcomeText.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Oil Plus'**
  String get welcomeText;

  /// No description provided for @signIn.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signIn;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// No description provided for @resetPassword.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPassword;

  /// No description provided for @sendResetLink.
  ///
  /// In en, this message translates to:
  /// **'Send Reset Link'**
  String get sendResetLink;

  /// No description provided for @noAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get noAccount;

  /// No description provided for @haveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get haveAccount;

  /// No description provided for @cars.
  ///
  /// In en, this message translates to:
  /// **'Cars'**
  String get cars;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @lightMode.
  ///
  /// In en, this message translates to:
  /// **'Light Mode'**
  String get lightMode;

  /// No description provided for @darkTheme.
  ///
  /// In en, this message translates to:
  /// **'Dark Theme'**
  String get darkTheme;

  /// No description provided for @lightTheme.
  ///
  /// In en, this message translates to:
  /// **'Light Theme'**
  String get lightTheme;

  /// No description provided for @themeSettings.
  ///
  /// In en, this message translates to:
  /// **'Theme Settings'**
  String get themeSettings;

  /// No description provided for @systemTheme.
  ///
  /// In en, this message translates to:
  /// **'System Theme'**
  String get systemTheme;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @enable.
  ///
  /// In en, this message translates to:
  /// **'Enable'**
  String get enable;

  /// No description provided for @disable.
  ///
  /// In en, this message translates to:
  /// **'Disable'**
  String get disable;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @emailInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get emailInvalid;

  /// No description provided for @passwordLength.
  ///
  /// In en, this message translates to:
  /// **'Password must be at least 6 characters'**
  String get passwordLength;

  /// No description provided for @passwordMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordMatch;

  /// No description provided for @phoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumber;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @phoneInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid phone number'**
  String get phoneInvalid;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'An error occurred'**
  String get errorOccurred;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @continueAction.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueAction;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @changeProfilePhoto.
  ///
  /// In en, this message translates to:
  /// **'Change Photo'**
  String get changeProfilePhoto;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @removePhoto.
  ///
  /// In en, this message translates to:
  /// **'Remove Photo'**
  String get removePhoto;

  /// No description provided for @updateProfile.
  ///
  /// In en, this message translates to:
  /// **'Update Profile'**
  String get updateProfile;

  /// No description provided for @errorUpdatingProfile.
  ///
  /// In en, this message translates to:
  /// **'Error updating profile'**
  String get errorUpdatingProfile;

  /// No description provided for @profileUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdated;

  /// No description provided for @car.
  ///
  /// In en, this message translates to:
  /// **'Car'**
  String get car;

  /// No description provided for @deleteCar.
  ///
  /// In en, this message translates to:
  /// **'Delete Car'**
  String get deleteCar;

  /// No description provided for @deleteCarConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this car? This action cannot be undone.'**
  String get deleteCarConfirmation;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @oilChangeStatus.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Status'**
  String get oilChangeStatus;

  /// No description provided for @lastOilChangeMileage.
  ///
  /// In en, this message translates to:
  /// **'Last Oil Change Mileage'**
  String get lastOilChangeMileage;

  /// No description provided for @inKilometers.
  ///
  /// In en, this message translates to:
  /// **'in {km} km'**
  String inKilometers(Object km);

  /// No description provided for @carDetails.
  ///
  /// In en, this message translates to:
  /// **'Car Details'**
  String get carDetails;

  /// No description provided for @make.
  ///
  /// In en, this message translates to:
  /// **'Make'**
  String get make;

  /// No description provided for @model.
  ///
  /// In en, this message translates to:
  /// **'Model'**
  String get model;

  /// No description provided for @year.
  ///
  /// In en, this message translates to:
  /// **'year'**
  String get year;

  /// No description provided for @maintenanceIntervals.
  ///
  /// In en, this message translates to:
  /// **'Maintenance Intervals'**
  String get maintenanceIntervals;

  /// No description provided for @oilChangeInterval.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Interval'**
  String get oilChangeInterval;

  /// No description provided for @maintenanceHistory.
  ///
  /// In en, this message translates to:
  /// **'Maintenance History'**
  String get maintenanceHistory;

  /// No description provided for @oilChanges.
  ///
  /// In en, this message translates to:
  /// **'Oil Changes'**
  String get oilChanges;

  /// No description provided for @errorLoadingCarDetails.
  ///
  /// In en, this message translates to:
  /// **'Error loading car details'**
  String get errorLoadingCarDetails;

  /// No description provided for @errorLastMileageGreater.
  ///
  /// In en, this message translates to:
  /// **'Last oil change mileage cannot be greater than current mileage'**
  String get errorLastMileageGreater;

  /// No description provided for @errorAddingCar.
  ///
  /// In en, this message translates to:
  /// **'Error adding car'**
  String get errorAddingCar;

  /// No description provided for @pleaseEnterCarMake.
  ///
  /// In en, this message translates to:
  /// **'Please enter car make'**
  String get pleaseEnterCarMake;

  /// No description provided for @pleaseEnterCarModel.
  ///
  /// In en, this message translates to:
  /// **'Please enter car model'**
  String get pleaseEnterCarModel;

  /// No description provided for @pleaseEnterCarYear.
  ///
  /// In en, this message translates to:
  /// **'Please enter car year'**
  String get pleaseEnterCarYear;

  /// No description provided for @pleaseEnterValidYear.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid year'**
  String get pleaseEnterValidYear;

  /// No description provided for @yearRangeError.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid year between 1900 and current year'**
  String get yearRangeError;

  /// No description provided for @pleaseEnterLastOilChangeMileage.
  ///
  /// In en, this message translates to:
  /// **'Please enter last oil change mileage'**
  String get pleaseEnterLastOilChangeMileage;

  /// No description provided for @pleaseEnterOilEndurance.
  ///
  /// In en, this message translates to:
  /// **'Please enter the oil\'s endurance in km (e.g., 5000)'**
  String get pleaseEnterOilEndurance;

  /// No description provided for @distance.
  ///
  /// In en, this message translates to:
  /// **'Distance (km)'**
  String get distance;

  /// No description provided for @timeMonths.
  ///
  /// In en, this message translates to:
  /// **'Time (months)'**
  String get timeMonths;

  /// No description provided for @months.
  ///
  /// In en, this message translates to:
  /// **'months'**
  String get months;

  /// No description provided for @saveCar.
  ///
  /// In en, this message translates to:
  /// **'Save Car'**
  String get saveCar;

  /// No description provided for @saveChanges.
  ///
  /// In en, this message translates to:
  /// **'Save Changes'**
  String get saveChanges;

  /// No description provided for @editCar.
  ///
  /// In en, this message translates to:
  /// **'Edit Car'**
  String get editCar;

  /// No description provided for @errorUpdatingCar.
  ///
  /// In en, this message translates to:
  /// **'Error updating car'**
  String get errorUpdatingCar;

  /// No description provided for @upcomingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Upcoming Maintenance'**
  String get upcomingMaintenance;

  /// No description provided for @kmDriven.
  ///
  /// In en, this message translates to:
  /// **'{km} km driven'**
  String kmDriven(Object km);

  /// No description provided for @kmRemaining.
  ///
  /// In en, this message translates to:
  /// **'km remaining {km}'**
  String kmRemaining(int km);

  /// No description provided for @oilChangeProgress.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Progress'**
  String get oilChangeProgress;

  /// No description provided for @pleaseEnterCurrentMileage.
  ///
  /// In en, this message translates to:
  /// **'Please enter current mileage'**
  String get pleaseEnterCurrentMileage;

  /// No description provided for @oilType.
  ///
  /// In en, this message translates to:
  /// **'Oil Type'**
  String get oilType;

  /// No description provided for @oilQuantity.
  ///
  /// In en, this message translates to:
  /// **'Oil Quantity (L)'**
  String get oilQuantity;

  /// No description provided for @oilEnduranceKm.
  ///
  /// In en, this message translates to:
  /// **'Oil Endurance (km)'**
  String get oilEnduranceKm;

  /// No description provided for @pleaseEnterOilType.
  ///
  /// In en, this message translates to:
  /// **'Please enter oil type'**
  String get pleaseEnterOilType;

  /// No description provided for @pleaseEnterOilQuantity.
  ///
  /// In en, this message translates to:
  /// **'Please enter oil quantity'**
  String get pleaseEnterOilQuantity;

  /// No description provided for @filterType.
  ///
  /// In en, this message translates to:
  /// **'Filter Type'**
  String get filterType;

  /// No description provided for @pleaseEnterFilterType.
  ///
  /// In en, this message translates to:
  /// **'Please enter filter type'**
  String get pleaseEnterFilterType;

  /// No description provided for @oilChangeDate.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Date'**
  String get oilChangeDate;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @driven.
  ///
  /// In en, this message translates to:
  /// **'Driven'**
  String get driven;

  /// No description provided for @remaining.
  ///
  /// In en, this message translates to:
  /// **'Remaining'**
  String get remaining;

  /// No description provided for @noOilChangesRecorded.
  ///
  /// In en, this message translates to:
  /// **'No oil changes recorded yet'**
  String get noOilChangesRecorded;

  /// No description provided for @tapPlusToAddOilChange.
  ///
  /// In en, this message translates to:
  /// **'Tap + to add an oil change'**
  String get tapPlusToAddOilChange;

  /// No description provided for @mileage.
  ///
  /// In en, this message translates to:
  /// **'Mileage'**
  String get mileage;

  /// No description provided for @deleteOilChange.
  ///
  /// In en, this message translates to:
  /// **'Delete Oil Change'**
  String get deleteOilChange;

  /// No description provided for @deleteOilChangeConfirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this oil change record?'**
  String get deleteOilChangeConfirmation;

  /// No description provided for @oilChangeDeleted.
  ///
  /// In en, this message translates to:
  /// **'Oil change deleted'**
  String get oilChangeDeleted;

  /// No description provided for @vehicleStatistics.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Statistics'**
  String get vehicleStatistics;

  /// No description provided for @averageMileage.
  ///
  /// In en, this message translates to:
  /// **'Average Mileage'**
  String get averageMileage;

  /// No description provided for @allGood.
  ///
  /// In en, this message translates to:
  /// **'All Good'**
  String get allGood;

  /// No description provided for @noMaintenanceAlerts.
  ///
  /// In en, this message translates to:
  /// **'No maintenance alerts'**
  String get noMaintenanceAlerts;

  /// No description provided for @noUpcomingReminders.
  ///
  /// In en, this message translates to:
  /// **'No upcoming reminders'**
  String get noUpcomingReminders;

  /// No description provided for @allMaintenanceUpToDate.
  ///
  /// In en, this message translates to:
  /// **'All maintenance up to date'**
  String get allMaintenanceUpToDate;

  /// No description provided for @oilChangeDueInDays.
  ///
  /// In en, this message translates to:
  /// **'Oil change due in {days} days'**
  String oilChangeDueInDays(Object days);

  /// No description provided for @nextOilChangeStatus.
  ///
  /// In en, this message translates to:
  /// **'Next Oil Change: {mileage} km or {days} days'**
  String nextOilChangeStatus(Object days, Object mileage);

  /// No description provided for @currentMileageStatus.
  ///
  /// In en, this message translates to:
  /// **'Current Mileage: {mileage} km'**
  String currentMileageStatus(Object mileage);

  /// No description provided for @oilChangeHistory.
  ///
  /// In en, this message translates to:
  /// **'Oil Change History'**
  String get oilChangeHistory;

  /// No description provided for @noOilChangesFound.
  ///
  /// In en, this message translates to:
  /// **'No oil change records found'**
  String get noOilChangesFound;

  /// No description provided for @recordOilChangeFirst.
  ///
  /// In en, this message translates to:
  /// **'Record your first oil change to start tracking maintenance history'**
  String get recordOilChangeFirst;

  /// No description provided for @oilChangeDetails.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Details'**
  String get oilChangeDetails;

  /// No description provided for @errorLoadingOilChangeDetails.
  ///
  /// In en, this message translates to:
  /// **'Error loading oil change details'**
  String get errorLoadingOilChangeDetails;

  /// No description provided for @overview.
  ///
  /// In en, this message translates to:
  /// **'Overview'**
  String get overview;

  /// No description provided for @maintenance.
  ///
  /// In en, this message translates to:
  /// **'Maintenance'**
  String get maintenance;

  /// No description provided for @noMaintenanceRecords.
  ///
  /// In en, this message translates to:
  /// **'No maintenance records found'**
  String get noMaintenanceRecords;

  /// No description provided for @maintenanceDescription.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get maintenanceDescription;

  /// No description provided for @maintenanceDate.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get maintenanceDate;

  /// No description provided for @maintenanceCost.
  ///
  /// In en, this message translates to:
  /// **'Cost'**
  String get maintenanceCost;

  /// No description provided for @maintenanceProvider.
  ///
  /// In en, this message translates to:
  /// **'Service Provider'**
  String get maintenanceProvider;

  /// No description provided for @maintenanceNotes.
  ///
  /// In en, this message translates to:
  /// **'Notes'**
  String get maintenanceNotes;

  /// No description provided for @maintenanceAdded.
  ///
  /// In en, this message translates to:
  /// **'Maintenance record added successfully'**
  String get maintenanceAdded;

  /// No description provided for @maintenanceDeleted.
  ///
  /// In en, this message translates to:
  /// **'Maintenance deleted successfully'**
  String get maintenanceDeleted;

  /// No description provided for @maintenanceUpdated.
  ///
  /// In en, this message translates to:
  /// **'Maintenance record updated successfully'**
  String get maintenanceUpdated;

  /// No description provided for @errorAddingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Error adding maintenance record'**
  String get errorAddingMaintenance;

  /// No description provided for @errorDeletingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Error deleting maintenance'**
  String get errorDeletingMaintenance;

  /// No description provided for @errorUpdatingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Error updating maintenance record'**
  String get errorUpdatingMaintenance;

  /// No description provided for @errorLoadingMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Error loading maintenance records'**
  String get errorLoadingMaintenance;

  /// No description provided for @currentPassword.
  ///
  /// In en, this message translates to:
  /// **'Current Password'**
  String get currentPassword;

  /// No description provided for @newPassword.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPassword;

  /// No description provided for @confirmNewPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm New Password'**
  String get confirmNewPassword;

  /// No description provided for @updatePassword.
  ///
  /// In en, this message translates to:
  /// **'Update Password'**
  String get updatePassword;

  /// No description provided for @passwordUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Password updated successfully'**
  String get passwordUpdatedSuccessfully;

  /// No description provided for @displayName.
  ///
  /// In en, this message translates to:
  /// **'Display Name'**
  String get displayName;

  /// No description provided for @displayNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Please enter display name'**
  String get displayNameRequired;

  /// No description provided for @profileUpdatedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profileUpdatedSuccessfully;

  /// No description provided for @chooseImageSource.
  ///
  /// In en, this message translates to:
  /// **'Choose Image Source'**
  String get chooseImageSource;

  /// No description provided for @profileImageUpdated.
  ///
  /// In en, this message translates to:
  /// **'Profile image updated successfully'**
  String get profileImageUpdated;

  /// No description provided for @errorUpdatingProfileImage.
  ///
  /// In en, this message translates to:
  /// **'Error updating profile image. Please check your permissions.'**
  String get errorUpdatingProfileImage;

  /// No description provided for @permissionRequired.
  ///
  /// In en, this message translates to:
  /// **'Permission Required'**
  String get permissionRequired;

  /// No description provided for @cameraPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Camera permission is required to take photos. Please enable it in settings.'**
  String get cameraPermissionDenied;

  /// No description provided for @galleryPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Storage permission is required to select photos. Please enable it in settings.'**
  String get galleryPermissionDenied;

  /// No description provided for @openSettings.
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get openSettings;

  /// No description provided for @maintenanceTitle.
  ///
  /// In en, this message translates to:
  /// **'Maintenance Title'**
  String get maintenanceTitle;

  /// Text shown between email login and social login options
  ///
  /// In en, this message translates to:
  /// **'Or continue with'**
  String get orContinueWith;

  /// Text shown to prompt users to sign up
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// No description provided for @preferences.
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// No description provided for @selectLanguage.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @getStarted.
  ///
  /// In en, this message translates to:
  /// **'Get Started!'**
  String get getStarted;

  /// No description provided for @onboardingTitle1.
  ///
  /// In en, this message translates to:
  /// **'Never Miss an Oil Change Again'**
  String get onboardingTitle1;

  /// No description provided for @onboardingDesc1.
  ///
  /// In en, this message translates to:
  /// **'Forgetting oil changes can damage your engine and cost thousands. Oil Plus tracks everything automatically and reminds you when it\'s time.'**
  String get onboardingDesc1;

  /// No description provided for @onboardingTitle2.
  ///
  /// In en, this message translates to:
  /// **'Everything You Need in One App'**
  String get onboardingTitle2;

  /// No description provided for @onboardingDesc2.
  ///
  /// In en, this message translates to:
  /// **'Manage all your cars, trucks, and motorcycles. Get smart reminders before you\'re overdue. Keep complete records with photos and receipts.'**
  String get onboardingDesc2;

  /// No description provided for @onboardingTitle3.
  ///
  /// In en, this message translates to:
  /// **'Start Protecting Your Investment'**
  String get onboardingTitle3;

  /// No description provided for @onboardingDesc3.
  ///
  /// In en, this message translates to:
  /// **'Begin with essential tracking features for free. Upgrade to Premium for AI assistance, unlimited vehicles, and advanced features. Try Premium risk-free for 7 days.'**
  String get onboardingDesc3;

  /// No description provided for @onboardingTitle4.
  ///
  /// In en, this message translates to:
  /// **'Track All Maintenance'**
  String get onboardingTitle4;

  /// No description provided for @onboardingDesc4.
  ///
  /// In en, this message translates to:
  /// **'Keep records of all maintenance activities for your vehicles.'**
  String get onboardingDesc4;

  /// No description provided for @onboardingTitle5.
  ///
  /// In en, this message translates to:
  /// **'Voice Commands & AI Assistant'**
  String get onboardingTitle5;

  /// No description provided for @onboardingDesc5.
  ///
  /// In en, this message translates to:
  /// **'Use voice input and chat with our AI assistant for quick tasks and support.'**
  String get onboardingDesc5;

  /// No description provided for @onboardingTitle6.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup & Premium'**
  String get onboardingTitle6;

  /// No description provided for @onboardingDesc6.
  ///
  /// In en, this message translates to:
  /// **'Secure cloud backup, premium features and ad-free experience with a subscription.'**
  String get onboardingDesc6;

  /// No description provided for @onboardingProblem.
  ///
  /// In en, this message translates to:
  /// **'Forgetting oil changes can damage your engine'**
  String get onboardingProblem;

  /// No description provided for @onboardingSolution.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus tracks everything automatically'**
  String get onboardingSolution;

  /// No description provided for @onboardingFeature1.
  ///
  /// In en, this message translates to:
  /// **'Track Multiple Vehicles'**
  String get onboardingFeature1;

  /// No description provided for @onboardingFeature1Desc.
  ///
  /// In en, this message translates to:
  /// **'Manage all your cars, trucks, and motorcycles'**
  String get onboardingFeature1Desc;

  /// No description provided for @onboardingFeature2.
  ///
  /// In en, this message translates to:
  /// **'Smart Reminders'**
  String get onboardingFeature2;

  /// No description provided for @onboardingFeature2Desc.
  ///
  /// In en, this message translates to:
  /// **'Get notified before you\'re overdue'**
  String get onboardingFeature2Desc;

  /// No description provided for @onboardingFeature3.
  ///
  /// In en, this message translates to:
  /// **'Complete Records'**
  String get onboardingFeature3;

  /// No description provided for @onboardingFeature3Desc.
  ///
  /// In en, this message translates to:
  /// **'Photos, receipts, and maintenance history'**
  String get onboardingFeature3Desc;

  /// No description provided for @onboardingCta.
  ///
  /// In en, this message translates to:
  /// **'Start Protecting Your Investment'**
  String get onboardingCta;

  /// No description provided for @onboardingFreeStart.
  ///
  /// In en, this message translates to:
  /// **'Free to Start'**
  String get onboardingFreeStart;

  /// No description provided for @onboardingFreeStartDesc.
  ///
  /// In en, this message translates to:
  /// **'Begin with essential tracking features'**
  String get onboardingFreeStartDesc;

  /// No description provided for @onboardingPremiumBenefits.
  ///
  /// In en, this message translates to:
  /// **'Premium Benefits'**
  String get onboardingPremiumBenefits;

  /// No description provided for @onboardingPremiumBenefitsDesc.
  ///
  /// In en, this message translates to:
  /// **'AI assistance and unlimited vehicles'**
  String get onboardingPremiumBenefitsDesc;

  /// No description provided for @onboardingTrialOffer.
  ///
  /// In en, this message translates to:
  /// **'7-Day Trial'**
  String get onboardingTrialOffer;

  /// No description provided for @onboardingTrialOfferDesc.
  ///
  /// In en, this message translates to:
  /// **'Try Premium features risk-free'**
  String get onboardingTrialOfferDesc;

  /// No description provided for @deleteMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Delete Maintenance'**
  String get deleteMaintenance;

  /// No description provided for @confirmDeleteMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this maintenance record? This action cannot be undone.'**
  String get confirmDeleteMaintenance;

  /// No description provided for @offlineMode.
  ///
  /// In en, this message translates to:
  /// **'You are offline - some features may be limited'**
  String get offlineMode;

  /// No description provided for @noData.
  ///
  /// In en, this message translates to:
  /// **'No data available'**
  String get noData;

  /// No description provided for @details.
  ///
  /// In en, this message translates to:
  /// **'Details'**
  String get details;

  /// No description provided for @selectRecommendedInterval.
  ///
  /// In en, this message translates to:
  /// **'Select Recommended Interval'**
  String get selectRecommendedInterval;

  /// No description provided for @selected.
  ///
  /// In en, this message translates to:
  /// **'Selected: {value}'**
  String selected(String value);

  /// No description provided for @oilChangeRemainingMessage.
  ///
  /// In en, this message translates to:
  /// **'Only {km} km remaining until oil change'**
  String oilChangeRemainingMessage(String km);

  /// No description provided for @privacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// No description provided for @termsOfService.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get termsOfService;

  /// No description provided for @termsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get termsAndConditions;

  /// No description provided for @legal.
  ///
  /// In en, this message translates to:
  /// **'Legal'**
  String get legal;

  /// No description provided for @signInToTrackVehicles.
  ///
  /// In en, this message translates to:
  /// **'You need to sign in to track your vehicles'**
  String get signInToTrackVehicles;

  /// No description provided for @carSavedAnyway.
  ///
  /// In en, this message translates to:
  /// **'Image upload failed but car will be updated'**
  String get carSavedAnyway;

  /// No description provided for @errorUploadingImage.
  ///
  /// In en, this message translates to:
  /// **'Image upload failed'**
  String get errorUploadingImage;

  /// No description provided for @addCarImage.
  ///
  /// In en, this message translates to:
  /// **'Add Car Image'**
  String get addCarImage;

  /// No description provided for @addCarImageHint.
  ///
  /// In en, this message translates to:
  /// **'Choose a photo of your car'**
  String get addCarImageHint;

  /// No description provided for @weatherUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Weather Unavailable'**
  String get weatherUnavailable;

  /// No description provided for @oilChangeRemindersDesc.
  ///
  /// In en, this message translates to:
  /// **'Get notified when your car needs an oil change'**
  String get oilChangeRemindersDesc;

  /// No description provided for @maintenanceRemindersDesc.
  ///
  /// In en, this message translates to:
  /// **'Get notified about scheduled maintenance'**
  String get maintenanceRemindersDesc;

  /// No description provided for @mileageRemindersDesc.
  ///
  /// In en, this message translates to:
  /// **'Get notified about mileage updates'**
  String get mileageRemindersDesc;

  /// No description provided for @failedToRefreshToken.
  ///
  /// In en, this message translates to:
  /// **'Failed to refresh FCM token'**
  String get failedToRefreshToken;

  /// No description provided for @refreshToken.
  ///
  /// In en, this message translates to:
  /// **'Refresh Token'**
  String get refreshToken;

  /// No description provided for @permissions.
  ///
  /// In en, this message translates to:
  /// **'Permissions'**
  String get permissions;

  /// No description provided for @manageAppPermissions.
  ///
  /// In en, this message translates to:
  /// **'Manage app permissions'**
  String get manageAppPermissions;

  /// No description provided for @notificationsPermissionDesc.
  ///
  /// In en, this message translates to:
  /// **'Receive timely reminders about oil changes and maintenance'**
  String get notificationsPermissionDesc;

  /// No description provided for @locationPermissionDesc.
  ///
  /// In en, this message translates to:
  /// **'Show local weather conditions for better maintenance recommendations'**
  String get locationPermissionDesc;

  /// No description provided for @cameraPermissionDesc.
  ///
  /// In en, this message translates to:
  /// **'Take photos of your vehicles for better tracking'**
  String get cameraPermissionDesc;

  /// No description provided for @storagePermissionDesc.
  ///
  /// In en, this message translates to:
  /// **'Save and access photos of your vehicles'**
  String get storagePermissionDesc;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @storage.
  ///
  /// In en, this message translates to:
  /// **'Storage'**
  String get storage;

  /// No description provided for @timeBasedOilChangeInterval.
  ///
  /// In en, this message translates to:
  /// **'Time-based Oil Change Interval'**
  String get timeBasedOilChangeInterval;

  /// No description provided for @timeBasedIntervalExplanation.
  ///
  /// In en, this message translates to:
  /// **'Even with low mileage, oil should be changed periodically due to deterioration over time.'**
  String get timeBasedIntervalExplanation;

  /// No description provided for @timeBasedIntervalDescription.
  ///
  /// In en, this message translates to:
  /// **'Set how often you want to change your oil based on time, regardless of mileage.'**
  String get timeBasedIntervalDescription;

  /// No description provided for @notificationExplanation.
  ///
  /// In en, this message translates to:
  /// **'You will receive notifications when your oil change is due based on either time or mileage, whichever comes first.'**
  String get notificationExplanation;

  /// No description provided for @invalidMonthRange.
  ///
  /// In en, this message translates to:
  /// **'Please enter a value between 1-12 months'**
  String get invalidMonthRange;

  /// No description provided for @oilFilter.
  ///
  /// In en, this message translates to:
  /// **'Oil Filter'**
  String get oilFilter;

  /// No description provided for @didYouChangeFilter.
  ///
  /// In en, this message translates to:
  /// **'Did you change the filter?'**
  String get didYouChangeFilter;

  /// No description provided for @enterFilterType.
  ///
  /// In en, this message translates to:
  /// **'Enter filter type/brand'**
  String get enterFilterType;

  /// No description provided for @costs.
  ///
  /// In en, this message translates to:
  /// **'Costs'**
  String get costs;

  /// No description provided for @oilCost.
  ///
  /// In en, this message translates to:
  /// **'Oil Cost'**
  String get oilCost;

  /// No description provided for @filterCost.
  ///
  /// In en, this message translates to:
  /// **'Filter Cost'**
  String get filterCost;

  /// No description provided for @currencySymbol.
  ///
  /// In en, this message translates to:
  /// **'\$'**
  String get currencySymbol;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @addNew.
  ///
  /// In en, this message translates to:
  /// **'Add New'**
  String get addNew;

  /// No description provided for @termsAcceptance.
  ///
  /// In en, this message translates to:
  /// **'Acceptance of Terms'**
  String get termsAcceptance;

  /// No description provided for @termsAcceptanceText.
  ///
  /// In en, this message translates to:
  /// **'By accessing or using Oil Plus, you agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you may not use our application.'**
  String get termsAcceptanceText;

  /// No description provided for @appUsage.
  ///
  /// In en, this message translates to:
  /// **'Application Usage'**
  String get appUsage;

  /// No description provided for @appUsageText.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus provides tools for tracking vehicle maintenance. The application and all content, features, and functionality are provided for your information and personal use only, and on an \'as is\', \'as available\' basis, without any warranties of any kind, either express or implied. While we strive for accuracy, we do not guarantee that any information provided through the application is accurate, complete, or current. We expressly disclaim all warranties, express or implied, including without limitation, warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.'**
  String get appUsageText;

  /// No description provided for @userAccounts.
  ///
  /// In en, this message translates to:
  /// **'User Accounts'**
  String get userAccounts;

  /// No description provided for @userAccountsText.
  ///
  /// In en, this message translates to:
  /// **'You are responsible for maintaining the confidentiality of your account information and password and for restricting access to your device. You accept responsibility for all activities that occur under your account. You must notify us immediately of any breach of security or unauthorized use of your account. We reserve the right to refuse service, terminate accounts, remove or edit content, or cancel orders at our sole discretion. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.'**
  String get userAccountsText;

  /// No description provided for @userContent.
  ///
  /// In en, this message translates to:
  /// **'User Content'**
  String get userContent;

  /// No description provided for @userContentText.
  ///
  /// In en, this message translates to:
  /// **'You retain all rights in any content you submit, post, or display on or through the application (\'User Content\'). By submitting, posting, or displaying User Content on or through the application, you grant us a worldwide, non-exclusive, royalty-free license (with the right to sublicense) to use, copy, reproduce, process, adapt, modify, publish, transmit, display, and distribute such content in any and all media or distribution methods. You represent and warrant that: (1) you own the User Content or have the right to use and license it to us; and (2) the User Content does not violate the privacy rights, publicity rights, intellectual property rights, or any other rights of any person.'**
  String get userContentText;

  /// No description provided for @intellectualProperty.
  ///
  /// In en, this message translates to:
  /// **'Intellectual Property'**
  String get intellectualProperty;

  /// No description provided for @intellectualPropertyText.
  ///
  /// In en, this message translates to:
  /// **'Oil Plus and its original content, features, and functionality are and will remain the exclusive property of Oil Plus and its licensors. The application is protected by copyright, trademark, and other laws of the United States and foreign countries. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Oil Plus. All content included in or made available through the application, including text, graphics, logos, images, data compilations, and software, is the property of Oil Plus or its content suppliers and protected by intellectual property laws.'**
  String get intellectualPropertyText;

  /// No description provided for @disclaimerWarranties.
  ///
  /// In en, this message translates to:
  /// **'Disclaimer of Warranties'**
  String get disclaimerWarranties;

  /// No description provided for @disclaimerWarrantiesText.
  ///
  /// In en, this message translates to:
  /// **'TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE APPLICATION AND ALL CONTENT, SERVICES, AND PRODUCTS ASSOCIATED WITH THE APPLICATION ARE PROVIDED TO YOU ON AN \'AS-IS\' AND \'AS AVAILABLE\' BASIS. OIL PLUS MAKES NO REPRESENTATIONS OR WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED, AS TO THE OPERATION OF THE APPLICATION, OR THE INFORMATION, CONTENT, MATERIALS, OR PRODUCTS INCLUDED ON THE APPLICATION. YOU EXPRESSLY AGREE THAT YOUR USE OF THE APPLICATION IS AT YOUR SOLE RISK. OIL PLUS DOES NOT WARRANT THAT THE APPLICATION, ITS SERVERS, OR EMAIL SENT FROM OIL PLUS ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS.'**
  String get disclaimerWarrantiesText;

  /// No description provided for @limitationLiability.
  ///
  /// In en, this message translates to:
  /// **'Limitation of Liability'**
  String get limitationLiability;

  /// No description provided for @limitationLiabilityText.
  ///
  /// In en, this message translates to:
  /// **'TO THE FULLEST EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL OIL PLUS, ITS AFFILIATES, OFFICERS, DIRECTORS, EMPLOYEES, AGENTS, SUPPLIERS, OR LICENSORS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM: (1) YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE APPLICATION; (2) ANY CONDUCT OR CONTENT OF ANY THIRD PARTY ON THE APPLICATION; (3) ANY CONTENT OBTAINED FROM THE APPLICATION; AND (4) UNAUTHORIZED ACCESS, USE, OR ALTERATION OF YOUR TRANSMISSIONS OR CONTENT, WHETHER BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE), OR ANY OTHER LEGAL THEORY, WHETHER OR NOT WE HAVE BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGE. IN JURISDICTIONS WHERE THE EXCLUSION OR LIMITATION OF LIABILITY FOR CONSEQUENTIAL OR INCIDENTAL DAMAGES IS NOT ALLOWED, OUR LIABILITY IS LIMITED TO THE MAXIMUM EXTENT PERMITTED BY LAW.'**
  String get limitationLiabilityText;

  /// No description provided for @termsModifications.
  ///
  /// In en, this message translates to:
  /// **'Modifications to Terms'**
  String get termsModifications;

  /// No description provided for @termsModificationsText.
  ///
  /// In en, this message translates to:
  /// **'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days\' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion. By continuing to access or use our application after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use the application.'**
  String get termsModificationsText;

  /// No description provided for @governingLaw.
  ///
  /// In en, this message translates to:
  /// **'Governing Law'**
  String get governingLaw;

  /// No description provided for @governingLawText.
  ///
  /// In en, this message translates to:
  /// **'These Terms and your use of the application shall be governed by and construed in accordance with the laws of the jurisdiction in which we operate, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect. These Terms constitute the entire agreement between us regarding our application, and supersede and replace any prior agreements we might have had between us regarding the application.'**
  String get governingLawText;

  /// No description provided for @contactUs.
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// No description provided for @contactUsText.
  ///
  /// In en, this message translates to:
  /// **'If you have any questions about these Terms and Conditions or our Privacy Policy, please contact <NAME_EMAIL>.'**
  String get contactUsText;

  /// No description provided for @lastUpdated.
  ///
  /// In en, this message translates to:
  /// **'Last Updated'**
  String get lastUpdated;

  /// No description provided for @privacyPolicyIntro.
  ///
  /// In en, this message translates to:
  /// **'Introduction'**
  String get privacyPolicyIntro;

  /// No description provided for @privacyPolicyIntroText.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Oil Plus! This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application. Please read this Privacy Policy carefully. By using the application, you consent to the data practices described in this statement.'**
  String get privacyPolicyIntroText;

  /// No description provided for @privacyPolicyInfo.
  ///
  /// In en, this message translates to:
  /// **'Information We Collect'**
  String get privacyPolicyInfo;

  /// No description provided for @privacyPolicyInfoText.
  ///
  /// In en, this message translates to:
  /// **'We may collect several types of information from and about users of our application, including: (1) Personal information you voluntarily provide when using our application, such as email address, name, and profile details; (2) Information about your vehicle(s) including make, model, year, maintenance history, and images; (3) Usage data and analytics information about how you interact with our application; (4) Device information including operating system, hardware version, device settings, file and software names, battery and signal strength; (5) Location information when you enable this functionality; and (6) Information from third-party services if you choose to link them with your account.'**
  String get privacyPolicyInfoText;

  /// No description provided for @privacyPolicyUse.
  ///
  /// In en, this message translates to:
  /// **'How We Use Your Information'**
  String get privacyPolicyUse;

  /// No description provided for @privacyPolicyUseText.
  ///
  /// In en, this message translates to:
  /// **'We use information that we collect about you or that you provide to us, including any personal information: (1) To provide, maintain, and improve our Services; (2) To process and complete transactions, and send related information including confirmations and reminders; (3) To personalize your experience with our application; (4) To communicate with you, including for customer service, updates, security alerts, and support messages; (5) To analyze usage patterns and trends, and to better understand how users interact with our application; (6) To enhance security, monitor and verify identity, and prevent fraud; (7) For compliance purposes, including enforcing our Terms of Service; and (8) For any other purpose with your consent.'**
  String get privacyPolicyUseText;

  /// No description provided for @privacyPolicyStorage.
  ///
  /// In en, this message translates to:
  /// **'Data Storage and Security'**
  String get privacyPolicyStorage;

  /// No description provided for @privacyPolicyStorageText.
  ///
  /// In en, this message translates to:
  /// **'The security of your personal information is important to us. We implement and maintain reasonable security measures appropriate to the nature of the information we store in order to protect it from unauthorized access, destruction, use, modification, or disclosure. However, please be aware that no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee the absolute security of any information. Your data is stored on Firebase, a Google Cloud platform, with industry-standard encryption and security protocols. We retain personal information only for as long as necessary to fulfill the purposes for which it was collected and as required by applicable laws or regulations.'**
  String get privacyPolicyStorageText;

  /// No description provided for @privacyPolicyRights.
  ///
  /// In en, this message translates to:
  /// **'Your Rights'**
  String get privacyPolicyRights;

  /// No description provided for @privacyPolicyRightsText.
  ///
  /// In en, this message translates to:
  /// **'Depending on your location, you may have certain rights regarding your personal information. These may include: (1) The right to access personal information we hold about you; (2) The right to request correction of inaccurate data; (3) The right to request deletion of your data; (4) The right to restrict or object to our processing of your data; (5) The right to data portability; and (6) The right to withdraw consent. Please contact us if you wish to exercise any of these rights. We will respond to your request within the timeframe required by applicable law.'**
  String get privacyPolicyRightsText;

  /// No description provided for @privacyPolicyChildren.
  ///
  /// In en, this message translates to:
  /// **'Children\'s Privacy'**
  String get privacyPolicyChildren;

  /// No description provided for @privacyPolicyChildrenText.
  ///
  /// In en, this message translates to:
  /// **'Our Services are not directed to children under the age of 13, and we do not knowingly collect personal information from children under 13. If we learn that we have collected personal information from a child under 13, we will take steps to delete such information as quickly as possible. If you believe we might have any information from or about a child under 13, please contact us immediately.'**
  String get privacyPolicyChildrenText;

  /// No description provided for @privacyPolicyThirdParty.
  ///
  /// In en, this message translates to:
  /// **'Third-Party Services'**
  String get privacyPolicyThirdParty;

  /// No description provided for @privacyPolicyThirdPartyText.
  ///
  /// In en, this message translates to:
  /// **'Our application may contain links to or integrate with third-party websites, services, or applications. We are not responsible for the privacy practices or content of these third parties. The collection, use, and disclosure of your information by these third parties are subject to their respective privacy policies, not this Privacy Policy. We encourage you to read the privacy policies of all third-party websites, services, or applications you visit or use.'**
  String get privacyPolicyThirdPartyText;

  /// No description provided for @privacyPolicyChanges.
  ///
  /// In en, this message translates to:
  /// **'Changes to This Privacy Policy'**
  String get privacyPolicyChanges;

  /// No description provided for @privacyPolicyChangesText.
  ///
  /// In en, this message translates to:
  /// **'We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the \'Last Updated\' date. You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page. Your continued use of the application after we post any modifications to the Privacy Policy will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified Privacy Policy.'**
  String get privacyPolicyChangesText;

  /// No description provided for @indemnification.
  ///
  /// In en, this message translates to:
  /// **'Indemnification'**
  String get indemnification;

  /// No description provided for @indemnificationText.
  ///
  /// In en, this message translates to:
  /// **'You agree to defend, indemnify, and hold harmless Oil Plus, its parent, subsidiaries, affiliates, and their respective directors, officers, employees, agents, service providers, contractors, licensors, suppliers, successors, and assigns from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys\' fees) arising out of or relating to your violation of these Terms or your use of the application, including, but not limited to, any use of the application\'s content, services, and products other than as expressly authorized in these Terms or your use of any information obtained from the application.'**
  String get indemnificationText;

  /// No description provided for @disputeResolution.
  ///
  /// In en, this message translates to:
  /// **'Dispute Resolution'**
  String get disputeResolution;

  /// No description provided for @disputeResolutionText.
  ///
  /// In en, this message translates to:
  /// **'Any legal action or proceeding relating to your access to, or use of, the application or these Terms shall be instituted in a state or federal court in the jurisdiction where we operate. You and Oil Plus agree to submit to the personal jurisdiction of such courts, and to waive any and all objections to the exercise of jurisdiction over the parties by such courts and to venue in such courts.'**
  String get disputeResolutionText;

  /// No description provided for @severability.
  ///
  /// In en, this message translates to:
  /// **'Severability'**
  String get severability;

  /// No description provided for @severabilityText.
  ///
  /// In en, this message translates to:
  /// **'If any provision of these Terms is held by a court or other tribunal of competent jurisdiction to be invalid, illegal, or unenforceable for any reason, such provision shall be eliminated or limited to the minimum extent such that the remaining provisions of the Terms will continue in full force and effect.'**
  String get severabilityText;

  /// No description provided for @notifyChanges.
  ///
  /// In en, this message translates to:
  /// **'Notification of Changes'**
  String get notifyChanges;

  /// No description provided for @notifyChangesText.
  ///
  /// In en, this message translates to:
  /// **'We reserve the right to make changes to our Privacy Policy and Terms at any time. If we decide to change our Privacy Policy or Terms, we will post those changes on this page so that you are always aware of what information we collect, how we use it, and under what circumstances we disclose it. Changes will be effective immediately upon posting to the application. Your continued use of the application after we post any modifications to the Privacy Policy or Terms will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified policy or terms.'**
  String get notifyChangesText;

  /// No description provided for @account.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// No description provided for @clearCache.
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get clearCache;

  /// No description provided for @clearLocalImageCache.
  ///
  /// In en, this message translates to:
  /// **'Clear local image cache'**
  String get clearLocalImageCache;

  /// No description provided for @cacheCleared.
  ///
  /// In en, this message translates to:
  /// **'Cache cleared successfully'**
  String get cacheCleared;

  /// No description provided for @errorClearingCache.
  ///
  /// In en, this message translates to:
  /// **'Error clearing cache'**
  String get errorClearingCache;

  /// No description provided for @checkConnection.
  ///
  /// In en, this message translates to:
  /// **'Check your connection'**
  String get checkConnection;

  /// No description provided for @locationNeeded.
  ///
  /// In en, this message translates to:
  /// **'Location Access Needed'**
  String get locationNeeded;

  /// No description provided for @tapToSetLocation.
  ///
  /// In en, this message translates to:
  /// **'Tap to set location'**
  String get tapToSetLocation;

  /// No description provided for @locationPermissionGranted.
  ///
  /// In en, this message translates to:
  /// **'Location permission granted'**
  String get locationPermissionGranted;

  /// No description provided for @locationPermissionRationale.
  ///
  /// In en, this message translates to:
  /// **'We need location permission to show you local weather data. The weather information helps you plan oil changes based on local conditions.'**
  String get locationPermissionRationale;

  /// No description provided for @weatherOptions.
  ///
  /// In en, this message translates to:
  /// **'Weather Options'**
  String get weatherOptions;

  /// No description provided for @useDeviceLocation.
  ///
  /// In en, this message translates to:
  /// **'Use Device Location'**
  String get useDeviceLocation;

  /// No description provided for @locationExplanation.
  ///
  /// In en, this message translates to:
  /// **'Get precise weather for your current location'**
  String get locationExplanation;

  /// No description provided for @useIpLocation.
  ///
  /// In en, this message translates to:
  /// **'Use Approximate Location'**
  String get useIpLocation;

  /// No description provided for @ipLocationExplanation.
  ///
  /// In en, this message translates to:
  /// **'Estimate location based on your IP address'**
  String get ipLocationExplanation;

  /// No description provided for @refreshWeather.
  ///
  /// In en, this message translates to:
  /// **'Refresh Weather'**
  String get refreshWeather;

  /// No description provided for @refreshWeatherExplanation.
  ///
  /// In en, this message translates to:
  /// **'Update weather data with your current settings'**
  String get refreshWeatherExplanation;

  /// No description provided for @authenticationError.
  ///
  /// In en, this message translates to:
  /// **'Authentication error: Please sign in again'**
  String get authenticationError;

  /// No description provided for @signInAgain.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get signInAgain;

  /// No description provided for @errorFcmToken.
  ///
  /// In en, this message translates to:
  /// **'Error: FCM token not available'**
  String get errorFcmToken;

  /// No description provided for @errorGeneric.
  ///
  /// In en, this message translates to:
  /// **'Error: {message}'**
  String errorGeneric(String message);

  /// No description provided for @carUpdatedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Car updated successfully'**
  String get carUpdatedSuccess;

  /// No description provided for @imageUploadFailed.
  ///
  /// In en, this message translates to:
  /// **'Image upload failed but car will be saved'**
  String get imageUploadFailed;

  /// No description provided for @addAnotherPhoto.
  ///
  /// In en, this message translates to:
  /// **'Add Another Photo'**
  String get addAnotherPhoto;

  /// No description provided for @showCarFromDifferentAngles.
  ///
  /// In en, this message translates to:
  /// **'Show your car from different angles'**
  String get showCarFromDifferentAngles;

  /// No description provided for @selectRecommendedIntervalHint.
  ///
  /// In en, this message translates to:
  /// **'Select recommended interval'**
  String get selectRecommendedIntervalHint;

  /// No description provided for @onboardingReset.
  ///
  /// In en, this message translates to:
  /// **'Onboarding reset. Restart the app to see onboarding again.'**
  String get onboardingReset;

  /// No description provided for @resetOnboardingTooltip.
  ///
  /// In en, this message translates to:
  /// **'Reset Onboarding (Testing Only)'**
  String get resetOnboardingTooltip;

  /// No description provided for @oilChangeNotificationSentSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Oil change notification sent successfully!'**
  String get oilChangeNotificationSentSuccessfully;

  /// No description provided for @failedToSendOilChangeNotification.
  ///
  /// In en, this message translates to:
  /// **'Failed to send oil change notification'**
  String get failedToSendOilChangeNotification;

  /// No description provided for @scheduledRemindersTriggeredSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Scheduled reminders triggered successfully!'**
  String get scheduledRemindersTriggeredSuccessfully;

  /// No description provided for @failedToTriggerScheduledReminders.
  ///
  /// In en, this message translates to:
  /// **'Failed to trigger scheduled reminders'**
  String get failedToTriggerScheduledReminders;

  /// No description provided for @tokenRefreshed.
  ///
  /// In en, this message translates to:
  /// **'FCM Token refreshed: {token}...'**
  String tokenRefreshed(String token);

  /// No description provided for @viewPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'View our Privacy Policy'**
  String get viewPrivacyPolicy;

  /// No description provided for @viewTermsAndConditions.
  ///
  /// In en, this message translates to:
  /// **'View our Terms & Conditions'**
  String get viewTermsAndConditions;

  /// Success message after updating mileage
  ///
  /// In en, this message translates to:
  /// **'{make} {model} mileage updated to {mileage} km'**
  String mileageUpdatedTo(String make, String model, String mileage);

  /// No description provided for @totalMaintenanceCost.
  ///
  /// In en, this message translates to:
  /// **'Total Maintenance Cost'**
  String get totalMaintenanceCost;

  /// No description provided for @totalRecords.
  ///
  /// In en, this message translates to:
  /// **'Total Records'**
  String get totalRecords;

  /// No description provided for @maintenanceDetails.
  ///
  /// In en, this message translates to:
  /// **'Maintenance Details'**
  String get maintenanceDetails;

  /// No description provided for @errorLoadingMaintenanceDetails.
  ///
  /// In en, this message translates to:
  /// **'Error loading maintenance details'**
  String get errorLoadingMaintenanceDetails;

  /// No description provided for @editMaintenance.
  ///
  /// In en, this message translates to:
  /// **'Edit Maintenance'**
  String get editMaintenance;

  /// No description provided for @existingPhotos.
  ///
  /// In en, this message translates to:
  /// **'Existing Photos'**
  String get existingPhotos;

  /// No description provided for @addNewPhotos.
  ///
  /// In en, this message translates to:
  /// **'Add New Photos'**
  String get addNewPhotos;

  /// No description provided for @noMaintenanceRecordsYet.
  ///
  /// In en, this message translates to:
  /// **'No maintenance records yet'**
  String get noMaintenanceRecordsYet;

  /// No description provided for @receiptPhotos.
  ///
  /// In en, this message translates to:
  /// **'Receipt Photos'**
  String get receiptPhotos;

  /// No description provided for @addReceiptPhotosDesc.
  ///
  /// In en, this message translates to:
  /// **'Add photos of your maintenance receipts or invoices'**
  String get addReceiptPhotosDesc;

  /// No description provided for @photos.
  ///
  /// In en, this message translates to:
  /// **'Photos'**
  String get photos;

  /// No description provided for @viewPhoto.
  ///
  /// In en, this message translates to:
  /// **'View Photo'**
  String get viewPhoto;

  /// No description provided for @errorLoadingImage.
  ///
  /// In en, this message translates to:
  /// **'Failed to load image'**
  String get errorLoadingImage;

  /// No description provided for @failedToTakePhoto.
  ///
  /// In en, this message translates to:
  /// **'Failed to take photo'**
  String get failedToTakePhoto;

  /// No description provided for @failedToSelectPhoto.
  ///
  /// In en, this message translates to:
  /// **'Failed to select photo'**
  String get failedToSelectPhoto;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @enterTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter maintenance title'**
  String get enterTitle;

  /// No description provided for @titleIsRequired.
  ///
  /// In en, this message translates to:
  /// **'Title is required'**
  String get titleIsRequired;

  /// No description provided for @enterOdometer.
  ///
  /// In en, this message translates to:
  /// **'Enter current odometer reading'**
  String get enterOdometer;

  /// No description provided for @odometerMustBeNumber.
  ///
  /// In en, this message translates to:
  /// **'Odometer must be a number'**
  String get odometerMustBeNumber;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @enterDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter maintenance description'**
  String get enterDescription;

  /// No description provided for @enterCost.
  ///
  /// In en, this message translates to:
  /// **'Enter maintenance cost'**
  String get enterCost;

  /// No description provided for @costMustBeNumber.
  ///
  /// In en, this message translates to:
  /// **'Cost must be a number'**
  String get costMustBeNumber;

  /// No description provided for @shopName.
  ///
  /// In en, this message translates to:
  /// **'Shop Name'**
  String get shopName;

  /// No description provided for @enterShopName.
  ///
  /// In en, this message translates to:
  /// **'Enter shop or service provider name'**
  String get enterShopName;

  /// No description provided for @dateIsRequired.
  ///
  /// In en, this message translates to:
  /// **'Date is required'**
  String get dateIsRequired;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select date'**
  String get selectDate;

  /// No description provided for @odometer.
  ///
  /// In en, this message translates to:
  /// **'Odometer'**
  String get odometer;

  /// No description provided for @emailNotRegistered.
  ///
  /// In en, this message translates to:
  /// **'Email address is not registered'**
  String get emailNotRegistered;

  /// No description provided for @forgotPasswordDescription.
  ///
  /// In en, this message translates to:
  /// **'Enter your email address and we will send you instructions to reset your password.'**
  String get forgotPasswordDescription;

  /// No description provided for @resetPasswordEmailSent.
  ///
  /// In en, this message translates to:
  /// **'Email Sent!'**
  String get resetPasswordEmailSent;

  /// No description provided for @resetPasswordCheckEmail.
  ///
  /// In en, this message translates to:
  /// **'Please check your email for instructions to reset your password.'**
  String get resetPasswordCheckEmail;

  /// No description provided for @backToLogin.
  ///
  /// In en, this message translates to:
  /// **'Back to Login'**
  String get backToLogin;

  /// No description provided for @emailVerification.
  ///
  /// In en, this message translates to:
  /// **'Email Verification'**
  String get emailVerification;

  /// No description provided for @pleaseVerifyEmail.
  ///
  /// In en, this message translates to:
  /// **'Please verify your email address to access all features'**
  String get pleaseVerifyEmail;

  /// No description provided for @resendVerificationEmail.
  ///
  /// In en, this message translates to:
  /// **'Resend Verification Email'**
  String get resendVerificationEmail;

  /// No description provided for @emailVerificationSent.
  ///
  /// In en, this message translates to:
  /// **'Verification email sent'**
  String get emailVerificationSent;

  /// No description provided for @emailVerificationFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to send verification email'**
  String get emailVerificationFailed;

  /// No description provided for @verifiedMyEmail.
  ///
  /// In en, this message translates to:
  /// **'I\'ve Verified My Email'**
  String get verifiedMyEmail;

  /// No description provided for @checkingEmailVerification.
  ///
  /// In en, this message translates to:
  /// **'Checking verification status...'**
  String get checkingEmailVerification;

  /// No description provided for @emailNotVerifiedYet.
  ///
  /// In en, this message translates to:
  /// **'Email not verified yet. Please check your inbox.'**
  String get emailNotVerifiedYet;

  /// No description provided for @verificationEmailSentTo.
  ///
  /// In en, this message translates to:
  /// **'We have sent a verification email to:'**
  String get verificationEmailSentTo;

  /// No description provided for @driverLicense.
  ///
  /// In en, this message translates to:
  /// **'Driver\'s License'**
  String get driverLicense;

  /// No description provided for @driverLicenseExpiryDate.
  ///
  /// In en, this message translates to:
  /// **'Driver\'s License Expiry Date'**
  String get driverLicenseExpiryDate;

  /// No description provided for @tapToSetExpiryDate.
  ///
  /// In en, this message translates to:
  /// **'Tap to set expiry date'**
  String get tapToSetExpiryDate;

  /// No description provided for @selectExpiryDate.
  ///
  /// In en, this message translates to:
  /// **'Select when your driver\'s license expires'**
  String get selectExpiryDate;

  /// No description provided for @expiresOn.
  ///
  /// In en, this message translates to:
  /// **'Expires'**
  String get expiresOn;

  /// No description provided for @expired.
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get expired;

  /// No description provided for @enableExpiryNotifications.
  ///
  /// In en, this message translates to:
  /// **'Enable expiry notifications'**
  String get enableExpiryNotifications;

  /// No description provided for @receiveRemindersBeforeExpiry.
  ///
  /// In en, this message translates to:
  /// **'Receive reminders before your license expires'**
  String get receiveRemindersBeforeExpiry;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @tomorrow.
  ///
  /// In en, this message translates to:
  /// **'Tomorrow'**
  String get tomorrow;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @expiredToday.
  ///
  /// In en, this message translates to:
  /// **'Expired today'**
  String get expiredToday;

  /// No description provided for @expiredYesterday.
  ///
  /// In en, this message translates to:
  /// **'Expired yesterday'**
  String get expiredYesterday;

  /// No description provided for @expiredDaysAgo.
  ///
  /// In en, this message translates to:
  /// **'Expired {days} days ago'**
  String expiredDaysAgo(int days);

  /// No description provided for @expiresToday.
  ///
  /// In en, this message translates to:
  /// **'Expires today'**
  String get expiresToday;

  /// No description provided for @expiresTomorrow.
  ///
  /// In en, this message translates to:
  /// **'Expires tomorrow'**
  String get expiresTomorrow;

  /// No description provided for @expiresInDays.
  ///
  /// In en, this message translates to:
  /// **'Expires in {days} days'**
  String expiresInDays(int days);

  /// No description provided for @years.
  ///
  /// In en, this message translates to:
  /// **'years'**
  String get years;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'month'**
  String get month;

  /// No description provided for @expiresIn.
  ///
  /// In en, this message translates to:
  /// **'Expires in {details}'**
  String expiresIn(String details);

  /// No description provided for @notificationScheduleInfo.
  ///
  /// In en, this message translates to:
  /// **'If enabled, reminders are sent 30, 14, 7, 1 day(s) before expiry, and on the expiry day.'**
  String get notificationScheduleInfo;

  /// No description provided for @week.
  ///
  /// In en, this message translates to:
  /// **'week'**
  String get week;

  /// No description provided for @weeks.
  ///
  /// In en, this message translates to:
  /// **'weeks'**
  String get weeks;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @licenseExpiryDate.
  ///
  /// In en, this message translates to:
  /// **'License Expiry Date'**
  String get licenseExpiryDate;

  /// No description provided for @notSet.
  ///
  /// In en, this message translates to:
  /// **'Not Set'**
  String get notSet;

  /// No description provided for @licenseExpiredTitle.
  ///
  /// In en, this message translates to:
  /// **'License Expired'**
  String get licenseExpiredTitle;

  /// No description provided for @licenseExpiredBody.
  ///
  /// In en, this message translates to:
  /// **'{year} {make} {model} license has expired.'**
  String licenseExpiredBody(String year, String make, String model);

  /// No description provided for @licenseExpiryReminderTitle.
  ///
  /// In en, this message translates to:
  /// **'License Expiry Reminder'**
  String get licenseExpiryReminderTitle;

  /// No description provided for @licenseExpiringSoonTitle.
  ///
  /// In en, this message translates to:
  /// **'License Expiring Soon'**
  String get licenseExpiringSoonTitle;

  /// No description provided for @licenseExpiringSoonBody.
  ///
  /// In en, this message translates to:
  /// **'{year} {make} {model} license expires in {days} days.'**
  String licenseExpiringSoonBody(
      String year, String make, String model, String days);

  /// No description provided for @licenseExpiresTodayTitle.
  ///
  /// In en, this message translates to:
  /// **'License Expires Today!'**
  String get licenseExpiresTodayTitle;

  /// No description provided for @licenseExpiresTodayBody.
  ///
  /// In en, this message translates to:
  /// **'{year} {make} {model} license expires today.'**
  String licenseExpiresTodayBody(String year, String make, String model);

  /// No description provided for @isOverdueForOilChange.
  ///
  /// In en, this message translates to:
  /// **'is overdue for an oil change'**
  String get isOverdueForOilChange;

  /// No description provided for @oilChangeDueSoon.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Due Soon'**
  String get oilChangeDueSoon;

  /// No description provided for @oilChangeDueSoonBody.
  ///
  /// In en, this message translates to:
  /// **'{year} {make} {model} will need an oil change in {days} days'**
  String oilChangeDueSoonBody(
      String year, String make, String model, String days);

  /// No description provided for @oilChangeDueTodayTitle.
  ///
  /// In en, this message translates to:
  /// **'Oil Change Due Today'**
  String get oilChangeDueTodayTitle;

  /// No description provided for @oilChangeDueTodayBody.
  ///
  /// In en, this message translates to:
  /// **'{year} {make} {model} is due for an oil change today'**
  String oilChangeDueTodayBody(String year, String make, String model);

  /// No description provided for @takingPhoto.
  ///
  /// In en, this message translates to:
  /// **'Taking photo...'**
  String get takingPhoto;

  /// No description provided for @noPhotoTaken.
  ///
  /// In en, this message translates to:
  /// **'No photo taken'**
  String get noPhotoTaken;

  /// No description provided for @selectingPhoto.
  ///
  /// In en, this message translates to:
  /// **'Selecting photo...'**
  String get selectingPhoto;

  /// No description provided for @noPhotoSelected.
  ///
  /// In en, this message translates to:
  /// **'No photo selected'**
  String get noPhotoSelected;

  /// No description provided for @networkError.
  ///
  /// In en, this message translates to:
  /// **'Network Connection Error'**
  String get networkError;

  /// No description provided for @checkInternetConnection.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection'**
  String get checkInternetConnection;

  /// No description provided for @networkConnectionError.
  ///
  /// In en, this message translates to:
  /// **'Network error. Please check your internet connection and try again.'**
  String get networkConnectionError;

  /// No description provided for @networkConnectionLost.
  ///
  /// In en, this message translates to:
  /// **'Network connection lost. Please check your internet connection and try again.'**
  String get networkConnectionLost;

  /// No description provided for @unlockPremiumFeatures.
  ///
  /// In en, this message translates to:
  /// **'Unlock Premium Features'**
  String get unlockPremiumFeatures;

  /// No description provided for @chooseYourPlan.
  ///
  /// In en, this message translates to:
  /// **'Choose the plan that works for you'**
  String get chooseYourPlan;

  /// No description provided for @saveWithYearly.
  ///
  /// In en, this message translates to:
  /// **'Save up to 30% with yearly subscription'**
  String get saveWithYearly;

  /// No description provided for @current.
  ///
  /// In en, this message translates to:
  /// **'Current'**
  String get current;

  /// No description provided for @free.
  ///
  /// In en, this message translates to:
  /// **'Free'**
  String get free;

  /// No description provided for @currentPlan.
  ///
  /// In en, this message translates to:
  /// **'Current Plan'**
  String get currentPlan;

  /// No description provided for @selectPlan.
  ///
  /// In en, this message translates to:
  /// **'Select Plan'**
  String get selectPlan;

  /// No description provided for @subscriptionActivated.
  ///
  /// In en, this message translates to:
  /// **'Subscription activated successfully'**
  String get subscriptionActivated;

  /// No description provided for @subscriptionFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to activate subscription'**
  String get subscriptionFailed;

  /// No description provided for @purchasesRestored.
  ///
  /// In en, this message translates to:
  /// **'Purchases restored successfully'**
  String get purchasesRestored;

  /// No description provided for @noPurchasesFound.
  ///
  /// In en, this message translates to:
  /// **'No purchases found'**
  String get noPurchasesFound;

  /// No description provided for @subscriptionTerms.
  ///
  /// In en, this message translates to:
  /// **'Subscriptions can be managed and canceled through your Google Play account settings. No automatic renewals occur without your explicit consent.'**
  String get subscriptionTerms;

  /// No description provided for @voiceCommands.
  ///
  /// In en, this message translates to:
  /// **'Voice Commands'**
  String get voiceCommands;

  /// No description provided for @voiceCommandsDescription.
  ///
  /// In en, this message translates to:
  /// **'Use voice commands to quickly add records'**
  String get voiceCommandsDescription;

  /// No description provided for @premiumFeature.
  ///
  /// In en, this message translates to:
  /// **'Premium Feature'**
  String get premiumFeature;

  /// No description provided for @premiumRequired.
  ///
  /// In en, this message translates to:
  /// **'This feature requires a premium subscription'**
  String get premiumRequired;

  /// No description provided for @upgradeNow.
  ///
  /// In en, this message translates to:
  /// **'Upgrade Now'**
  String get upgradeNow;

  /// No description provided for @notNow.
  ///
  /// In en, this message translates to:
  /// **'Not Now'**
  String get notNow;

  /// No description provided for @tryFree.
  ///
  /// In en, this message translates to:
  /// **'Try Free for 7 Days'**
  String get tryFree;

  /// No description provided for @familySharing.
  ///
  /// In en, this message translates to:
  /// **'Family Sharing'**
  String get familySharing;

  /// No description provided for @familySharingDescription.
  ///
  /// In en, this message translates to:
  /// **'Share with up to 5 family members'**
  String get familySharingDescription;

  /// No description provided for @unlimitedVehicles.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Vehicles'**
  String get unlimitedVehicles;

  /// No description provided for @adFreeExperience.
  ///
  /// In en, this message translates to:
  /// **'Ad-Free Experience'**
  String get adFreeExperience;

  /// No description provided for @enhancedAnalytics.
  ///
  /// In en, this message translates to:
  /// **'Enhanced Analytics'**
  String get enhancedAnalytics;

  /// No description provided for @prioritySupport.
  ///
  /// In en, this message translates to:
  /// **'Priority Support'**
  String get prioritySupport;

  /// No description provided for @trialStarted.
  ///
  /// In en, this message translates to:
  /// **'Trial started successfully'**
  String get trialStarted;

  /// No description provided for @trialFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to start trial'**
  String get trialFailed;

  /// No description provided for @trialExpires.
  ///
  /// In en, this message translates to:
  /// **'Trial expires on {date}'**
  String trialExpires(String date);

  /// No description provided for @daysRemaining.
  ///
  /// In en, this message translates to:
  /// **'{days} days remaining'**
  String daysRemaining(int days);

  /// No description provided for @manageSubscription.
  ///
  /// In en, this message translates to:
  /// **'Manage Subscription'**
  String get manageSubscription;

  /// No description provided for @subscriptionExpires.
  ///
  /// In en, this message translates to:
  /// **'Subscription expires on {date}'**
  String subscriptionExpires(String date);

  /// No description provided for @voiceCommandsHelp.
  ///
  /// In en, this message translates to:
  /// **'Voice Commands Help'**
  String get voiceCommandsHelp;

  /// No description provided for @voiceCommandsHelpDescription.
  ///
  /// In en, this message translates to:
  /// **'Try these example commands with the voice input feature:'**
  String get voiceCommandsHelpDescription;

  /// No description provided for @addOilChange.
  ///
  /// In en, this message translates to:
  /// **'Add Oil Change'**
  String get addOilChange;

  /// No description provided for @productNotAvailable.
  ///
  /// In en, this message translates to:
  /// **'Product not available'**
  String get productNotAvailable;

  /// No description provided for @checkConnectionAndTryAgain.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again'**
  String get checkConnectionAndTryAgain;

  /// No description provided for @premium.
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get premium;

  /// No description provided for @upgradeToPremiun.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to Premium'**
  String get upgradeToPremiun;

  /// No description provided for @subscriptionDetails.
  ///
  /// In en, this message translates to:
  /// **'Subscription Details'**
  String get subscriptionDetails;

  /// No description provided for @premiumSubscription.
  ///
  /// In en, this message translates to:
  /// **'Premium Subscription'**
  String get premiumSubscription;

  /// No description provided for @freeVersion.
  ///
  /// In en, this message translates to:
  /// **'Free Version'**
  String get freeVersion;

  /// No description provided for @youHaveActivePremium.
  ///
  /// In en, this message translates to:
  /// **'You have an active premium subscription'**
  String get youHaveActivePremium;

  /// No description provided for @upgradeToRemoveAds.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to premium to remove ads and unlock all features'**
  String get upgradeToRemoveAds;

  /// No description provided for @upgradeToPremium.
  ///
  /// In en, this message translates to:
  /// **'Upgrade to Premium'**
  String get upgradeToPremium;

  /// No description provided for @viewCarDetails.
  ///
  /// In en, this message translates to:
  /// **'View Car Details'**
  String get viewCarDetails;

  /// No description provided for @unknownCommand.
  ///
  /// In en, this message translates to:
  /// **'Unknown Command'**
  String get unknownCommand;

  /// No description provided for @youSaid.
  ///
  /// In en, this message translates to:
  /// **'You said'**
  String get youSaid;

  /// No description provided for @extractedInformation.
  ///
  /// In en, this message translates to:
  /// **'Extracted Information'**
  String get extractedInformation;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @couldNotUnderstandCommand.
  ///
  /// In en, this message translates to:
  /// **'Could not understand the voice command. Please try again.'**
  String get couldNotUnderstandCommand;

  /// No description provided for @noSpeechDetected.
  ///
  /// In en, this message translates to:
  /// **'No speech detected. Please try again.'**
  String get noSpeechDetected;

  /// No description provided for @microphonePermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Microphone permission is required to use voice commands.'**
  String get microphonePermissionDenied;

  /// No description provided for @cloudBackup.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup'**
  String get cloudBackup;

  /// No description provided for @cloudBackupPremiumFeature.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup is a Premium Feature'**
  String get cloudBackupPremiumFeature;

  /// No description provided for @cloudBackupDescription.
  ///
  /// In en, this message translates to:
  /// **'Securely backup and restore your vehicle data, oil change history, and settings to the cloud.'**
  String get cloudBackupDescription;

  /// No description provided for @backupSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Backup created successfully'**
  String get backupSuccessful;

  /// No description provided for @backupFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to create backup'**
  String get backupFailed;

  /// No description provided for @confirmRestore.
  ///
  /// In en, this message translates to:
  /// **'Confirm Restore'**
  String get confirmRestore;

  /// No description provided for @firstCarWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Let\'s add your first vehicle to start tracking oil changes and maintenance!'**
  String get firstCarWelcomeMessage;

  /// No description provided for @firstCarStepIndicator.
  ///
  /// In en, this message translates to:
  /// **'Step 1 of 1: Add Your First Car'**
  String get firstCarStepIndicator;

  /// No description provided for @quickTips.
  ///
  /// In en, this message translates to:
  /// **'Quick Tips:'**
  String get quickTips;

  /// No description provided for @tipAddPhotos.
  ///
  /// In en, this message translates to:
  /// **'Add photos to easily identify your car'**
  String get tipAddPhotos;

  /// No description provided for @tipEnterMileage.
  ///
  /// In en, this message translates to:
  /// **'Enter current mileage for accurate tracking'**
  String get tipEnterMileage;

  /// No description provided for @tipSetIntervals.
  ///
  /// In en, this message translates to:
  /// **'Set oil change intervals based on your driving habits'**
  String get tipSetIntervals;

  /// No description provided for @congratulations.
  ///
  /// In en, this message translates to:
  /// **'🎉 Congratulations!'**
  String get congratulations;

  /// No description provided for @firstCarSuccessMessage.
  ///
  /// In en, this message translates to:
  /// **'You\'ve successfully added your first car! You\'re now ready to track oil changes and maintenance.'**
  String get firstCarSuccessMessage;

  /// No description provided for @whatsNext.
  ///
  /// In en, this message translates to:
  /// **'What\'s Next:'**
  String get whatsNext;

  /// No description provided for @nextStepRecordOil.
  ///
  /// In en, this message translates to:
  /// **'Record your next oil change'**
  String get nextStepRecordOil;

  /// No description provided for @nextStepReminders.
  ///
  /// In en, this message translates to:
  /// **'Get reminders for maintenance'**
  String get nextStepReminders;

  /// No description provided for @nextStepTrackHealth.
  ///
  /// In en, this message translates to:
  /// **'Track your vehicle\'s health'**
  String get nextStepTrackHealth;

  /// No description provided for @tapToAddPhotos.
  ///
  /// In en, this message translates to:
  /// **'Tap above to add photos of your car'**
  String get tapToAddPhotos;

  /// No description provided for @dashboardWelcomeSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Start tracking your vehicle\'s oil changes and maintenance with ease'**
  String get dashboardWelcomeSubtitle;

  /// No description provided for @whatYoullGet.
  ///
  /// In en, this message translates to:
  /// **'What you\'ll get:'**
  String get whatYoullGet;

  /// No description provided for @smartReminders.
  ///
  /// In en, this message translates to:
  /// **'Smart Reminders'**
  String get smartReminders;

  /// No description provided for @smartRemindersDesc.
  ///
  /// In en, this message translates to:
  /// **'Never miss an oil change with intelligent notifications'**
  String get smartRemindersDesc;

  /// No description provided for @trackHistory.
  ///
  /// In en, this message translates to:
  /// **'Track History'**
  String get trackHistory;

  /// No description provided for @trackHistoryDesc.
  ///
  /// In en, this message translates to:
  /// **'Monitor your vehicle\'s maintenance history and costs'**
  String get trackHistoryDesc;

  /// No description provided for @mileageTracking.
  ///
  /// In en, this message translates to:
  /// **'Mileage Tracking'**
  String get mileageTracking;

  /// No description provided for @mileageTrackingDesc.
  ///
  /// In en, this message translates to:
  /// **'Keep track of your vehicle\'s mileage automatically'**
  String get mileageTrackingDesc;

  /// No description provided for @restoreWarning.
  ///
  /// In en, this message translates to:
  /// **'This will replace all your current data with the backup data. This action cannot be undone.'**
  String get restoreWarning;

  /// No description provided for @restore.
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get restore;

  /// No description provided for @restoreSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Data restored successfully'**
  String get restoreSuccessful;

  /// No description provided for @restoreFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to restore data'**
  String get restoreFailed;

  /// No description provided for @automaticBackups.
  ///
  /// In en, this message translates to:
  /// **'Automatic Backups'**
  String get automaticBackups;

  /// No description provided for @automaticBackupsDescription.
  ///
  /// In en, this message translates to:
  /// **'Automatically backup your data daily to ensure you never lose your records.'**
  String get automaticBackupsDescription;

  /// No description provided for @enableAutomaticBackups.
  ///
  /// In en, this message translates to:
  /// **'Enable automatic backups'**
  String get enableAutomaticBackups;

  /// No description provided for @automaticBackupsEnabled.
  ///
  /// In en, this message translates to:
  /// **'Automatic backups enabled'**
  String get automaticBackupsEnabled;

  /// No description provided for @automaticBackupsDisabled.
  ///
  /// In en, this message translates to:
  /// **'Automatic backups disabled'**
  String get automaticBackupsDisabled;

  /// No description provided for @manualBackup.
  ///
  /// In en, this message translates to:
  /// **'Manual Backup'**
  String get manualBackup;

  /// No description provided for @createBackup.
  ///
  /// In en, this message translates to:
  /// **'Create Backup'**
  String get createBackup;

  /// No description provided for @backupHistory.
  ///
  /// In en, this message translates to:
  /// **'Backup History'**
  String get backupHistory;

  /// No description provided for @noBackupsFound.
  ///
  /// In en, this message translates to:
  /// **'No backups found'**
  String get noBackupsFound;

  /// No description provided for @manageCloudBackups.
  ///
  /// In en, this message translates to:
  /// **'Manage your cloud backups and restore data'**
  String get manageCloudBackups;

  /// No description provided for @chatAssistantTitle.
  ///
  /// In en, this message translates to:
  /// **'Car Assistant 🔧'**
  String get chatAssistantTitle;

  /// No description provided for @clearChatTooltip.
  ///
  /// In en, this message translates to:
  /// **'Clear chat'**
  String get clearChatTooltip;

  /// No description provided for @chatEmptyTitle.
  ///
  /// In en, this message translates to:
  /// **'Ask about any problem in your car'**
  String get chatEmptyTitle;

  /// No description provided for @chatEmptySubtitle.
  ///
  /// In en, this message translates to:
  /// **'I will help with diagnostics and solutions'**
  String get chatEmptySubtitle;

  /// No description provided for @assistantThinking.
  ///
  /// In en, this message translates to:
  /// **'Assistant is thinking...'**
  String get assistantThinking;

  /// No description provided for @chatFaqTitle.
  ///
  /// In en, this message translates to:
  /// **'FAQs:'**
  String get chatFaqTitle;

  /// No description provided for @chatPromptEngineNoStart.
  ///
  /// In en, this message translates to:
  /// **'Engine won\'t start'**
  String get chatPromptEngineNoStart;

  /// No description provided for @chatPromptAbsLight.
  ///
  /// In en, this message translates to:
  /// **'ABS light is on'**
  String get chatPromptAbsLight;

  /// No description provided for @chatPromptOilLight.
  ///
  /// In en, this message translates to:
  /// **'Oil warning light'**
  String get chatPromptOilLight;

  /// No description provided for @chatPromptEngineNoise.
  ///
  /// In en, this message translates to:
  /// **'Strange engine noise'**
  String get chatPromptEngineNoise;

  /// No description provided for @chatPromptVibration.
  ///
  /// In en, this message translates to:
  /// **'Car vibrates while driving'**
  String get chatPromptVibration;

  /// No description provided for @chatPromptBrakes.
  ///
  /// In en, this message translates to:
  /// **'Brake problem'**
  String get chatPromptBrakes;

  /// No description provided for @chatPromptHighTemp.
  ///
  /// In en, this message translates to:
  /// **'Engine temperature high'**
  String get chatPromptHighTemp;

  /// No description provided for @chatPromptBattery.
  ///
  /// In en, this message translates to:
  /// **'Battery issue'**
  String get chatPromptBattery;

  /// No description provided for @premiumRemoveAdsDescription.
  ///
  /// In en, this message translates to:
  /// **'Get access to all premium features and remove ads'**
  String get premiumRemoveAdsDescription;

  /// No description provided for @monthlyPremium.
  ///
  /// In en, this message translates to:
  /// **'Monthly Premium'**
  String get monthlyPremium;

  /// No description provided for @annualPremium.
  ///
  /// In en, this message translates to:
  /// **'Annual Premium'**
  String get annualPremium;

  /// No description provided for @basePrice.
  ///
  /// In en, this message translates to:
  /// **'Base price: {price}'**
  String basePrice(String price);

  /// No description provided for @monthlyEquivalent.
  ///
  /// In en, this message translates to:
  /// **'Monthly equivalent: {price}'**
  String monthlyEquivalent(String price);

  /// No description provided for @premiumFeaturesTitle.
  ///
  /// In en, this message translates to:
  /// **'Premium Features'**
  String get premiumFeaturesTitle;

  /// No description provided for @featureVoiceCommands.
  ///
  /// In en, this message translates to:
  /// **'Voice commands for quick entry'**
  String get featureVoiceCommands;

  /// No description provided for @featureUnlimitedVehicles.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Vehicles'**
  String get featureUnlimitedVehicles;

  /// No description provided for @featureCloudBackup.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup & Sync'**
  String get featureCloudBackup;

  /// No description provided for @featureAdvancedAnalytics.
  ///
  /// In en, this message translates to:
  /// **'Advanced analytics'**
  String get featureAdvancedAnalytics;

  /// No description provided for @freeTrialTitle.
  ///
  /// In en, this message translates to:
  /// **'Free Trial'**
  String get freeTrialTitle;

  /// No description provided for @tryPremiumFeaturesFree.
  ///
  /// In en, this message translates to:
  /// **'Try Premium Features Free'**
  String get tryPremiumFeaturesFree;

  /// No description provided for @freeTrialDescription.
  ///
  /// In en, this message translates to:
  /// **'Enjoy a 7-day free trial with no commitment. Cancel anytime.'**
  String get freeTrialDescription;

  /// No description provided for @selectPlanToTry.
  ///
  /// In en, this message translates to:
  /// **'Select a plan to try:'**
  String get selectPlanToTry;

  /// No description provided for @freeTrialAgreement.
  ///
  /// In en, this message translates to:
  /// **'I understand that after the trial period ends, I can choose to subscribe if I wish to continue using premium features.'**
  String get freeTrialAgreement;

  /// No description provided for @startFreeTrial.
  ///
  /// In en, this message translates to:
  /// **'Start Free Trial'**
  String get startFreeTrial;

  /// No description provided for @freeTrialTerms.
  ///
  /// In en, this message translates to:
  /// **'By starting a free trial, you agree to our Terms of Service and Privacy Policy. The trial will end automatically after 7 days with no charges. To continue using premium features, you can manually subscribe.'**
  String get freeTrialTerms;

  /// No description provided for @loadingPrice.
  ///
  /// In en, this message translates to:
  /// **'Loading price...'**
  String get loadingPrice;

  /// No description provided for @trialStartedSuccess.
  ///
  /// In en, this message translates to:
  /// **'Trial started successfully!'**
  String get trialStartedSuccess;

  /// No description provided for @trialStartFailed.
  ///
  /// In en, this message translates to:
  /// **'Failed to start trial. Please try again.'**
  String get trialStartFailed;

  /// No description provided for @freeForSevenDaysThen.
  ///
  /// In en, this message translates to:
  /// **'Free for 7 days. After trial: {price} / {perMonth} (manual subscription required)'**
  String freeForSevenDaysThen(String price, String perMonth);

  /// No description provided for @subscriptionPolicy.
  ///
  /// In en, this message translates to:
  /// **'Subscription Policy'**
  String get subscriptionPolicy;

  /// No description provided for @viewSubscriptionPolicy.
  ///
  /// In en, this message translates to:
  /// **'View subscription details'**
  String get viewSubscriptionPolicy;

  /// No description provided for @refundPolicy.
  ///
  /// In en, this message translates to:
  /// **'Refund Policy'**
  String get refundPolicy;

  /// No description provided for @viewRefundPolicy.
  ///
  /// In en, this message translates to:
  /// **'View refund policy'**
  String get viewRefundPolicy;

  /// No description provided for @aiAssistant.
  ///
  /// In en, this message translates to:
  /// **'AI Assistant'**
  String get aiAssistant;

  /// No description provided for @aiChat.
  ///
  /// In en, this message translates to:
  /// **'AI Chat'**
  String get aiChat;

  /// No description provided for @aiFeatures.
  ///
  /// In en, this message translates to:
  /// **'AI Features'**
  String get aiFeatures;

  /// No description provided for @aiFeaturesPremiumDescription.
  ///
  /// In en, this message translates to:
  /// **'Voice commands and AI chat assistant are premium features that help you manage your vehicle maintenance more efficiently.'**
  String get aiFeaturesPremiumDescription;

  /// No description provided for @voiceCommandsWelcome.
  ///
  /// In en, this message translates to:
  /// **'Voice Commands'**
  String get voiceCommandsWelcome;

  /// No description provided for @voiceCommandsSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Use your voice to quickly record maintenance and oil changes'**
  String get voiceCommandsSubtitle;

  /// No description provided for @recordOilChangeVoice.
  ///
  /// In en, this message translates to:
  /// **'Record oil changes using voice commands'**
  String get recordOilChangeVoice;

  /// No description provided for @recordMaintenanceVoice.
  ///
  /// In en, this message translates to:
  /// **'Record maintenance tasks using voice commands'**
  String get recordMaintenanceVoice;

  /// No description provided for @featureLocked.
  ///
  /// In en, this message translates to:
  /// **'{featureName} Locked'**
  String featureLocked(String featureName);

  /// No description provided for @featureRequiresPremium.
  ///
  /// In en, this message translates to:
  /// **'This feature requires a premium subscription to unlock.'**
  String get featureRequiresPremium;

  /// No description provided for @premiumFeatures.
  ///
  /// In en, this message translates to:
  /// **'Premium Features'**
  String get premiumFeatures;

  /// No description provided for @featureAiChat.
  ///
  /// In en, this message translates to:
  /// **'AI Chat Assistant'**
  String get featureAiChat;

  /// No description provided for @featureAdFree.
  ///
  /// In en, this message translates to:
  /// **'Ad-Free Experience'**
  String get featureAdFree;

  /// No description provided for @verificationEmailSent.
  ///
  /// In en, this message translates to:
  /// **'Verification email sent'**
  String get verificationEmailSent;

  /// No description provided for @errorSendingVerificationEmail.
  ///
  /// In en, this message translates to:
  /// **'Error sending verification email'**
  String get errorSendingVerificationEmail;

  /// No description provided for @verifyEmail.
  ///
  /// In en, this message translates to:
  /// **'Verify Email'**
  String get verifyEmail;

  /// No description provided for @viewCar.
  ///
  /// In en, this message translates to:
  /// **'View Car'**
  String get viewCar;

  /// No description provided for @noParametersDetected.
  ///
  /// In en, this message translates to:
  /// **'No parameters detected'**
  String get noParametersDetected;

  /// No description provided for @notSpecified.
  ///
  /// In en, this message translates to:
  /// **'Not specified'**
  String get notSpecified;

  /// No description provided for @restartApp.
  ///
  /// In en, this message translates to:
  /// **'Restart App'**
  String get restartApp;

  /// No description provided for @adminNotifications.
  ///
  /// In en, this message translates to:
  /// **'Admin Notifications'**
  String get adminNotifications;

  /// No description provided for @resetAllTokens.
  ///
  /// In en, this message translates to:
  /// **'Reset All Tokens'**
  String get resetAllTokens;

  /// No description provided for @sendToAllUsers.
  ///
  /// In en, this message translates to:
  /// **'Send to All Users'**
  String get sendToAllUsers;

  /// No description provided for @notificationType.
  ///
  /// In en, this message translates to:
  /// **'Notification Type'**
  String get notificationType;

  /// No description provided for @promotional.
  ///
  /// In en, this message translates to:
  /// **'Promotional'**
  String get promotional;

  /// No description provided for @oilChange.
  ///
  /// In en, this message translates to:
  /// **'Oil Change'**
  String get oilChange;

  /// No description provided for @notificationTitle.
  ///
  /// In en, this message translates to:
  /// **'Notification Title'**
  String get notificationTitle;

  /// No description provided for @notificationBody.
  ///
  /// In en, this message translates to:
  /// **'Notification Body'**
  String get notificationBody;

  /// No description provided for @enterNotificationTitle.
  ///
  /// In en, this message translates to:
  /// **'Enter notification title'**
  String get enterNotificationTitle;

  /// No description provided for @enterNotificationMessage.
  ///
  /// In en, this message translates to:
  /// **'Enter notification message'**
  String get enterNotificationMessage;

  /// No description provided for @titleBodyRequired.
  ///
  /// In en, this message translates to:
  /// **'Title and body are required'**
  String get titleBodyRequired;

  /// No description provided for @accessDeniedAdmin.
  ///
  /// In en, this message translates to:
  /// **'Access denied. Admin privileges required.'**
  String get accessDeniedAdmin;

  /// No description provided for @processing.
  ///
  /// In en, this message translates to:
  /// **'Processing...'**
  String get processing;

  /// No description provided for @verificationResults.
  ///
  /// In en, this message translates to:
  /// **'Verification Results'**
  String get verificationResults;

  /// No description provided for @googleAccountDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Manage Your Google Account'**
  String get googleAccountDialogTitle;

  /// No description provided for @googleAccountDialogDescription.
  ///
  /// In en, this message translates to:
  /// **'To manage your Google account:\n\n1. Go to Settings > Google\n2. Select your account\n3. Manage your preferences'**
  String get googleAccountDialogDescription;

  /// No description provided for @goBack.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBack;

  /// No description provided for @backToMaintenanceList.
  ///
  /// In en, this message translates to:
  /// **'Back to Maintenance List'**
  String get backToMaintenanceList;

  /// No description provided for @listening.
  ///
  /// In en, this message translates to:
  /// **'Listening...'**
  String get listening;

  /// No description provided for @noVoiceDetected.
  ///
  /// In en, this message translates to:
  /// **'No voice detected.'**
  String get noVoiceDetected;

  /// No description provided for @failedToStartRecording.
  ///
  /// In en, this message translates to:
  /// **'Failed to start recording: {error}'**
  String failedToStartRecording(Object error);

  /// No description provided for @errorMissingCarId.
  ///
  /// In en, this message translates to:
  /// **'Error: Car ID is missing.'**
  String get errorMissingCarId;

  /// No description provided for @developerSettings.
  ///
  /// In en, this message translates to:
  /// **'Developer Settings'**
  String get developerSettings;

  /// No description provided for @premiumFeatureDescription.
  ///
  /// In en, this message translates to:
  /// **'Free users are limited to 3 vehicles. Upgrade to Premium for unlimited vehicles.'**
  String get premiumFeatureDescription;

  /// No description provided for @voiceInputFeature1.
  ///
  /// In en, this message translates to:
  /// **'Voice input for quick data entry'**
  String get voiceInputFeature1;

  /// No description provided for @voiceInputFeature2.
  ///
  /// In en, this message translates to:
  /// **'Ad-free experience'**
  String get voiceInputFeature2;

  /// No description provided for @voiceInputFeature3.
  ///
  /// In en, this message translates to:
  /// **'Unlimited vehicles'**
  String get voiceInputFeature3;

  /// No description provided for @voiceInputFeature4.
  ///
  /// In en, this message translates to:
  /// **'Enhanced analytics and insights'**
  String get voiceInputFeature4;

  /// No description provided for @voiceInputFeature5.
  ///
  /// In en, this message translates to:
  /// **'Cloud backup and sync'**
  String get voiceInputFeature5;

  /// No description provided for @resettingAndRefreshingTokens.
  ///
  /// In en, this message translates to:
  /// **'Resetting and refreshing all tokens...'**
  String get resettingAndRefreshingTokens;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return SAr();
    case 'en':
      return SEn();
  }

  throw FlutterError(
      'S.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
