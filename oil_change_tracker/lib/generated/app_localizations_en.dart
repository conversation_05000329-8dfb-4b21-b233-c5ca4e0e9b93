// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Oil Plus';

  @override
  String get welcomeMessage =>
      'Track your vehicle\'s oil changes and maintenance easily';

  @override
  String get login => 'Login';

  @override
  String get signup => 'Sign Up';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get name => 'Name';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordTooShort => 'Password must be at least 6 characters';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get nameRequired => 'Name is required';

  @override
  String get signInWithGoogle => 'Sign in with Google';

  @override
  String get noAccountSignUp => 'Don\'t have an account? Sign up now';

  @override
  String get haveAccountLogin => 'Already have an account? Log in';

  @override
  String get signUpSuccess => 'Account created successfully! Please log in.';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get error => 'Error';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get loading => 'Loading...';

  @override
  String get confirmPasswordRequired => 'Please confirm your password';

  @override
  String get alreadyHaveAccount => 'Already have an account? Log in';

  @override
  String get welcomeBack => 'Welcome to your dashboard';

  @override
  String get oilConsumption => 'Oil Consumption';

  @override
  String get used => 'Used';

  @override
  String kmUntilNextOilChange(Object km) {
    return '$km km until next oil change';
  }

  @override
  String get oilChangeOverdue => 'Oil Change Overdue!';

  @override
  String get recordOilChange => 'Record Oil Change';

  @override
  String nextOilChange(String mileage) {
    return 'Next Oil Change';
  }

  @override
  String get overdue => 'Overdue';

  @override
  String get close => 'Close';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get notifications => 'Notifications';

  @override
  String get reminders => 'Reminders';

  @override
  String get oilChangeReminders => 'Oil Change Reminders';

  @override
  String get oilChangeRemindersDescription =>
      'Receive notifications when your car needs an oil change';

  @override
  String get maintenanceReminders => 'Maintenance Reminders';

  @override
  String get maintenanceRemindersDescription =>
      'Receive notifications about scheduled maintenance';

  @override
  String get mileageReminders => 'Mileage Reminders';

  @override
  String get mileageRemindersDescription =>
      'Receive notifications about mileage updates';

  @override
  String get notificationSettings => 'Manage notification preferences';

  @override
  String get about => 'About';

  @override
  String get aboutApp => 'About Oil Plus';

  @override
  String get aboutDescription =>
      'Oil Plus helps you track your vehicle\'s maintenance schedule and oil changes. Keep your vehicle healthy with timely reminders and a detailed service record.';

  @override
  String get personalInformation => 'Personal Information';

  @override
  String get security => 'Security';

  @override
  String get appSettings => 'App Settings';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get changePassword => 'Change Password';

  @override
  String get signOut => 'Sign Out';

  @override
  String get signOutConfirmation => 'Are you sure you want to sign out?';

  @override
  String get unknownUser => 'Unknown User';

  @override
  String get comingSoon => 'Coming Soon!';

  @override
  String get recordMaintenance => 'Record Maintenance';

  @override
  String get selectCar => 'Select Car';

  @override
  String get maintenanceType => 'Maintenance Type';

  @override
  String get currentMileage => 'Current Mileage';

  @override
  String get date => 'Date';

  @override
  String get cost => 'Cost';

  @override
  String get serviceProvider => 'Service Provider';

  @override
  String get notes => 'Notes';

  @override
  String get optional => 'Optional';

  @override
  String get pleaseSelectCar => 'Please select a car';

  @override
  String get pleaseEnterMileage => 'Please enter current mileage';

  @override
  String get pleaseEnterCost => 'Please enter cost';

  @override
  String get saveMaintenance => 'Save Maintenance';

  @override
  String get maintenanceAddedSuccess => 'Maintenance record added successfully';

  @override
  String get noCarsFound => 'No cars found';

  @override
  String get addCarFirst => 'Add a car first to record maintenance';

  @override
  String get addCar => 'Add Car';

  @override
  String get errorLoadingCars => 'Error loading cars';

  @override
  String get retry => 'Retry';

  @override
  String get generalService => 'General Service';

  @override
  String get brakeService => 'Brake Service';

  @override
  String get engineService => 'Engine Service';

  @override
  String get transmissionService => 'Transmission Service';

  @override
  String get tireService => 'Tire Service';

  @override
  String get batteryService => 'Battery Service';

  @override
  String get airConditioning => 'Air Conditioning';

  @override
  String get electricalSystem => 'Electrical System';

  @override
  String get suspension => 'Suspension';

  @override
  String get exhaustSystem => 'Exhaust System';

  @override
  String get fuelSystem => 'Fuel System';

  @override
  String get coolingSystem => 'Cooling System';

  @override
  String get regularMaintenance => 'Regular Maintenance';

  @override
  String get other => 'Other';

  @override
  String get myCars => 'My Cars';

  @override
  String get noCarsAddedYet => 'No cars added yet';

  @override
  String get addFirstCarMessage =>
      'Add your first car to start tracking oil changes';

  @override
  String get totalCars => 'Total Cars';

  @override
  String get needOilChange => 'Need Oil Change';

  @override
  String get carsDueForOilChange => 'Cars due for oil change';

  @override
  String get viewAllCars => 'View all cars';

  @override
  String get noUpcomingMaintenance => 'No upcoming maintenance';

  @override
  String get allCarsUpToDate =>
      'All cars are up to date with their maintenance schedule';

  @override
  String nextOilChangeInDays(Object days) {
    return 'Next oil change in $days days';
  }

  @override
  String overdueDays(Object days) {
    return 'Overdue by $days days';
  }

  @override
  String daysUntilNextChange(int days) {
    return '$days days until next change';
  }

  @override
  String get viewAll => 'View All';

  @override
  String get goodMorning => 'Good Morning';

  @override
  String get goodAfternoon => 'Good Afternoon';

  @override
  String get goodEvening => 'Good Evening';

  @override
  String get subscriptions => 'Subscriptions';

  @override
  String get activeSubscription => 'Active Subscription';

  @override
  String get billingPeriod => 'Billing Period';

  @override
  String get monthly => 'Monthly';

  @override
  String get yearly => 'Yearly';

  @override
  String get save30Percent => 'SAVE 30%';

  @override
  String get perMonth => 'per month';

  @override
  String get perYear => 'per year';

  @override
  String get subscribe => 'Subscribe';

  @override
  String get popular => 'POPULAR';

  @override
  String get restorePurchases => 'Restore Purchases';

  @override
  String get cancelSubscription => 'Cancel Subscription';

  @override
  String get cancelSubscriptionTitle => 'Cancel Subscription?';

  @override
  String get cancelSubscriptionMessage =>
      'Your subscription will remain active until the end of the current billing period.';

  @override
  String get addNewCar => 'Add New Car';

  @override
  String get enterCarDetails => 'Please fill in your car details';

  @override
  String get saveSuccess => 'Saved Successfully';

  @override
  String get pleaseSignIn => 'Please sign in to view the dashboard';

  @override
  String get quickActions => 'Quick Actions';

  @override
  String get addMaintenance => 'Add Maintenance';

  @override
  String get update => 'Update';

  @override
  String get updateMileage => 'Update Mileage';

  @override
  String get mileageUpdated => 'Mileage updated successfully';

  @override
  String get invalidMileage => 'Invalid mileage value';

  @override
  String get mileageRequired => 'Mileage is required';

  @override
  String get mileageMustBeNumber => 'Mileage must be a number';

  @override
  String mileageMustBeGreaterThan(String currentMileage) {
    return 'Mileage must be greater than $currentMileage';
  }

  @override
  String get mileageTooHigh =>
      'Mileage seems too high, please check your input';

  @override
  String get lastOilChange => 'Last oil change at';

  @override
  String get kilometersElapsed => 'Kilometers elapsed';

  @override
  String get appTitle => 'Oil Plus';

  @override
  String get welcomeText => 'Welcome to Oil Plus';

  @override
  String get signIn => 'Sign In';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get sendResetLink => 'Send Reset Link';

  @override
  String get noAccount => 'Don\'t have an account?';

  @override
  String get haveAccount => 'Already have an account?';

  @override
  String get cars => 'Cars';

  @override
  String get logout => 'Logout';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get darkTheme => 'Dark Theme';

  @override
  String get lightTheme => 'Light Theme';

  @override
  String get themeSettings => 'Theme Settings';

  @override
  String get systemTheme => 'System Theme';

  @override
  String get english => 'English';

  @override
  String get arabic => 'Arabic';

  @override
  String get enable => 'Enable';

  @override
  String get disable => 'Disable';

  @override
  String get version => 'Version';

  @override
  String get emailInvalid => 'Please enter a valid email';

  @override
  String get passwordLength => 'Password must be at least 6 characters';

  @override
  String get passwordMatch => 'Passwords do not match';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get theme => 'Theme';

  @override
  String get phoneInvalid => 'Please enter a valid phone number';

  @override
  String get errorOccurred => 'An error occurred';

  @override
  String get success => 'Success';

  @override
  String get continueAction => 'Continue';

  @override
  String get done => 'Done';

  @override
  String get changeProfilePhoto => 'Change Photo';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get removePhoto => 'Remove Photo';

  @override
  String get updateProfile => 'Update Profile';

  @override
  String get errorUpdatingProfile => 'Error updating profile';

  @override
  String get profileUpdated => 'Profile updated successfully';

  @override
  String get car => 'Car';

  @override
  String get deleteCar => 'Delete Car';

  @override
  String get deleteCarConfirmation =>
      'Are you sure you want to delete this car? This action cannot be undone.';

  @override
  String get delete => 'Delete';

  @override
  String get oilChangeStatus => 'Oil Change Status';

  @override
  String get lastOilChangeMileage => 'Last Oil Change Mileage';

  @override
  String inKilometers(Object km) {
    return 'in $km km';
  }

  @override
  String get carDetails => 'Car Details';

  @override
  String get make => 'Make';

  @override
  String get model => 'Model';

  @override
  String get year => 'year';

  @override
  String get maintenanceIntervals => 'Maintenance Intervals';

  @override
  String get oilChangeInterval => 'Oil Change Interval';

  @override
  String get maintenanceHistory => 'Maintenance History';

  @override
  String get oilChanges => 'Oil Changes';

  @override
  String get errorLoadingCarDetails => 'Error loading car details';

  @override
  String get errorLastMileageGreater =>
      'Last oil change mileage cannot be greater than current mileage';

  @override
  String get errorAddingCar => 'Error adding car';

  @override
  String get pleaseEnterCarMake => 'Please enter car make';

  @override
  String get pleaseEnterCarModel => 'Please enter car model';

  @override
  String get pleaseEnterCarYear => 'Please enter car year';

  @override
  String get pleaseEnterValidYear => 'Please enter a valid year';

  @override
  String get yearRangeError =>
      'Please enter a valid year between 1900 and current year';

  @override
  String get pleaseEnterLastOilChangeMileage =>
      'Please enter last oil change mileage';

  @override
  String get pleaseEnterOilEndurance =>
      'Please enter the oil\'s endurance in km (e.g., 5000)';

  @override
  String get distance => 'Distance (km)';

  @override
  String get timeMonths => 'Time (months)';

  @override
  String get months => 'months';

  @override
  String get saveCar => 'Save Car';

  @override
  String get saveChanges => 'Save Changes';

  @override
  String get editCar => 'Edit Car';

  @override
  String get errorUpdatingCar => 'Error updating car';

  @override
  String get upcomingMaintenance => 'Upcoming Maintenance';

  @override
  String kmDriven(Object km) {
    return '$km km driven';
  }

  @override
  String kmRemaining(int km) {
    return 'km remaining $km';
  }

  @override
  String get oilChangeProgress => 'Oil Change Progress';

  @override
  String get pleaseEnterCurrentMileage => 'Please enter current mileage';

  @override
  String get oilType => 'Oil Type';

  @override
  String get oilQuantity => 'Oil Quantity (L)';

  @override
  String get oilEnduranceKm => 'Oil Endurance (km)';

  @override
  String get pleaseEnterOilType => 'Please enter oil type';

  @override
  String get pleaseEnterOilQuantity => 'Please enter oil quantity';

  @override
  String get filterType => 'Filter Type';

  @override
  String get pleaseEnterFilterType => 'Please enter filter type';

  @override
  String get oilChangeDate => 'Oil Change Date';

  @override
  String get save => 'Save';

  @override
  String get driven => 'Driven';

  @override
  String get remaining => 'Remaining';

  @override
  String get noOilChangesRecorded => 'No oil changes recorded yet';

  @override
  String get tapPlusToAddOilChange => 'Tap + to add an oil change';

  @override
  String get mileage => 'Mileage';

  @override
  String get deleteOilChange => 'Delete Oil Change';

  @override
  String get deleteOilChangeConfirmation =>
      'Are you sure you want to delete this oil change record?';

  @override
  String get oilChangeDeleted => 'Oil change deleted';

  @override
  String get vehicleStatistics => 'Vehicle Statistics';

  @override
  String get averageMileage => 'Average Mileage';

  @override
  String get allGood => 'All Good';

  @override
  String get noMaintenanceAlerts => 'No maintenance alerts';

  @override
  String get noUpcomingReminders => 'No upcoming reminders';

  @override
  String get allMaintenanceUpToDate => 'All maintenance up to date';

  @override
  String oilChangeDueInDays(Object days) {
    return 'Oil change due in $days days';
  }

  @override
  String nextOilChangeStatus(Object days, Object mileage) {
    return 'Next Oil Change: $mileage km or $days days';
  }

  @override
  String currentMileageStatus(Object mileage) {
    return 'Current Mileage: $mileage km';
  }

  @override
  String get oilChangeHistory => 'Oil Change History';

  @override
  String get noOilChangesFound => 'No oil change records found';

  @override
  String get recordOilChangeFirst =>
      'Record your first oil change to start tracking maintenance history';

  @override
  String get oilChangeDetails => 'Oil Change Details';

  @override
  String get errorLoadingOilChangeDetails => 'Error loading oil change details';

  @override
  String get overview => 'Overview';

  @override
  String get maintenance => 'Maintenance';

  @override
  String get noMaintenanceRecords => 'No maintenance records found';

  @override
  String get maintenanceDescription => 'Description';

  @override
  String get maintenanceDate => 'Date';

  @override
  String get maintenanceCost => 'Cost';

  @override
  String get maintenanceProvider => 'Service Provider';

  @override
  String get maintenanceNotes => 'Notes';

  @override
  String get maintenanceAdded => 'Maintenance record added successfully';

  @override
  String get maintenanceDeleted => 'Maintenance deleted successfully';

  @override
  String get maintenanceUpdated => 'Maintenance record updated successfully';

  @override
  String get errorAddingMaintenance => 'Error adding maintenance record';

  @override
  String get errorDeletingMaintenance => 'Error deleting maintenance';

  @override
  String get errorUpdatingMaintenance => 'Error updating maintenance record';

  @override
  String get errorLoadingMaintenance => 'Error loading maintenance records';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get updatePassword => 'Update Password';

  @override
  String get passwordUpdatedSuccessfully => 'Password updated successfully';

  @override
  String get displayName => 'Display Name';

  @override
  String get displayNameRequired => 'Please enter display name';

  @override
  String get profileUpdatedSuccessfully => 'Profile updated successfully';

  @override
  String get chooseImageSource => 'Choose Image Source';

  @override
  String get profileImageUpdated => 'Profile image updated successfully';

  @override
  String get errorUpdatingProfileImage =>
      'Error updating profile image. Please check your permissions.';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get cameraPermissionDenied =>
      'Camera permission is required to take photos. Please enable it in settings.';

  @override
  String get galleryPermissionDenied =>
      'Storage permission is required to select photos. Please enable it in settings.';

  @override
  String get openSettings => 'Open Settings';

  @override
  String get maintenanceTitle => 'Maintenance Title';

  @override
  String get orContinueWith => 'Or continue with';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get preferences => 'Preferences';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get next => 'Next';

  @override
  String get skip => 'Skip';

  @override
  String get getStarted => 'Get Started!';

  @override
  String get onboardingTitle1 => 'Never Miss an Oil Change Again';

  @override
  String get onboardingDesc1 =>
      'Forgetting oil changes can damage your engine and cost thousands. Oil Plus tracks everything automatically and reminds you when it\'s time.';

  @override
  String get onboardingTitle2 => 'Everything You Need in One App';

  @override
  String get onboardingDesc2 =>
      'Manage all your cars, trucks, and motorcycles. Get smart reminders before you\'re overdue. Keep complete records with photos and receipts.';

  @override
  String get onboardingTitle3 => 'Start Protecting Your Investment';

  @override
  String get onboardingDesc3 =>
      'Begin with essential tracking features for free. Upgrade to Premium for AI assistance, unlimited vehicles, and advanced features. Try Premium risk-free for 7 days.';

  @override
  String get onboardingTitle4 => 'Track All Maintenance';

  @override
  String get onboardingDesc4 =>
      'Keep records of all maintenance activities for your vehicles.';

  @override
  String get onboardingTitle5 => 'Voice Commands & AI Assistant';

  @override
  String get onboardingDesc5 =>
      'Use voice input and chat with our AI assistant for quick tasks and support.';

  @override
  String get onboardingTitle6 => 'Cloud Backup & Premium';

  @override
  String get onboardingDesc6 =>
      'Secure cloud backup, premium features and ad-free experience with a subscription.';

  @override
  String get onboardingProblem =>
      'Forgetting oil changes can damage your engine';

  @override
  String get onboardingSolution => 'Oil Plus tracks everything automatically';

  @override
  String get onboardingFeature1 => 'Track Multiple Vehicles';

  @override
  String get onboardingFeature1Desc =>
      'Manage all your cars, trucks, and motorcycles';

  @override
  String get onboardingFeature2 => 'Smart Reminders';

  @override
  String get onboardingFeature2Desc => 'Get notified before you\'re overdue';

  @override
  String get onboardingFeature3 => 'Complete Records';

  @override
  String get onboardingFeature3Desc =>
      'Photos, receipts, and maintenance history';

  @override
  String get onboardingCta => 'Start Protecting Your Investment';

  @override
  String get onboardingFreeStart => 'Free to Start';

  @override
  String get onboardingFreeStartDesc =>
      'Begin with essential tracking features';

  @override
  String get onboardingPremiumBenefits => 'Premium Benefits';

  @override
  String get onboardingPremiumBenefitsDesc =>
      'AI assistance and unlimited vehicles';

  @override
  String get onboardingTrialOffer => '7-Day Trial';

  @override
  String get onboardingTrialOfferDesc => 'Try Premium features risk-free';

  @override
  String get deleteMaintenance => 'Delete Maintenance';

  @override
  String get confirmDeleteMaintenance =>
      'Are you sure you want to delete this maintenance record? This action cannot be undone.';

  @override
  String get offlineMode => 'You are offline - some features may be limited';

  @override
  String get noData => 'No data available';

  @override
  String get details => 'Details';

  @override
  String get selectRecommendedInterval => 'Select Recommended Interval';

  @override
  String selected(String value) {
    return 'Selected: $value';
  }

  @override
  String oilChangeRemainingMessage(String km) {
    return 'Only $km km remaining until oil change';
  }

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get termsAndConditions => 'Terms and Conditions';

  @override
  String get legal => 'Legal';

  @override
  String get signInToTrackVehicles =>
      'You need to sign in to track your vehicles';

  @override
  String get carSavedAnyway => 'Image upload failed but car will be updated';

  @override
  String get errorUploadingImage => 'Image upload failed';

  @override
  String get addCarImage => 'Add Car Image';

  @override
  String get addCarImageHint => 'Choose a photo of your car';

  @override
  String get weatherUnavailable => 'Weather Unavailable';

  @override
  String get oilChangeRemindersDesc =>
      'Get notified when your car needs an oil change';

  @override
  String get maintenanceRemindersDesc =>
      'Get notified about scheduled maintenance';

  @override
  String get mileageRemindersDesc => 'Get notified about mileage updates';

  @override
  String get failedToRefreshToken => 'Failed to refresh FCM token';

  @override
  String get refreshToken => 'Refresh Token';

  @override
  String get permissions => 'Permissions';

  @override
  String get manageAppPermissions => 'Manage app permissions';

  @override
  String get notificationsPermissionDesc =>
      'Receive timely reminders about oil changes and maintenance';

  @override
  String get locationPermissionDesc =>
      'Show local weather conditions for better maintenance recommendations';

  @override
  String get cameraPermissionDesc =>
      'Take photos of your vehicles for better tracking';

  @override
  String get storagePermissionDesc => 'Save and access photos of your vehicles';

  @override
  String get location => 'Location';

  @override
  String get storage => 'Storage';

  @override
  String get timeBasedOilChangeInterval => 'Time-based Oil Change Interval';

  @override
  String get timeBasedIntervalExplanation =>
      'Even with low mileage, oil should be changed periodically due to deterioration over time.';

  @override
  String get timeBasedIntervalDescription =>
      'Set how often you want to change your oil based on time, regardless of mileage.';

  @override
  String get notificationExplanation =>
      'You will receive notifications when your oil change is due based on either time or mileage, whichever comes first.';

  @override
  String get invalidMonthRange => 'Please enter a value between 1-12 months';

  @override
  String get oilFilter => 'Oil Filter';

  @override
  String get didYouChangeFilter => 'Did you change the filter?';

  @override
  String get enterFilterType => 'Enter filter type/brand';

  @override
  String get costs => 'Costs';

  @override
  String get oilCost => 'Oil Cost';

  @override
  String get filterCost => 'Filter Cost';

  @override
  String get currencySymbol => '\$';

  @override
  String get add => 'Add';

  @override
  String get addNew => 'Add New';

  @override
  String get termsAcceptance => 'Acceptance of Terms';

  @override
  String get termsAcceptanceText =>
      'By accessing or using Oil Plus, you agree to be bound by these Terms and Conditions. If you do not agree with any part of these terms, you may not use our application.';

  @override
  String get appUsage => 'Application Usage';

  @override
  String get appUsageText =>
      'Oil Plus provides tools for tracking vehicle maintenance. The application and all content, features, and functionality are provided for your information and personal use only, and on an \'as is\', \'as available\' basis, without any warranties of any kind, either express or implied. While we strive for accuracy, we do not guarantee that any information provided through the application is accurate, complete, or current. We expressly disclaim all warranties, express or implied, including without limitation, warranties of merchantability, fitness for a particular purpose, non-infringement, or course of performance.';

  @override
  String get userAccounts => 'User Accounts';

  @override
  String get userAccountsText =>
      'You are responsible for maintaining the confidentiality of your account information and password and for restricting access to your device. You accept responsibility for all activities that occur under your account. You must notify us immediately of any breach of security or unauthorized use of your account. We reserve the right to refuse service, terminate accounts, remove or edit content, or cancel orders at our sole discretion. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.';

  @override
  String get userContent => 'User Content';

  @override
  String get userContentText =>
      'You retain all rights in any content you submit, post, or display on or through the application (\'User Content\'). By submitting, posting, or displaying User Content on or through the application, you grant us a worldwide, non-exclusive, royalty-free license (with the right to sublicense) to use, copy, reproduce, process, adapt, modify, publish, transmit, display, and distribute such content in any and all media or distribution methods. You represent and warrant that: (1) you own the User Content or have the right to use and license it to us; and (2) the User Content does not violate the privacy rights, publicity rights, intellectual property rights, or any other rights of any person.';

  @override
  String get intellectualProperty => 'Intellectual Property';

  @override
  String get intellectualPropertyText =>
      'Oil Plus and its original content, features, and functionality are and will remain the exclusive property of Oil Plus and its licensors. The application is protected by copyright, trademark, and other laws of the United States and foreign countries. Our trademarks and trade dress may not be used in connection with any product or service without the prior written consent of Oil Plus. All content included in or made available through the application, including text, graphics, logos, images, data compilations, and software, is the property of Oil Plus or its content suppliers and protected by intellectual property laws.';

  @override
  String get disclaimerWarranties => 'Disclaimer of Warranties';

  @override
  String get disclaimerWarrantiesText =>
      'TO THE MAXIMUM EXTENT PERMITTED BY APPLICABLE LAW, THE APPLICATION AND ALL CONTENT, SERVICES, AND PRODUCTS ASSOCIATED WITH THE APPLICATION ARE PROVIDED TO YOU ON AN \'AS-IS\' AND \'AS AVAILABLE\' BASIS. OIL PLUS MAKES NO REPRESENTATIONS OR WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED, AS TO THE OPERATION OF THE APPLICATION, OR THE INFORMATION, CONTENT, MATERIALS, OR PRODUCTS INCLUDED ON THE APPLICATION. YOU EXPRESSLY AGREE THAT YOUR USE OF THE APPLICATION IS AT YOUR SOLE RISK. OIL PLUS DOES NOT WARRANT THAT THE APPLICATION, ITS SERVERS, OR EMAIL SENT FROM OIL PLUS ARE FREE OF VIRUSES OR OTHER HARMFUL COMPONENTS.';

  @override
  String get limitationLiability => 'Limitation of Liability';

  @override
  String get limitationLiabilityText =>
      'TO THE FULLEST EXTENT PERMITTED BY APPLICABLE LAW, IN NO EVENT SHALL OIL PLUS, ITS AFFILIATES, OFFICERS, DIRECTORS, EMPLOYEES, AGENTS, SUPPLIERS, OR LICENSORS BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM: (1) YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE APPLICATION; (2) ANY CONDUCT OR CONTENT OF ANY THIRD PARTY ON THE APPLICATION; (3) ANY CONTENT OBTAINED FROM THE APPLICATION; AND (4) UNAUTHORIZED ACCESS, USE, OR ALTERATION OF YOUR TRANSMISSIONS OR CONTENT, WHETHER BASED ON WARRANTY, CONTRACT, TORT (INCLUDING NEGLIGENCE), OR ANY OTHER LEGAL THEORY, WHETHER OR NOT WE HAVE BEEN INFORMED OF THE POSSIBILITY OF SUCH DAMAGE. IN JURISDICTIONS WHERE THE EXCLUSION OR LIMITATION OF LIABILITY FOR CONSEQUENTIAL OR INCIDENTAL DAMAGES IS NOT ALLOWED, OUR LIABILITY IS LIMITED TO THE MAXIMUM EXTENT PERMITTED BY LAW.';

  @override
  String get termsModifications => 'Modifications to Terms';

  @override
  String get termsModificationsText =>
      'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days\' notice prior to any new terms taking effect. What constitutes a material change will be determined at our sole discretion. By continuing to access or use our application after any revisions become effective, you agree to be bound by the revised terms. If you do not agree to the new terms, you are no longer authorized to use the application.';

  @override
  String get governingLaw => 'Governing Law';

  @override
  String get governingLawText =>
      'These Terms and your use of the application shall be governed by and construed in accordance with the laws of the jurisdiction in which we operate, without regard to its conflict of law provisions. Our failure to enforce any right or provision of these Terms will not be considered a waiver of those rights. If any provision of these Terms is held to be invalid or unenforceable by a court, the remaining provisions of these Terms will remain in effect. These Terms constitute the entire agreement between us regarding our application, and supersede and replace any prior agreements we might have had between us regarding the application.';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get contactUsText =>
      'If you have any questions about these Terms and Conditions or our Privacy Policy, please contact <NAME_EMAIL>.';

  @override
  String get lastUpdated => 'Last Updated';

  @override
  String get privacyPolicyIntro => 'Introduction';

  @override
  String get privacyPolicyIntroText =>
      'Welcome to Oil Plus! This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our mobile application. Please read this Privacy Policy carefully. By using the application, you consent to the data practices described in this statement.';

  @override
  String get privacyPolicyInfo => 'Information We Collect';

  @override
  String get privacyPolicyInfoText =>
      'We may collect several types of information from and about users of our application, including: (1) Personal information you voluntarily provide when using our application, such as email address, name, and profile details; (2) Information about your vehicle(s) including make, model, year, maintenance history, and images; (3) Usage data and analytics information about how you interact with our application; (4) Device information including operating system, hardware version, device settings, file and software names, battery and signal strength; (5) Location information when you enable this functionality; and (6) Information from third-party services if you choose to link them with your account.';

  @override
  String get privacyPolicyUse => 'How We Use Your Information';

  @override
  String get privacyPolicyUseText =>
      'We use information that we collect about you or that you provide to us, including any personal information: (1) To provide, maintain, and improve our Services; (2) To process and complete transactions, and send related information including confirmations and reminders; (3) To personalize your experience with our application; (4) To communicate with you, including for customer service, updates, security alerts, and support messages; (5) To analyze usage patterns and trends, and to better understand how users interact with our application; (6) To enhance security, monitor and verify identity, and prevent fraud; (7) For compliance purposes, including enforcing our Terms of Service; and (8) For any other purpose with your consent.';

  @override
  String get privacyPolicyStorage => 'Data Storage and Security';

  @override
  String get privacyPolicyStorageText =>
      'The security of your personal information is important to us. We implement and maintain reasonable security measures appropriate to the nature of the information we store in order to protect it from unauthorized access, destruction, use, modification, or disclosure. However, please be aware that no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee the absolute security of any information. Your data is stored on Firebase, a Google Cloud platform, with industry-standard encryption and security protocols. We retain personal information only for as long as necessary to fulfill the purposes for which it was collected and as required by applicable laws or regulations.';

  @override
  String get privacyPolicyRights => 'Your Rights';

  @override
  String get privacyPolicyRightsText =>
      'Depending on your location, you may have certain rights regarding your personal information. These may include: (1) The right to access personal information we hold about you; (2) The right to request correction of inaccurate data; (3) The right to request deletion of your data; (4) The right to restrict or object to our processing of your data; (5) The right to data portability; and (6) The right to withdraw consent. Please contact us if you wish to exercise any of these rights. We will respond to your request within the timeframe required by applicable law.';

  @override
  String get privacyPolicyChildren => 'Children\'s Privacy';

  @override
  String get privacyPolicyChildrenText =>
      'Our Services are not directed to children under the age of 13, and we do not knowingly collect personal information from children under 13. If we learn that we have collected personal information from a child under 13, we will take steps to delete such information as quickly as possible. If you believe we might have any information from or about a child under 13, please contact us immediately.';

  @override
  String get privacyPolicyThirdParty => 'Third-Party Services';

  @override
  String get privacyPolicyThirdPartyText =>
      'Our application may contain links to or integrate with third-party websites, services, or applications. We are not responsible for the privacy practices or content of these third parties. The collection, use, and disclosure of your information by these third parties are subject to their respective privacy policies, not this Privacy Policy. We encourage you to read the privacy policies of all third-party websites, services, or applications you visit or use.';

  @override
  String get privacyPolicyChanges => 'Changes to This Privacy Policy';

  @override
  String get privacyPolicyChangesText =>
      'We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the \'Last Updated\' date. You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page. Your continued use of the application after we post any modifications to the Privacy Policy will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified Privacy Policy.';

  @override
  String get indemnification => 'Indemnification';

  @override
  String get indemnificationText =>
      'You agree to defend, indemnify, and hold harmless Oil Plus, its parent, subsidiaries, affiliates, and their respective directors, officers, employees, agents, service providers, contractors, licensors, suppliers, successors, and assigns from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys\' fees) arising out of or relating to your violation of these Terms or your use of the application, including, but not limited to, any use of the application\'s content, services, and products other than as expressly authorized in these Terms or your use of any information obtained from the application.';

  @override
  String get disputeResolution => 'Dispute Resolution';

  @override
  String get disputeResolutionText =>
      'Any legal action or proceeding relating to your access to, or use of, the application or these Terms shall be instituted in a state or federal court in the jurisdiction where we operate. You and Oil Plus agree to submit to the personal jurisdiction of such courts, and to waive any and all objections to the exercise of jurisdiction over the parties by such courts and to venue in such courts.';

  @override
  String get severability => 'Severability';

  @override
  String get severabilityText =>
      'If any provision of these Terms is held by a court or other tribunal of competent jurisdiction to be invalid, illegal, or unenforceable for any reason, such provision shall be eliminated or limited to the minimum extent such that the remaining provisions of the Terms will continue in full force and effect.';

  @override
  String get notifyChanges => 'Notification of Changes';

  @override
  String get notifyChangesText =>
      'We reserve the right to make changes to our Privacy Policy and Terms at any time. If we decide to change our Privacy Policy or Terms, we will post those changes on this page so that you are always aware of what information we collect, how we use it, and under what circumstances we disclose it. Changes will be effective immediately upon posting to the application. Your continued use of the application after we post any modifications to the Privacy Policy or Terms will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified policy or terms.';

  @override
  String get account => 'Account';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get clearLocalImageCache => 'Clear local image cache';

  @override
  String get cacheCleared => 'Cache cleared successfully';

  @override
  String get errorClearingCache => 'Error clearing cache';

  @override
  String get checkConnection => 'Check your connection';

  @override
  String get locationNeeded => 'Location Access Needed';

  @override
  String get tapToSetLocation => 'Tap to set location';

  @override
  String get locationPermissionGranted => 'Location permission granted';

  @override
  String get locationPermissionRationale =>
      'We need location permission to show you local weather data. The weather information helps you plan oil changes based on local conditions.';

  @override
  String get weatherOptions => 'Weather Options';

  @override
  String get useDeviceLocation => 'Use Device Location';

  @override
  String get locationExplanation =>
      'Get precise weather for your current location';

  @override
  String get useIpLocation => 'Use Approximate Location';

  @override
  String get ipLocationExplanation =>
      'Estimate location based on your IP address';

  @override
  String get refreshWeather => 'Refresh Weather';

  @override
  String get refreshWeatherExplanation =>
      'Update weather data with your current settings';

  @override
  String get authenticationError =>
      'Authentication error: Please sign in again';

  @override
  String get signInAgain => 'Sign In';

  @override
  String get errorFcmToken => 'Error: FCM token not available';

  @override
  String errorGeneric(String message) {
    return 'Error: $message';
  }

  @override
  String get carUpdatedSuccess => 'Car updated successfully';

  @override
  String get imageUploadFailed => 'Image upload failed but car will be saved';

  @override
  String get addAnotherPhoto => 'Add Another Photo';

  @override
  String get showCarFromDifferentAngles =>
      'Show your car from different angles';

  @override
  String get selectRecommendedIntervalHint => 'Select recommended interval';

  @override
  String get onboardingReset =>
      'Onboarding reset. Restart the app to see onboarding again.';

  @override
  String get resetOnboardingTooltip => 'Reset Onboarding (Testing Only)';

  @override
  String get oilChangeNotificationSentSuccessfully =>
      'Oil change notification sent successfully!';

  @override
  String get failedToSendOilChangeNotification =>
      'Failed to send oil change notification';

  @override
  String get scheduledRemindersTriggeredSuccessfully =>
      'Scheduled reminders triggered successfully!';

  @override
  String get failedToTriggerScheduledReminders =>
      'Failed to trigger scheduled reminders';

  @override
  String tokenRefreshed(String token) {
    return 'FCM Token refreshed: $token...';
  }

  @override
  String get viewPrivacyPolicy => 'View our Privacy Policy';

  @override
  String get viewTermsAndConditions => 'View our Terms & Conditions';

  @override
  String mileageUpdatedTo(String make, String model, String mileage) {
    return '$make $model mileage updated to $mileage km';
  }

  @override
  String get totalMaintenanceCost => 'Total Maintenance Cost';

  @override
  String get totalRecords => 'Total Records';

  @override
  String get maintenanceDetails => 'Maintenance Details';

  @override
  String get errorLoadingMaintenanceDetails =>
      'Error loading maintenance details';

  @override
  String get editMaintenance => 'Edit Maintenance';

  @override
  String get existingPhotos => 'Existing Photos';

  @override
  String get addNewPhotos => 'Add New Photos';

  @override
  String get noMaintenanceRecordsYet => 'No maintenance records yet';

  @override
  String get receiptPhotos => 'Receipt Photos';

  @override
  String get addReceiptPhotosDesc =>
      'Add photos of your maintenance receipts or invoices';

  @override
  String get photos => 'Photos';

  @override
  String get viewPhoto => 'View Photo';

  @override
  String get errorLoadingImage => 'Failed to load image';

  @override
  String get failedToTakePhoto => 'Failed to take photo';

  @override
  String get failedToSelectPhoto => 'Failed to select photo';

  @override
  String get title => 'Title';

  @override
  String get enterTitle => 'Enter maintenance title';

  @override
  String get titleIsRequired => 'Title is required';

  @override
  String get enterOdometer => 'Enter current odometer reading';

  @override
  String get odometerMustBeNumber => 'Odometer must be a number';

  @override
  String get description => 'Description';

  @override
  String get enterDescription => 'Enter maintenance description';

  @override
  String get enterCost => 'Enter maintenance cost';

  @override
  String get costMustBeNumber => 'Cost must be a number';

  @override
  String get shopName => 'Shop Name';

  @override
  String get enterShopName => 'Enter shop or service provider name';

  @override
  String get dateIsRequired => 'Date is required';

  @override
  String get selectDate => 'Select date';

  @override
  String get odometer => 'Odometer';

  @override
  String get emailNotRegistered => 'Email address is not registered';

  @override
  String get forgotPasswordDescription =>
      'Enter your email address and we will send you instructions to reset your password.';

  @override
  String get resetPasswordEmailSent => 'Email Sent!';

  @override
  String get resetPasswordCheckEmail =>
      'Please check your email for instructions to reset your password.';

  @override
  String get backToLogin => 'Back to Login';

  @override
  String get emailVerification => 'Email Verification';

  @override
  String get pleaseVerifyEmail =>
      'Please verify your email address to access all features';

  @override
  String get resendVerificationEmail => 'Resend Verification Email';

  @override
  String get emailVerificationSent => 'Verification email sent';

  @override
  String get emailVerificationFailed => 'Failed to send verification email';

  @override
  String get verifiedMyEmail => 'I\'ve Verified My Email';

  @override
  String get checkingEmailVerification => 'Checking verification status...';

  @override
  String get emailNotVerifiedYet =>
      'Email not verified yet. Please check your inbox.';

  @override
  String get verificationEmailSentTo => 'We have sent a verification email to:';

  @override
  String get driverLicense => 'Driver\'s License';

  @override
  String get driverLicenseExpiryDate => 'Driver\'s License Expiry Date';

  @override
  String get tapToSetExpiryDate => 'Tap to set expiry date';

  @override
  String get selectExpiryDate => 'Select when your driver\'s license expires';

  @override
  String get expiresOn => 'Expires';

  @override
  String get expired => 'Expired';

  @override
  String get enableExpiryNotifications => 'Enable expiry notifications';

  @override
  String get receiveRemindersBeforeExpiry =>
      'Receive reminders before your license expires';

  @override
  String get today => 'Today';

  @override
  String get tomorrow => 'Tomorrow';

  @override
  String get days => 'days';

  @override
  String get expiredToday => 'Expired today';

  @override
  String get expiredYesterday => 'Expired yesterday';

  @override
  String expiredDaysAgo(int days) {
    return 'Expired $days days ago';
  }

  @override
  String get expiresToday => 'Expires today';

  @override
  String get expiresTomorrow => 'Expires tomorrow';

  @override
  String expiresInDays(int days) {
    return 'Expires in $days days';
  }

  @override
  String get years => 'years';

  @override
  String get month => 'month';

  @override
  String expiresIn(String details) {
    return 'Expires in $details';
  }

  @override
  String get notificationScheduleInfo =>
      'If enabled, reminders are sent 30, 14, 7, 1 day(s) before expiry, and on the expiry day.';

  @override
  String get week => 'week';

  @override
  String get weeks => 'weeks';

  @override
  String get day => 'day';

  @override
  String get licenseExpiryDate => 'License Expiry Date';

  @override
  String get notSet => 'Not Set';

  @override
  String get licenseExpiredTitle => 'License Expired';

  @override
  String licenseExpiredBody(String year, String make, String model) {
    return '$year $make $model license has expired.';
  }

  @override
  String get licenseExpiryReminderTitle => 'License Expiry Reminder';

  @override
  String get licenseExpiringSoonTitle => 'License Expiring Soon';

  @override
  String licenseExpiringSoonBody(
      String year, String make, String model, String days) {
    return '$year $make $model license expires in $days days.';
  }

  @override
  String get licenseExpiresTodayTitle => 'License Expires Today!';

  @override
  String licenseExpiresTodayBody(String year, String make, String model) {
    return '$year $make $model license expires today.';
  }

  @override
  String get isOverdueForOilChange => 'is overdue for an oil change';

  @override
  String get oilChangeDueSoon => 'Oil Change Due Soon';

  @override
  String oilChangeDueSoonBody(
      String year, String make, String model, String days) {
    return '$year $make $model will need an oil change in $days days';
  }

  @override
  String get oilChangeDueTodayTitle => 'Oil Change Due Today';

  @override
  String oilChangeDueTodayBody(String year, String make, String model) {
    return '$year $make $model is due for an oil change today';
  }

  @override
  String get takingPhoto => 'Taking photo...';

  @override
  String get noPhotoTaken => 'No photo taken';

  @override
  String get selectingPhoto => 'Selecting photo...';

  @override
  String get noPhotoSelected => 'No photo selected';

  @override
  String get networkError => 'Network Connection Error';

  @override
  String get checkInternetConnection => 'Please check your internet connection';

  @override
  String get networkConnectionError =>
      'Network error. Please check your internet connection and try again.';

  @override
  String get networkConnectionLost =>
      'Network connection lost. Please check your internet connection and try again.';

  @override
  String get unlockPremiumFeatures => 'Unlock Premium Features';

  @override
  String get chooseYourPlan => 'Choose the plan that works for you';

  @override
  String get saveWithYearly => 'Save up to 30% with yearly subscription';

  @override
  String get current => 'Current';

  @override
  String get free => 'Free';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get selectPlan => 'Select Plan';

  @override
  String get subscriptionActivated => 'Subscription activated successfully';

  @override
  String get subscriptionFailed => 'Failed to activate subscription';

  @override
  String get purchasesRestored => 'Purchases restored successfully';

  @override
  String get noPurchasesFound => 'No purchases found';

  @override
  String get subscriptionTerms =>
      'Subscriptions can be managed and canceled through your Google Play account settings. No automatic renewals occur without your explicit consent.';

  @override
  String get voiceCommands => 'Voice Commands';

  @override
  String get voiceCommandsDescription =>
      'Use voice commands to quickly add records';

  @override
  String get premiumFeature => 'Premium Feature';

  @override
  String get premiumRequired => 'This feature requires a premium subscription';

  @override
  String get upgradeNow => 'Upgrade Now';

  @override
  String get notNow => 'Not Now';

  @override
  String get tryFree => 'Try Free for 7 Days';

  @override
  String get familySharing => 'Family Sharing';

  @override
  String get familySharingDescription => 'Share with up to 5 family members';

  @override
  String get unlimitedVehicles => 'Unlimited Vehicles';

  @override
  String get adFreeExperience => 'Ad-Free Experience';

  @override
  String get enhancedAnalytics => 'Enhanced Analytics';

  @override
  String get prioritySupport => 'Priority Support';

  @override
  String get trialStarted => 'Trial started successfully';

  @override
  String get trialFailed => 'Failed to start trial';

  @override
  String trialExpires(String date) {
    return 'Trial expires on $date';
  }

  @override
  String daysRemaining(int days) {
    return '$days days remaining';
  }

  @override
  String get manageSubscription => 'Manage Subscription';

  @override
  String subscriptionExpires(String date) {
    return 'Subscription expires on $date';
  }

  @override
  String get voiceCommandsHelp => 'Voice Commands Help';

  @override
  String get voiceCommandsHelpDescription =>
      'Try these example commands with the voice input feature:';

  @override
  String get addOilChange => 'Add Oil Change';

  @override
  String get productNotAvailable => 'Product not available';

  @override
  String get checkConnectionAndTryAgain =>
      'Please check your internet connection and try again';

  @override
  String get premium => 'Premium';

  @override
  String get upgradeToPremiun => 'Upgrade to Premium';

  @override
  String get subscriptionDetails => 'Subscription Details';

  @override
  String get premiumSubscription => 'Premium Subscription';

  @override
  String get freeVersion => 'Free Version';

  @override
  String get youHaveActivePremium => 'You have an active premium subscription';

  @override
  String get upgradeToRemoveAds =>
      'Upgrade to premium to remove ads and unlock all features';

  @override
  String get upgradeToPremium => 'Upgrade to Premium';

  @override
  String get viewCarDetails => 'View Car Details';

  @override
  String get unknownCommand => 'Unknown Command';

  @override
  String get youSaid => 'You said';

  @override
  String get extractedInformation => 'Extracted Information';

  @override
  String get confirm => 'Confirm';

  @override
  String get couldNotUnderstandCommand =>
      'Could not understand the voice command. Please try again.';

  @override
  String get noSpeechDetected => 'No speech detected. Please try again.';

  @override
  String get microphonePermissionDenied =>
      'Microphone permission is required to use voice commands.';

  @override
  String get cloudBackup => 'Cloud Backup';

  @override
  String get cloudBackupPremiumFeature => 'Cloud Backup is a Premium Feature';

  @override
  String get cloudBackupDescription =>
      'Securely backup and restore your vehicle data, oil change history, and settings to the cloud.';

  @override
  String get backupSuccessful => 'Backup created successfully';

  @override
  String get backupFailed => 'Failed to create backup';

  @override
  String get confirmRestore => 'Confirm Restore';

  @override
  String get firstCarWelcomeMessage =>
      'Let\'s add your first vehicle to start tracking oil changes and maintenance!';

  @override
  String get firstCarStepIndicator => 'Step 1 of 1: Add Your First Car';

  @override
  String get quickTips => 'Quick Tips:';

  @override
  String get tipAddPhotos => 'Add photos to easily identify your car';

  @override
  String get tipEnterMileage => 'Enter current mileage for accurate tracking';

  @override
  String get tipSetIntervals =>
      'Set oil change intervals based on your driving habits';

  @override
  String get congratulations => '🎉 Congratulations!';

  @override
  String get firstCarSuccessMessage =>
      'You\'ve successfully added your first car! You\'re now ready to track oil changes and maintenance.';

  @override
  String get whatsNext => 'What\'s Next:';

  @override
  String get nextStepRecordOil => 'Record your next oil change';

  @override
  String get nextStepReminders => 'Get reminders for maintenance';

  @override
  String get nextStepTrackHealth => 'Track your vehicle\'s health';

  @override
  String get tapToAddPhotos => 'Tap above to add photos of your car';

  @override
  String get dashboardWelcomeSubtitle =>
      'Start tracking your vehicle\'s oil changes and maintenance with ease';

  @override
  String get whatYoullGet => 'What you\'ll get:';

  @override
  String get smartReminders => 'Smart Reminders';

  @override
  String get smartRemindersDesc =>
      'Never miss an oil change with intelligent notifications';

  @override
  String get trackHistory => 'Track History';

  @override
  String get trackHistoryDesc =>
      'Monitor your vehicle\'s maintenance history and costs';

  @override
  String get mileageTracking => 'Mileage Tracking';

  @override
  String get mileageTrackingDesc =>
      'Keep track of your vehicle\'s mileage automatically';

  @override
  String get restoreWarning =>
      'This will replace all your current data with the backup data. This action cannot be undone.';

  @override
  String get restore => 'Restore';

  @override
  String get restoreSuccessful => 'Data restored successfully';

  @override
  String get restoreFailed => 'Failed to restore data';

  @override
  String get automaticBackups => 'Automatic Backups';

  @override
  String get automaticBackupsDescription =>
      'Automatically backup your data daily to ensure you never lose your records.';

  @override
  String get enableAutomaticBackups => 'Enable automatic backups';

  @override
  String get automaticBackupsEnabled => 'Automatic backups enabled';

  @override
  String get automaticBackupsDisabled => 'Automatic backups disabled';

  @override
  String get manualBackup => 'Manual Backup';

  @override
  String get createBackup => 'Create Backup';

  @override
  String get backupHistory => 'Backup History';

  @override
  String get noBackupsFound => 'No backups found';

  @override
  String get manageCloudBackups => 'Manage your cloud backups and restore data';

  @override
  String get chatAssistantTitle => 'Car Assistant 🔧';

  @override
  String get clearChatTooltip => 'Clear chat';

  @override
  String get chatEmptyTitle => 'Ask about any problem in your car';

  @override
  String get chatEmptySubtitle => 'I will help with diagnostics and solutions';

  @override
  String get assistantThinking => 'Assistant is thinking...';

  @override
  String get chatFaqTitle => 'FAQs:';

  @override
  String get chatPromptEngineNoStart => 'Engine won\'t start';

  @override
  String get chatPromptAbsLight => 'ABS light is on';

  @override
  String get chatPromptOilLight => 'Oil warning light';

  @override
  String get chatPromptEngineNoise => 'Strange engine noise';

  @override
  String get chatPromptVibration => 'Car vibrates while driving';

  @override
  String get chatPromptBrakes => 'Brake problem';

  @override
  String get chatPromptHighTemp => 'Engine temperature high';

  @override
  String get chatPromptBattery => 'Battery issue';

  @override
  String get premiumRemoveAdsDescription =>
      'Get access to all premium features and remove ads';

  @override
  String get monthlyPremium => 'Monthly Premium';

  @override
  String get annualPremium => 'Annual Premium';

  @override
  String basePrice(String price) {
    return 'Base price: $price';
  }

  @override
  String monthlyEquivalent(String price) {
    return 'Monthly equivalent: $price';
  }

  @override
  String get premiumFeaturesTitle => 'Premium Features';

  @override
  String get featureVoiceCommands => 'Voice commands for quick entry';

  @override
  String get featureUnlimitedVehicles => 'Unlimited Vehicles';

  @override
  String get featureCloudBackup => 'Cloud Backup & Sync';

  @override
  String get featureAdvancedAnalytics => 'Advanced analytics';

  @override
  String get freeTrialTitle => 'Free Trial';

  @override
  String get tryPremiumFeaturesFree => 'Try Premium Features Free';

  @override
  String get freeTrialDescription =>
      'Enjoy a 7-day free trial with no commitment. Cancel anytime.';

  @override
  String get selectPlanToTry => 'Select a plan to try:';

  @override
  String get freeTrialAgreement =>
      'I understand that after the trial period ends, I can choose to subscribe if I wish to continue using premium features.';

  @override
  String get startFreeTrial => 'Start Free Trial';

  @override
  String get freeTrialTerms =>
      'By starting a free trial, you agree to our Terms of Service and Privacy Policy. The trial will end automatically after 7 days with no charges. To continue using premium features, you can manually subscribe.';

  @override
  String get loadingPrice => 'Loading price...';

  @override
  String get trialStartedSuccess => 'Trial started successfully!';

  @override
  String get trialStartFailed => 'Failed to start trial. Please try again.';

  @override
  String freeForSevenDaysThen(String price, String perMonth) {
    return 'Free for 7 days. After trial: $price / $perMonth (manual subscription required)';
  }

  @override
  String get subscriptionPolicy => 'Subscription Policy';

  @override
  String get viewSubscriptionPolicy => 'View subscription details';

  @override
  String get refundPolicy => 'Refund Policy';

  @override
  String get viewRefundPolicy => 'View refund policy';

  @override
  String get aiAssistant => 'AI Assistant';

  @override
  String get aiChat => 'AI Chat';

  @override
  String get aiFeatures => 'AI Features';

  @override
  String get aiFeaturesPremiumDescription =>
      'Voice commands and AI chat assistant are premium features that help you manage your vehicle maintenance more efficiently.';

  @override
  String get voiceCommandsWelcome => 'Voice Commands';

  @override
  String get voiceCommandsSubtitle =>
      'Use your voice to quickly record maintenance and oil changes';

  @override
  String get recordOilChangeVoice => 'Record oil changes using voice commands';

  @override
  String get recordMaintenanceVoice =>
      'Record maintenance tasks using voice commands';

  @override
  String featureLocked(String featureName) {
    return '$featureName Locked';
  }

  @override
  String get featureRequiresPremium =>
      'This feature requires a premium subscription to unlock.';

  @override
  String get premiumFeatures => 'Premium Features';

  @override
  String get featureAiChat => 'AI Chat Assistant';

  @override
  String get featureAdFree => 'Ad-Free Experience';

  @override
  String get verificationEmailSent => 'Verification email sent';

  @override
  String get errorSendingVerificationEmail =>
      'Error sending verification email';

  @override
  String get verifyEmail => 'Verify Email';

  @override
  String get viewCar => 'View Car';

  @override
  String get noParametersDetected => 'No parameters detected';

  @override
  String get notSpecified => 'Not specified';

  @override
  String get restartApp => 'Restart App';

  @override
  String get adminNotifications => 'Admin Notifications';

  @override
  String get resetAllTokens => 'Reset All Tokens';

  @override
  String get sendToAllUsers => 'Send to All Users';

  @override
  String get notificationType => 'Notification Type';

  @override
  String get promotional => 'Promotional';

  @override
  String get oilChange => 'Oil Change';

  @override
  String get notificationTitle => 'Notification Title';

  @override
  String get notificationBody => 'Notification Body';

  @override
  String get enterNotificationTitle => 'Enter notification title';

  @override
  String get enterNotificationMessage => 'Enter notification message';

  @override
  String get titleBodyRequired => 'Title and body are required';

  @override
  String get accessDeniedAdmin => 'Access denied. Admin privileges required.';

  @override
  String get processing => 'Processing...';

  @override
  String get verificationResults => 'Verification Results';

  @override
  String get googleAccountDialogTitle => 'Manage Your Google Account';

  @override
  String get googleAccountDialogDescription =>
      'To manage your Google account:\n\n1. Go to Settings > Google\n2. Select your account\n3. Manage your preferences';

  @override
  String get goBack => 'Go Back';

  @override
  String get backToMaintenanceList => 'Back to Maintenance List';

  @override
  String get listening => 'Listening...';

  @override
  String get noVoiceDetected => 'No voice detected.';

  @override
  String failedToStartRecording(Object error) {
    return 'Failed to start recording: $error';
  }

  @override
  String get errorMissingCarId => 'Error: Car ID is missing.';

  @override
  String get developerSettings => 'Developer Settings';

  @override
  String get premiumFeatureDescription =>
      'Free users are limited to 3 vehicles. Upgrade to Premium for unlimited vehicles.';

  @override
  String get voiceInputFeature1 => 'Voice input for quick data entry';

  @override
  String get voiceInputFeature2 => 'Ad-free experience';

  @override
  String get voiceInputFeature3 => 'Unlimited vehicles';

  @override
  String get voiceInputFeature4 => 'Enhanced analytics and insights';

  @override
  String get voiceInputFeature5 => 'Cloud backup and sync';

  @override
  String get resettingAndRefreshingTokens =>
      'Resetting and refreshing all tokens...';
}
