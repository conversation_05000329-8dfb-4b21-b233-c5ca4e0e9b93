// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class SAr extends S {
  SAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'اويل بلس';

  @override
  String get welcomeMessage => 'تتبع تغييرات الزيت والصيانة لمركبتك بسهولة';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get signup => 'التسجيل';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get name => 'الاسم';

  @override
  String get emailRequired => 'البريد الإلكتروني مطلوب';

  @override
  String get invalidEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get passwordRequired => 'كلمة المرور مطلوبة';

  @override
  String get passwordTooShort => 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';

  @override
  String get passwordsDoNotMatch => 'كلمات المرور غير متطابقة';

  @override
  String get nameRequired => 'الاسم مطلوب';

  @override
  String get signInWithGoogle => 'تسجيل الدخول باستخدام جوجل';

  @override
  String get noAccountSignUp => 'ليس لديك حساب؟ سجل الآن';

  @override
  String get haveAccountLogin => 'لديك حساب بالفعل؟ تسجيل الدخول';

  @override
  String get signUpSuccess => 'تم إنشاء الحساب بنجاح! الرجاء تسجيل الدخول.';

  @override
  String get dashboard => 'لوحة القيادة';

  @override
  String get error => 'خطأ';

  @override
  String get ok => 'موافق';

  @override
  String get cancel => 'إلغاء';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get confirmPasswordRequired => 'يرجى تأكيد كلمة المرور';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟ سجل دخول';

  @override
  String get welcomeBack => 'مرحباً بك في لوحة القيادة';

  @override
  String get oilConsumption => 'استهلاك الزيت';

  @override
  String get used => 'مستهلك';

  @override
  String kmUntilNextOilChange(Object km) {
    return 'متبقي $km كم حتى تغيير الزيت التالي';
  }

  @override
  String get oilChangeOverdue => 'تأخر تغيير الزيت!';

  @override
  String get recordOilChange => 'تسجيل تغيير الزيت';

  @override
  String nextOilChange(String mileage) {
    return 'تغيير الزيت التالي';
  }

  @override
  String get overdue => 'متأخر';

  @override
  String get close => 'إغلاق';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get reminders => 'التذكيرات';

  @override
  String get oilChangeReminders => 'تذكيرات تغيير الزيت';

  @override
  String get oilChangeRemindersDescription =>
      'تلقي إشعارات عندما تحتاج سيارتك لتغيير الزيت';

  @override
  String get maintenanceReminders => 'تذكيرات الصيانة';

  @override
  String get maintenanceRemindersDescription =>
      'تلقي إشعارات حول مواعيد الصيانة المجدولة';

  @override
  String get mileageReminders => 'تذكيرات المسافة المقطوعة';

  @override
  String get mileageRemindersDescription =>
      'تلقي إشعارات حول تحديثات المسافة المقطوعة';

  @override
  String get notificationSettings => 'إدارة تفضيلات الإشعارات';

  @override
  String get about => 'حول';

  @override
  String get aboutApp => 'حول Oil Plus';

  @override
  String get aboutDescription =>
      'يساعدك Oil Plus على تتبع جدول صيانة سيارتك وتغييرات الزيت. حافظ على صحة مركبتك من خلال التذكيرات في الوقت المناسب وسجل خدمة مفصل.';

  @override
  String get personalInformation => 'معلومات شخصية';

  @override
  String get security => 'الأمان';

  @override
  String get appSettings => 'إعدادات التطبيق';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get signOut => 'تسجيل الخروج';

  @override
  String get signOutConfirmation => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get unknownUser => 'مستخدم غير معروف';

  @override
  String get comingSoon => 'قريبًا!';

  @override
  String get recordMaintenance => 'تسجيل الصيانة';

  @override
  String get selectCar => 'اختر السيارة';

  @override
  String get maintenanceType => 'نوع الصيانة';

  @override
  String get currentMileage => 'المسافة الحالية';

  @override
  String get date => 'التاريخ';

  @override
  String get cost => 'التكلفة';

  @override
  String get serviceProvider => 'مزود الخدمة';

  @override
  String get notes => 'ملاحظات';

  @override
  String get optional => 'اختياري';

  @override
  String get pleaseSelectCar => 'يرجى اختيار سيارة';

  @override
  String get pleaseEnterMileage => 'يرجى إدخال المسافة الحالية';

  @override
  String get pleaseEnterCost => 'يرجى إدخال التكلفة';

  @override
  String get saveMaintenance => 'حفظ الصيانة';

  @override
  String get maintenanceAddedSuccess => 'تمت إضافة سجل الصيانة بنجاح';

  @override
  String get noCarsFound => 'لم يتم العثور على سيارات';

  @override
  String get addCarFirst => 'أضف سيارة أولاً لتسجيل الصيانة';

  @override
  String get addCar => 'إضافة سيارة';

  @override
  String get errorLoadingCars => 'خطأ في تحميل السيارات';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get generalService => 'خدمة عامة';

  @override
  String get brakeService => 'صيانة الفرامل';

  @override
  String get engineService => 'صيانة المحرك';

  @override
  String get transmissionService => 'صيانة ناقل الحركة';

  @override
  String get tireService => 'صيانة الإطارات';

  @override
  String get batteryService => 'صيانة البطارية';

  @override
  String get airConditioning => 'تكييف الهواء';

  @override
  String get electricalSystem => 'النظام الكهربائي';

  @override
  String get suspension => 'نظام التعليق';

  @override
  String get exhaustSystem => 'نظام العادم';

  @override
  String get fuelSystem => 'نظام الوقود';

  @override
  String get coolingSystem => 'نظام التبريد';

  @override
  String get regularMaintenance => 'صيانة دورية';

  @override
  String get other => 'أخرى';

  @override
  String get myCars => 'سياراتي';

  @override
  String get noCarsAddedYet => 'لم تتم إضافة سيارات بعد';

  @override
  String get addFirstCarMessage => 'أضف سيارتك الأولى لبدء تتبع تغييرات الزيت';

  @override
  String get totalCars => 'إجمالي السيارات';

  @override
  String get needOilChange => 'تحتاج لتغيير الزيت';

  @override
  String get carsDueForOilChange => 'السيارات المستحقة لتغيير الزيت';

  @override
  String get viewAllCars => 'عرض كل السيارات';

  @override
  String get noUpcomingMaintenance => 'لا توجد صيانة قادمة';

  @override
  String get allCarsUpToDate => 'جميع السيارات محدثة مع جدول الصيانة الخاص بها';

  @override
  String nextOilChangeInDays(Object days) {
    return 'تغيير الزيت التالي في $days يوم';
  }

  @override
  String overdueDays(Object days) {
    return 'متأخر بـ $days يوم';
  }

  @override
  String daysUntilNextChange(int days) {
    return 'متبقي $days يوم حتى التغيير التالي';
  }

  @override
  String get viewAll => 'عرض الكل';

  @override
  String get goodMorning => 'صباح الخير';

  @override
  String get goodAfternoon => 'مساء الخير';

  @override
  String get goodEvening => 'مساء الخير';

  @override
  String get subscriptions => 'الاشتراكات';

  @override
  String get activeSubscription => 'اشتراك نشط';

  @override
  String get billingPeriod => 'فترة الفوترة';

  @override
  String get monthly => 'شهرياً';

  @override
  String get yearly => 'سنوياً';

  @override
  String get save30Percent => 'وفر 30%';

  @override
  String get perMonth => 'في الشهر';

  @override
  String get perYear => 'في السنة';

  @override
  String get subscribe => 'اشترك';

  @override
  String get popular => 'الأكثر شيوعاً';

  @override
  String get restorePurchases => 'استرداد المشتريات';

  @override
  String get cancelSubscription => 'إلغاء الاشتراك';

  @override
  String get cancelSubscriptionTitle => 'إلغاء الاشتراك؟';

  @override
  String get cancelSubscriptionMessage =>
      'سيبقى اشتراكك نشطاً حتى نهاية فترة الفوترة الحالية.';

  @override
  String get addNewCar => 'إضافة سيارة جديدة';

  @override
  String get enterCarDetails => 'يرجى ملء بيانات سيارتك';

  @override
  String get saveSuccess => 'تم الحفظ بنجاح';

  @override
  String get pleaseSignIn => 'يرجى تسجيل الدخول لعرض لوحة القيادة';

  @override
  String get quickActions => 'إجراءات سريعة';

  @override
  String get addMaintenance => 'إضافة صيانة';

  @override
  String get update => 'تحديث';

  @override
  String get updateMileage => 'تحديث المسافة';

  @override
  String get mileageUpdated => 'تم تحديث المسافة بنجاح';

  @override
  String get invalidMileage => 'قيمة مسافة غير صالحة';

  @override
  String get mileageRequired => 'المسافة مطلوبة';

  @override
  String get mileageMustBeNumber => 'يجب أن تكون المسافة رقمًا';

  @override
  String mileageMustBeGreaterThan(String currentMileage) {
    return 'يجب أن تكون المسافة أكبر من $currentMileage';
  }

  @override
  String get mileageTooHigh =>
      'المسافة تبدو عالية جدًا، يرجى التحقق من الإدخال';

  @override
  String get lastOilChange => 'آخر تغيير للزيت عند';

  @override
  String get kilometersElapsed => 'المسافة المقطوعة';

  @override
  String get appTitle => 'اويل بلس';

  @override
  String get welcomeText => 'مرحبًا بك في اويل بلس';

  @override
  String get signIn => 'تسجيل الدخول';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get sendResetLink => 'إرسال رابط إعادة التعيين';

  @override
  String get noAccount => 'ليس لديك حساب؟';

  @override
  String get haveAccount => 'لديك حساب بالفعل؟';

  @override
  String get cars => 'السيارات';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get darkMode => 'الوضع الداكن';

  @override
  String get lightMode => 'الوضع الفاتح';

  @override
  String get darkTheme => 'السمة الداكنة';

  @override
  String get lightTheme => 'السمة الفاتحة';

  @override
  String get themeSettings => 'إعدادات السمة';

  @override
  String get systemTheme => 'سمة النظام';

  @override
  String get english => 'الإنجليزية';

  @override
  String get arabic => 'العربية';

  @override
  String get enable => 'تفعيل';

  @override
  String get disable => 'تعطيل';

  @override
  String get version => 'الإصدار';

  @override
  String get emailInvalid => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get passwordLength => 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';

  @override
  String get passwordMatch => 'كلمات المرور غير متطابقة';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get theme => 'المظهر';

  @override
  String get phoneInvalid => 'يرجى إدخال رقم هاتف صحيح';

  @override
  String get errorOccurred => 'حدث خطأ';

  @override
  String get success => 'نجاح';

  @override
  String get continueAction => 'متابعة';

  @override
  String get done => 'تم';

  @override
  String get changeProfilePhoto => 'تغيير صورة الملف الشخصي';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get removePhoto => 'إزالة الصورة';

  @override
  String get updateProfile => 'تحديث الملف الشخصي';

  @override
  String get errorUpdatingProfile => 'خطأ في تحديث الملف الشخصي';

  @override
  String get profileUpdated => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get car => 'سيارة';

  @override
  String get deleteCar => 'حذف السيارة';

  @override
  String get deleteCarConfirmation =>
      'هل أنت متأكد من حذف هذه السيارة؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get delete => 'حذف';

  @override
  String get oilChangeStatus => 'حالة تغيير الزيت';

  @override
  String get lastOilChangeMileage => 'مسافة آخر تغيير للزيت';

  @override
  String inKilometers(Object km) {
    return 'في $km كم';
  }

  @override
  String get carDetails => 'تفاصيل السيارة';

  @override
  String get make => 'الشركة المصنعة';

  @override
  String get model => 'الموديل';

  @override
  String get year => 'سنة';

  @override
  String get maintenanceIntervals => 'فترات الصيانة';

  @override
  String get oilChangeInterval => 'فترة تغيير الزيت';

  @override
  String get maintenanceHistory => 'تاريخ الصيانة';

  @override
  String get oilChanges => 'تغييرات الزيت';

  @override
  String get errorLoadingCarDetails => 'خطأ في تحميل تفاصيل السيارة';

  @override
  String get errorLastMileageGreater =>
      'لا يمكن أن تكون مسافة آخر تغيير للزيت أكبر من المسافة الحالية';

  @override
  String get errorAddingCar => 'خطأ في إضافة السيارة';

  @override
  String get pleaseEnterCarMake => 'يرجى إدخال الشركة المصنعة للسيارة';

  @override
  String get pleaseEnterCarModel => 'يرجى إدخال موديل السيارة';

  @override
  String get pleaseEnterCarYear => 'يرجى إدخال سنة السيارة';

  @override
  String get pleaseEnterValidYear => 'يرجى إدخال سنة صالحة';

  @override
  String get yearRangeError => 'يرجى إدخال سنة صالحة بين 1900 والسنة الحالية';

  @override
  String get pleaseEnterLastOilChangeMileage =>
      'يرجى إدخال مسافة آخر تغيير للزيت';

  @override
  String get pleaseEnterOilEndurance =>
      'يرجى إدخال قدرة تحمل الزيت بالكيلومتر (مثال: 5000)';

  @override
  String get distance => 'المسافة (كم)';

  @override
  String get timeMonths => 'الوقت (أشهر)';

  @override
  String get months => 'أشهر';

  @override
  String get saveCar => 'حفظ السيارة';

  @override
  String get saveChanges => 'حفظ التغييرات';

  @override
  String get editCar => 'تعديل السيارة';

  @override
  String get errorUpdatingCar => 'خطأ في تحديث السيارة';

  @override
  String get upcomingMaintenance => 'الصيانة القادمة';

  @override
  String kmDriven(Object km) {
    return 'تم قطع $km كم';
  }

  @override
  String kmRemaining(int km) {
    return 'متبقي $km كم';
  }

  @override
  String get oilChangeProgress => 'تقدم تغيير الزيت';

  @override
  String get pleaseEnterCurrentMileage => 'يرجى إدخال المسافة الحالية';

  @override
  String get oilType => 'نوع الزيت';

  @override
  String get oilQuantity => 'مسافة الزيت';

  @override
  String get oilEnduranceKm => 'قدرة تحمل الزيت (كم)';

  @override
  String get pleaseEnterOilType => 'يرجى إدخال نوع الزيت';

  @override
  String get pleaseEnterOilQuantity => 'يرجى إدخال مسافة الزيت';

  @override
  String get filterType => 'نوع الفلتر';

  @override
  String get pleaseEnterFilterType => 'يرجى إدخال نوع الفلتر';

  @override
  String get oilChangeDate => 'تاريخ تغيير الزيت';

  @override
  String get save => 'حفظ';

  @override
  String get driven => 'مقطوعة';

  @override
  String get remaining => 'متبقية';

  @override
  String get noOilChangesRecorded => 'لم يتم تسجيل تغييرات الزيت بعد';

  @override
  String get tapPlusToAddOilChange => 'اضغط + لإضافة تغيير زيت';

  @override
  String get mileage => 'المسافة المقطوعة';

  @override
  String get deleteOilChange => 'حذف تغيير الزيت';

  @override
  String get deleteOilChangeConfirmation =>
      'هل أنت متأكد من حذف سجل تغيير الزيت هذا؟';

  @override
  String get oilChangeDeleted => 'تم حذف تغيير الزيت';

  @override
  String get vehicleStatistics => 'إحصائيات المركبات';

  @override
  String get averageMileage => 'متوسط المسافة';

  @override
  String get allGood => 'كل شيء جيد';

  @override
  String get noMaintenanceAlerts => 'لا توجد تنبيهات صيانة';

  @override
  String get noUpcomingReminders => 'لا توجد تذكيرات قادمة';

  @override
  String get allMaintenanceUpToDate => 'جميع الصيانة محدثة';

  @override
  String oilChangeDueInDays(Object days) {
    return 'تغيير الزيت مستحق في $days يوم';
  }

  @override
  String nextOilChangeStatus(Object days, Object mileage) {
    return 'تغيير الزيت القادم: $mileage كم أو $days يوم';
  }

  @override
  String currentMileageStatus(Object mileage) {
    return 'المسافة الحالية: $mileage كم';
  }

  @override
  String get oilChangeHistory => 'سجل تغيير الزيت';

  @override
  String get noOilChangesFound => 'لم يتم العثور على سجلات تغيير الزيت';

  @override
  String get recordOilChangeFirst =>
      'سجل أول تغيير للزيت لبدء تتبع سجل الصيانة';

  @override
  String get oilChangeDetails => 'تفاصيل تغيير الزيت';

  @override
  String get errorLoadingOilChangeDetails => 'خطأ في تحميل تفاصيل تغيير الزيت';

  @override
  String get overview => 'نظرة عامة';

  @override
  String get maintenance => 'صيانة';

  @override
  String get noMaintenanceRecords => 'لم يتم العثور على سجلات صيانة';

  @override
  String get maintenanceDescription => 'الوصف';

  @override
  String get maintenanceDate => 'التاريخ';

  @override
  String get maintenanceCost => 'التكلفة';

  @override
  String get maintenanceProvider => 'مزود الخدمة';

  @override
  String get maintenanceNotes => 'ملاحظات';

  @override
  String get maintenanceAdded => 'تمت إضافة سجل الصيانة بنجاح';

  @override
  String get maintenanceDeleted => 'تم حذف الصيانة بنجاح';

  @override
  String get maintenanceUpdated => 'تم تحديث سجل الصيانة بنجاح';

  @override
  String get errorAddingMaintenance => 'خطأ في إضافة سجل الصيانة';

  @override
  String get errorDeletingMaintenance => 'خطأ في حذف الصيانة';

  @override
  String get errorUpdatingMaintenance => 'خطأ في تحديث سجل الصيانة';

  @override
  String get errorLoadingMaintenance => 'خطأ في تحميل سجلات الصيانة';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmNewPassword => 'تأكيد كلمة المرور الجديدة';

  @override
  String get updatePassword => 'تحديث كلمة المرور';

  @override
  String get passwordUpdatedSuccessfully => 'تم تحديث كلمة المرور بنجاح';

  @override
  String get displayName => 'اسم المستخدم';

  @override
  String get displayNameRequired => 'الرجاء إدخال اسم المستخدم';

  @override
  String get profileUpdatedSuccessfully => 'تم تحديث الملف الشخصي بنجاح';

  @override
  String get chooseImageSource => 'اختر مصدر الصورة';

  @override
  String get profileImageUpdated => 'تم تحديث صورة الملف الشخصي بنجاح';

  @override
  String get errorUpdatingProfileImage =>
      'خطأ في تحديث صورة الملف الشخصي. يرجى التحقق من الأذونات الخاصة بك.';

  @override
  String get permissionRequired => 'الإذن مطلوب';

  @override
  String get cameraPermissionDenied =>
      'إذن الكاميرا مطلوب لالتقاط الصور. يرجى تمكينه في الإعدادات.';

  @override
  String get galleryPermissionDenied =>
      'إذن التخزين مطلوب لاختيار الصور. يرجى تمكينه في الإعدادات.';

  @override
  String get openSettings => 'فتح الإعدادات';

  @override
  String get maintenanceTitle => 'عنوان الصيانة';

  @override
  String get orContinueWith => 'أو المتابعة باستخدام';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get preferences => 'التفضيلات';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get next => 'التالي';

  @override
  String get skip => 'تخطي';

  @override
  String get getStarted => 'ابدأ الآن!';

  @override
  String get onboardingTitle1 => 'لا تفوت تغيير الزيت مرة أخرى';

  @override
  String get onboardingDesc1 =>
      'نسيان تغيير الزيت يمكن أن يضر بمحركك ويكلفك آلاف الجنيهات. اويل بلس يتتبع كل شيء تلقائياً وينبهك عندما يحين الوقت.';

  @override
  String get onboardingTitle2 => 'كل ما تحتاجه في تطبيق واحد';

  @override
  String get onboardingDesc2 =>
      'أدر جميع سياراتك وشاحناتك ودراجاتك النارية. احصل على تذكيرات ذكية قبل انتهاء الموعد. احتفظ بسجلات كاملة مع الصور والإيصالات.';

  @override
  String get onboardingTitle3 => 'ابدأ في حماية استثمارك';

  @override
  String get onboardingDesc3 =>
      'ابدأ بميزات التتبع الأساسية مجاناً. ترقى إلى البريميوم للحصول على مساعدة الذكاء الاصطناعي ومركبات غير محدودة وميزات متقدمة. جرب البريميوم مجاناً لمدة 7 أيام.';

  @override
  String get onboardingTitle4 => 'تتبع جميع أعمال الصيانة';

  @override
  String get onboardingDesc4 => 'احتفظ بسجلات جميع أنشطة الصيانة لمركباتك.';

  @override
  String get onboardingTitle5 => 'الأوامر الصوتية والمساعد الذكي';

  @override
  String get onboardingDesc5 =>
      'استخدم الأوامر الصوتية والدردشة مع المساعد الذكي للمهام السريعة والدعم.';

  @override
  String get onboardingTitle6 => 'النسخ الاحتياطي السحابي والمميزات المميزة';

  @override
  String get onboardingDesc6 =>
      'نسخ احتياطي آمن على السحابة ومميزات إضافية وتجربة بدون إعلانات مع الاشتراك.';

  @override
  String get onboardingProblem => 'نسيان تغيير الزيت يمكن أن يضر بمحركك';

  @override
  String get onboardingSolution => 'اويل بلس يتتبع كل شيء تلقائياً';

  @override
  String get onboardingFeature1 => 'تتبع مركبات متعددة';

  @override
  String get onboardingFeature1Desc =>
      'أدر جميع سياراتك وشاحناتك ودراجاتك النارية';

  @override
  String get onboardingFeature2 => 'تذكيرات ذكية';

  @override
  String get onboardingFeature2Desc => 'احصل على إشعارات قبل انتهاء الموعد';

  @override
  String get onboardingFeature3 => 'سجلات كاملة';

  @override
  String get onboardingFeature3Desc => 'الصور والإيصالات وتاريخ الصيانة';

  @override
  String get onboardingCta => 'ابدأ في حماية استثمارك';

  @override
  String get onboardingFreeStart => 'مجاني للبداية';

  @override
  String get onboardingFreeStartDesc => 'ابدأ بميزات التتبع الأساسية';

  @override
  String get onboardingPremiumBenefits => 'مزايا البريميوم';

  @override
  String get onboardingPremiumBenefitsDesc =>
      'مساعدة الذكاء الاصطناعي ومركبات غير محدودة';

  @override
  String get onboardingTrialOffer => 'تجربة 7 أيام';

  @override
  String get onboardingTrialOfferDesc => 'جرب ميزات البريميوم بدون مخاطر';

  @override
  String get deleteMaintenance => 'حذف الصيانة';

  @override
  String get confirmDeleteMaintenance =>
      'هل أنت متأكد أنك تريد حذف سجل الصيانة هذا؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get offlineMode =>
      'أنت غير متصل بالإنترنت - قد تكون بعض الميزات محدودة';

  @override
  String get noData => 'لا توجد بيانات';

  @override
  String get details => 'التفاصيل';

  @override
  String get selectRecommendedInterval => 'اختر الفاصل الزمني الموصى به';

  @override
  String selected(String value) {
    return 'تم اختيار: $value';
  }

  @override
  String oilChangeRemainingMessage(String km) {
    return 'متبقي $km كم فقط حتى تغيير الزيت';
  }

  @override
  String get privacyPolicy => 'سياسة الخصوصية';

  @override
  String get termsOfService => 'شروط الخدمة';

  @override
  String get termsAndConditions => 'الشروط والأحكام';

  @override
  String get legal => 'قانوني';

  @override
  String get signInToTrackVehicles => 'تحتاج إلى تسجيل الدخول لتتبع مركباتك';

  @override
  String get carSavedAnyway => 'سيتم حفظ السيارة على أي حال';

  @override
  String get errorUploadingImage => 'خطأ في تحميل الصورة';

  @override
  String get addCarImage => 'إضافة صورة السيارة';

  @override
  String get addCarImageHint => 'اختر صورة لسيارتك';

  @override
  String get weatherUnavailable => 'الطقس غير متاح';

  @override
  String get oilChangeRemindersDesc =>
      'تلقي إشعارات عندما تحتاج سيارتك لتغيير الزيت';

  @override
  String get maintenanceRemindersDesc =>
      'تلقي إشعارات حول مواعيد الصيانة المجدولة';

  @override
  String get mileageRemindersDesc =>
      'تلقي إشعارات حول تحديثات المسافة المقطوعة';

  @override
  String get failedToRefreshToken => 'فشل في تحديث رمز FCM';

  @override
  String get refreshToken => 'تحديث رمز FCM';

  @override
  String get permissions => 'الأذونات';

  @override
  String get manageAppPermissions => 'إدارة أذونات التطبيق';

  @override
  String get notificationsPermissionDesc =>
      'استلام تذكيرات في الوقت المناسب حول تغييرات الزيت والصيانة';

  @override
  String get locationPermissionDesc =>
      'عرض أحوال الطقس المحلية للحصول على توصيات صيانة أفضل';

  @override
  String get cameraPermissionDesc => 'التقاط صور لمركباتك للتتبع بشكل أفضل';

  @override
  String get storagePermissionDesc => 'حفظ والوصول إلى صور مركباتك';

  @override
  String get location => 'الموقع';

  @override
  String get storage => 'التخزين';

  @override
  String get timeBasedOilChangeInterval => 'فترة تغيير الزيت على أساس الوقت';

  @override
  String get timeBasedIntervalExplanation =>
      'حتى مع المسافات القصيرة، يجب تغيير الزيت بشكل دوري بسبب تدهوره مع مرور الوقت.';

  @override
  String get timeBasedIntervalDescription =>
      'حدد عدد المرات التي تريد فيها تغيير الزيت بناءً على الوقت، بغض النظر عن المسافة.';

  @override
  String get notificationExplanation =>
      'ستتلقى إشعارات عندما يحين موعد تغيير الزيت بناءً على الوقت أو المسافة، أيهما يأتي أولاً.';

  @override
  String get invalidMonthRange => 'الرجاء إدخال قيمة بين 1-12 شهرًا';

  @override
  String get oilFilter => 'فلتر الزيت';

  @override
  String get didYouChangeFilter => 'هل قمت بتغيير فلتر الزيت؟';

  @override
  String get enterFilterType => 'أدخل نوع/ماركة الفلتر';

  @override
  String get costs => 'التكاليف';

  @override
  String get oilCost => 'تكلفة الزيت';

  @override
  String get filterCost => 'تكلفة الفلتر';

  @override
  String get currencySymbol => '\$';

  @override
  String get add => 'إضافة';

  @override
  String get addNew => 'إضافة جديد';

  @override
  String get termsAcceptance => 'قبول الشروط';

  @override
  String get termsAcceptanceText =>
      'من خلال الوصول إلى أو استخدام اويل بلس، فإنك توافق على الالتزام بهذه الشروط والأحكام. إذا كنت لا توافق على أي جزء من هذه الشروط، فلا يجوز لك استخدام تطبيقنا.';

  @override
  String get appUsage => 'استخدام التطبيق';

  @override
  String get appUsageText =>
      'يوفر اويل بلس أدوات لتتبع صيانة المركبات. بينما نسعى جاهدين للدقة، لا يمكننا ضمان أن جميع المعلومات المقدمة كاملة أو حديثة. يتم تقديم التطبيق \'كما هو\' دون أي ضمانات من أي نوع.';

  @override
  String get userAccounts => 'حسابات المستخدمين';

  @override
  String get userAccountsText =>
      'أنت مسؤول عن الحفاظ على سرية معلومات حسابك وعن جميع الأنشطة التي تحدث تحت حسابك. توافق على إخطارنا فوراً بأي استخدام غير مصرح به لحسابك.';

  @override
  String get userContent => 'محتوى المستخدم';

  @override
  String get userContentText =>
      'أي محتوى تقدمه لتطبيقنا يظل ملكك، ولكنك تمنحنا ترخيصاً غير حصري لاستخدام وتعديل وعرض هذا المحتوى فيما يتعلق بخدماتنا.';

  @override
  String get intellectualProperty => 'الملكية الفكرية';

  @override
  String get intellectualPropertyText =>
      'جميع المحتويات المدرجة في هذا التطبيق، مثل النصوص والرسومات والشعارات والبرامج، هي ملك لاويل بلس أو موردي المحتوى الخاصين به ومحمية بموجب قوانين حقوق النشر الدولية.';

  @override
  String get disclaimerWarranties => 'إخلاء المسؤولية عن الضمانات';

  @override
  String get disclaimerWarrantiesText =>
      'إلى أقصى حد يسمح به القانون المعمول به، نستبعد جميع التمثيلات والضمانات والشروط المتعلقة بتطبيقنا واستخدام هذا التطبيق.';

  @override
  String get limitationLiability => 'حدود المسؤولية';

  @override
  String get limitationLiabilityText =>
      'لن نكون مسؤولين عن أي خسارة أو ضرر من أي نوع ناتج عن استخدام تطبيقنا أو من أي معلومات مقدمة على تطبيقنا.';

  @override
  String get termsModifications => 'تعديلات على الشروط';

  @override
  String get termsModificationsText =>
      'نحتفظ بالحق في مراجعة هذه الشروط في أي وقت دون إشعار. من خلال استخدام هذا التطبيق، يُتوقع منك مراجعة هذه الشروط بانتظام للتأكد من فهمك لجميع الشروط والأحكام التي تحكم استخدام هذا التطبيق.';

  @override
  String get governingLaw => 'القانون الحاكم';

  @override
  String get governingLawText =>
      'ستخضع هذه الشروط وتفسر وفقاً لقوانين الولاية القضائية التي نعمل فيها، وأنت تخضع للاختصاص القضائي غير الحصري للمحاكم الموجودة في تلك الولاية القضائية.';

  @override
  String get contactUs => 'اتصل بنا';

  @override
  String get contactUsText =>
      'إذا كانت لديك أي أسئلة حول شروط الخدمة أو سياسة الخصوصية الخاصة بنا، يرجى الاتصال بنا على <EMAIL>.';

  @override
  String get lastUpdated => 'آخر تحديث';

  @override
  String get privacyPolicyIntro => 'مقدمة';

  @override
  String get privacyPolicyIntroText =>
      'مرحبًا بك في Oil Plus! توضح سياسة الخصوصية هذه كيفية جمع واستخدام والكشف عن وحماية معلوماتك عند استخدام تطبيقنا للهاتف المحمول. يرجى قراءة سياسة الخصوصية هذه بعناية. باستخدام التطبيق، فإنك توافق على ممارسات البيانات الموضحة في هذا البيان.';

  @override
  String get privacyPolicyInfo => 'المعلومات التي نجمعها';

  @override
  String get privacyPolicyInfoText =>
      'قد نجمع عدة أنواع من المعلومات من وحول مستخدمي تطبيقنا، بما في ذلك: (1) المعلومات الشخصية التي تقدمها طواعية عند استخدام تطبيقنا، مثل عنوان البريد الإلكتروني والاسم وتفاصيل الملف الشخصي؛ (2) معلومات حول سيارتك (سياراتك) بما في ذلك الشركة المصنعة والموديل والسنة وتاريخ الصيانة والصور؛ (3) بيانات الاستخدام ومعلومات التحليلات حول كيفية تفاعلك مع تطبيقنا؛ (4) معلومات الجهاز بما في ذلك نظام التشغيل وإصدار الأجهزة وإعدادات الجهاز وأسماء الملفات والبرامج وقوة البطارية والإشارة؛ (5) معلومات الموقع عند تمكين هذه الوظيفة؛ و(6) معلومات من خدمات الطرف الثالث إذا اخترت ربطها بحسابك.';

  @override
  String get privacyPolicyUse => 'How We Use Your Information';

  @override
  String get privacyPolicyUseText =>
      'We use information that we collect about you or that you provide to us, including any personal information: (1) To provide, maintain, and improve our Services; (2) To process and complete transactions, and send related information including confirmations and reminders; (3) To personalize your experience with our application; (4) To communicate with you, including for customer service, updates, security alerts, and support messages; (5) To analyze usage patterns and trends, and to better understand how users interact with our application; (6) To enhance security, monitor and verify identity, and prevent fraud; (7) For compliance purposes, including enforcing our Terms of Service; and (8) For any other purpose with your consent.';

  @override
  String get privacyPolicyStorage => 'Data Storage and Security';

  @override
  String get privacyPolicyStorageText =>
      'The security of your personal information is important to us. We implement and maintain reasonable security measures appropriate to the nature of the information we store in order to protect it from unauthorized access, destruction, use, modification, or disclosure. However, please be aware that no method of transmission over the internet or electronic storage is 100% secure, and we cannot guarantee the absolute security of any information. Your data is stored on Firebase, a Google Cloud platform, with industry-standard encryption and security protocols. We retain personal information only for as long as necessary to fulfill the purposes for which it was collected and as required by applicable laws or regulations.';

  @override
  String get privacyPolicyRights => 'Your Rights';

  @override
  String get privacyPolicyRightsText =>
      'Depending on your location, you may have certain rights regarding your personal information. These may include: (1) The right to access personal information we hold about you; (2) The right to request correction of inaccurate data; (3) The right to request deletion of your data; (4) The right to restrict or object to our processing of your data; (5) The right to data portability; and (6) The right to withdraw consent. Please contact us if you wish to exercise any of these rights. We will respond to your request within the timeframe required by applicable law.';

  @override
  String get privacyPolicyChildren => 'Children\'s Privacy';

  @override
  String get privacyPolicyChildrenText =>
      'Our Services are not directed to children under the age of 13, and we do not knowingly collect personal information from children under 13. If we learn that we have collected personal information from a child under 13, we will take steps to delete such information as quickly as possible. If you believe we might have any information from or about a child under 13, please contact us immediately.';

  @override
  String get privacyPolicyThirdParty => 'Third-Party Services';

  @override
  String get privacyPolicyThirdPartyText =>
      'Our application may contain links to or integrate with third-party websites, services, or applications. We are not responsible for the privacy practices or content of these third parties. The collection, use, and disclosure of your information by these third parties are subject to their respective privacy policies, not this Privacy Policy. We encourage you to read the privacy policies of all third-party websites, services, or applications you visit or use.';

  @override
  String get privacyPolicyChanges => 'Changes to This Privacy Policy';

  @override
  String get privacyPolicyChangesText =>
      'We may update our Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the \'Last Updated\' date. You are advised to review this Privacy Policy periodically for any changes. Changes to this Privacy Policy are effective when they are posted on this page. Your continued use of the application after we post any modifications to the Privacy Policy will constitute your acknowledgment of the modifications and your consent to abide and be bound by the modified Privacy Policy.';

  @override
  String get indemnification => 'Indemnification';

  @override
  String get indemnificationText =>
      'You agree to defend, indemnify, and hold harmless Oil Plus, its parent, subsidiaries, affiliates, and their respective directors, officers, employees, agents, service providers, contractors, licensors, suppliers, successors, and assigns from and against any claims, liabilities, damages, judgments, awards, losses, costs, expenses, or fees (including reasonable attorneys\' fees) arising out of or relating to your violation of these Terms or your use of the application, including, but not limited to, any use of the application\'s content, services, and products other than as expressly authorized in these Terms or your use of any information obtained from the application.';

  @override
  String get disputeResolution => 'حل النزاعات';

  @override
  String get disputeResolutionText =>
      'يجب رفع أي دعوى قانونية أو إجراء يتعلق بوصولك إلى التطبيق أو استخدامه أو هذه الشروط في محكمة ولاية أو فيدرالية في الولاية القضائية التي نعمل فيها. أنت واويل بلس توافقان على الخضوع للاختصاص القضائي الشخصي لهذه المحاكم، والتنازل عن أي وجميع الاعتراضات على ممارسة الاختصاص القضائي على الطرفين من قبل هذه المحاكم والمكان في هذه المحاكم.';

  @override
  String get severability => 'قابلية الفصل';

  @override
  String get severabilityText =>
      'إذا اعتبرت أي محكمة أو هيئة قضائية أخرى ذات اختصاص أن أي حكم من هذه الشروط غير صالح أو غير قانوني أو غير قابل للتنفيذ لأي سبب، فسيتم إلغاء هذا الحكم أو تقييده إلى الحد الأدنى بحيث تستمر الأحكام المتبقية من الشروط بكامل قوتها وتأثيرها.';

  @override
  String get notifyChanges => 'إشعار بالتغييرات';

  @override
  String get notifyChangesText =>
      'نحتفظ بالحق في إجراء تغييرات على سياسة الخصوصية والشروط الخاصة بنا في أي وقت. إذا قررنا تغيير سياسة الخصوصية أو الشروط الخاصة بنا، فسننشر هذه التغييرات على هذه الصفحة حتى تكون على علم دائمًا بالمعلومات التي نجمعها وكيفية استخدامها وتحت أي ظروف نكشف عنها. ستكون التغييرات سارية المفعول فور نشرها على التطبيق. استمرار استخدامك للتطبيق بعد نشر أي تعديلات على سياسة الخصوصية أو الشروط سيشكل إقرارًا منك بالتعديلات وموافقتك على الالتزام بالسياسة أو الشروط المعدلة والالتزام بها.';

  @override
  String get account => 'الحساب';

  @override
  String get clearCache => 'مسح ذاكرة التخزين المؤقت';

  @override
  String get clearLocalImageCache => 'مسح ذاكرة التخزين المؤقت للصور';

  @override
  String get cacheCleared => 'تم مسح الذاكرة المؤقتة بنجاح';

  @override
  String get errorClearingCache => 'خطأ في مسح الذاكرة المؤقتة';

  @override
  String get checkConnection => 'تحقق من الاتصال الخاص بك';

  @override
  String get locationNeeded => 'الوصول إلى الموقع مطلوب';

  @override
  String get tapToSetLocation => 'اضغط لتعيين الموقع';

  @override
  String get locationPermissionGranted => 'تم منح إذن الموقع';

  @override
  String get locationPermissionRationale =>
      'نحتاج إلى إذن الموقع لإظهار بيانات الطقس المحلية. تساعدك معلومات الطقس على تخطيط تغييرات الزيت بناءً على الظروف المحلية.';

  @override
  String get weatherOptions => 'خيارات الطقس';

  @override
  String get useDeviceLocation => 'استخدام موقع الجهاز';

  @override
  String get locationExplanation => 'الحصول على طقس دقيق لموقعك الحالي';

  @override
  String get useIpLocation => 'استخدام الموقع التقريبي';

  @override
  String get ipLocationExplanation =>
      'تقدير الموقع بناءً على عنوان IP الخاص بك';

  @override
  String get refreshWeather => 'تحديث الطقس';

  @override
  String get refreshWeatherExplanation =>
      'تحديث بيانات الطقس باستخدام الإعدادات الحالية';

  @override
  String get authenticationError =>
      'خطأ في المصادقة: يرجى تسجيل الدخول مرة أخرى';

  @override
  String get signInAgain => 'تسجيل الدخول';

  @override
  String get errorFcmToken => 'خطأ: رمز FCM غير متوفر';

  @override
  String errorGeneric(String message) {
    return 'خطأ: $message';
  }

  @override
  String get carUpdatedSuccess => 'تم تحديث السيارة بنجاح';

  @override
  String get imageUploadFailed => 'فشل تحميل الصورة ولكن سيتم حفظ السيارة';

  @override
  String get addAnotherPhoto => 'إضافة صورة أخرى';

  @override
  String get showCarFromDifferentAngles => 'اعرض سيارتك من زوايا مختلفة';

  @override
  String get selectRecommendedIntervalHint => 'اختر الفاصل الزمني الموصى به';

  @override
  String get onboardingReset =>
      'تم إعادة تعيين شاشة الترحيب. أعد تشغيل التطبيق لمشاهدة شاشة الترحيب مرة أخرى.';

  @override
  String get resetOnboardingTooltip =>
      'إعادة تعيين شاشة الترحيب (للاختبار فقط)';

  @override
  String get oilChangeNotificationSentSuccessfully =>
      'تم إرسال إشعار تغيير الزيت بنجاح!';

  @override
  String get failedToSendOilChangeNotification =>
      'فشل في إرسال إشعار تغيير الزيت';

  @override
  String get scheduledRemindersTriggeredSuccessfully =>
      'تم تشغيل التذكيرات المجدولة بنجاح!';

  @override
  String get failedToTriggerScheduledReminders =>
      'فشل في تشغيل التذكيرات المجدولة';

  @override
  String tokenRefreshed(String token) {
    return 'تم تحديث رمز FCM: $token...';
  }

  @override
  String get viewPrivacyPolicy => 'عرض سياسة الخصوصية الخاصة بنا';

  @override
  String get viewTermsAndConditions => 'عرض الشروط والأحكام الخاصة بنا';

  @override
  String mileageUpdatedTo(String make, String model, String mileage) {
    return 'تم تحديث المسافة لـ $make $model إلى $mileage كم';
  }

  @override
  String get totalMaintenanceCost => 'إجمالي تكلفة الصيانة';

  @override
  String get totalRecords => 'إجمالي السجلات';

  @override
  String get maintenanceDetails => 'تفاصيل الصيانة';

  @override
  String get errorLoadingMaintenanceDetails => 'خطأ في تحميل تفاصيل الصيانة';

  @override
  String get editMaintenance => 'تعديل الصيانة';

  @override
  String get existingPhotos => 'الصور الموجودة';

  @override
  String get addNewPhotos => 'إضافة صور جديدة';

  @override
  String get noMaintenanceRecordsYet => 'لا توجد سجلات صيانة حتى الآن';

  @override
  String get receiptPhotos => 'صور الفواتير';

  @override
  String get addReceiptPhotosDesc =>
      'إضافة صور لفواتير أو إيصالات الصيانة الخاصة بك';

  @override
  String get photos => 'الصور';

  @override
  String get viewPhoto => 'عرض الصورة';

  @override
  String get errorLoadingImage => 'فشل في تحميل الصورة';

  @override
  String get failedToTakePhoto => 'فشل في التقاط الصورة';

  @override
  String get failedToSelectPhoto => 'فشل في اختيار الصورة';

  @override
  String get title => 'العنوان';

  @override
  String get enterTitle => 'أدخل عنوان الصيانة';

  @override
  String get titleIsRequired => 'العنوان مطلوب';

  @override
  String get enterOdometer => 'أدخل قراءة عداد المسافات الحالية';

  @override
  String get odometerMustBeNumber => 'عداد المسافات يجب أن يكون رقماً';

  @override
  String get description => 'الوصف';

  @override
  String get enterDescription => 'أدخل وصف الصيانة';

  @override
  String get enterCost => 'أدخل تكلفة الصيانة';

  @override
  String get costMustBeNumber => 'التكلفة يجب أن تكون رقماً';

  @override
  String get shopName => 'اسم المتجر';

  @override
  String get enterShopName => 'أدخل اسم المتجر أو مزود الخدمة';

  @override
  String get dateIsRequired => 'التاريخ مطلوب';

  @override
  String get selectDate => 'اختر التاريخ';

  @override
  String get odometer => 'عداد المسافات';

  @override
  String get emailNotRegistered => 'عنوان البريد الإلكتروني غير مسجل';

  @override
  String get forgotPasswordDescription =>
      'أدخل عنوان بريدك الإلكتروني وسنرسل لك تعليمات لإعادة تعيين كلمة المرور.';

  @override
  String get resetPasswordEmailSent => 'تم الإرسال!';

  @override
  String get resetPasswordCheckEmail =>
      'يرجى التحقق من بريدك الإلكتروني للحصول على تعليمات إعادة تعيين كلمة المرور.';

  @override
  String get backToLogin => 'العودة إلى تسجيل الدخول';

  @override
  String get emailVerification => 'تأكيد البريد الإلكتروني';

  @override
  String get pleaseVerifyEmail =>
      'يرجى تأكيد عنوان بريدك الإلكتروني للوصول إلى جميع الميزات';

  @override
  String get resendVerificationEmail => 'إعادة إرسال رسالة التأكيد الإلكترونية';

  @override
  String get emailVerificationSent => 'تم إرسال رسالة التأكيد الإلكترونية';

  @override
  String get emailVerificationFailed => 'فشل إرسال رسالة التأكيد الإلكترونية';

  @override
  String get verifiedMyEmail => 'لقد تحققت من بريدي الإلكتروني';

  @override
  String get checkingEmailVerification => 'جاري التحقق من حالة التأكيد...';

  @override
  String get emailNotVerifiedYet =>
      'لم يتم التحقق من البريد الإلكتروني بعد. يرجى التحقق من صندوق الوارد الخاص بك.';

  @override
  String get verificationEmailSentTo =>
      'لقد أرسلنا بريدًا إلكترونيًا للتحقق إلى:';

  @override
  String get driverLicense => 'رخصة القيادة';

  @override
  String get driverLicenseExpiryDate => 'تاريخ انتهاء رخصة القيادة';

  @override
  String get tapToSetExpiryDate => 'اضغط لتعيين تاريخ انتهاء الصلاحية';

  @override
  String get selectExpiryDate => 'اختر تاريخ انتهاء رخصة القيادة';

  @override
  String get expiresOn => 'تنتهي في';

  @override
  String get expired => 'منتهية الصلاحية';

  @override
  String get enableExpiryNotifications => 'تفعيل إشعارات انتهاء الصلاحية';

  @override
  String get receiveRemindersBeforeExpiry =>
      'تلقي تذكيرات قبل انتهاء صلاحية رخصتك.';

  @override
  String get today => 'اليوم';

  @override
  String get tomorrow => 'غداً';

  @override
  String get days => 'أيام';

  @override
  String get expiredToday => 'انتهت صلاحيته اليوم';

  @override
  String get expiredYesterday => 'انتهت صلاحيته أمس';

  @override
  String expiredDaysAgo(int days) {
    return 'انتهت صلاحيته منذ $days أيام';
  }

  @override
  String get expiresToday => 'تنتهي صلاحيتها اليوم';

  @override
  String get expiresTomorrow => 'تنتهي صلاحيتها غداً';

  @override
  String expiresInDays(int days) {
    return 'تنتهي صلاحيتها خلال $days أيام';
  }

  @override
  String get years => 'سنوات';

  @override
  String get month => 'شهر';

  @override
  String expiresIn(String details) {
    return 'تنتهي صلاحيته خلال $details';
  }

  @override
  String get notificationScheduleInfo =>
      'في حال التفعيل، سيتم إرسال تذكيرات قبل 30، 14، 7، ويوم واحد من تاريخ انتهاء الصلاحية، وفي يوم انتهاء الصلاحية.';

  @override
  String get week => 'أسبوع';

  @override
  String get weeks => 'أسابيع';

  @override
  String get day => 'يوم';

  @override
  String get licenseExpiryDate => 'تاريخ انتهاء الترخيص';

  @override
  String get notSet => 'لم يتم التعيين';

  @override
  String get licenseExpiredTitle => 'الرخصة منتهية الصلاحية';

  @override
  String licenseExpiredBody(String year, String make, String model) {
    return 'رخصة السيارة $year $make $model قد انتهت صلاحيتها.';
  }

  @override
  String get licenseExpiryReminderTitle => 'تذكير بانتهاء صلاحية الرخصة';

  @override
  String get licenseExpiringSoonTitle => 'الرخصة تنتهي قريباً';

  @override
  String licenseExpiringSoonBody(
      String year, String make, String model, String days) {
    return 'رخصة السيارة $year $make $model تنتهي صلاحيتها خلال $days أيام.';
  }

  @override
  String get licenseExpiresTodayTitle => 'الرخصة تنتهي اليوم!';

  @override
  String licenseExpiresTodayBody(String year, String make, String model) {
    return 'رخصة السيارة $year $make $model تنتهي صلاحيتها اليوم.';
  }

  @override
  String get isOverdueForOilChange => 'متأخر عن تغيير الزيت';

  @override
  String get oilChangeDueSoon => 'تغيير الزيت قريبًا';

  @override
  String oilChangeDueSoonBody(
      String year, String make, String model, String days) {
    return 'السيارة $year $make $model ستحتاج إلى تغيير الزيت خلال $days أيام';
  }

  @override
  String get oilChangeDueTodayTitle => 'تغيير الزيت اليوم';

  @override
  String oilChangeDueTodayBody(String year, String make, String model) {
    return 'السيارة $year $make $model مستحقة لتغيير الزيت اليوم';
  }

  @override
  String get takingPhoto => 'Taking photo...';

  @override
  String get noPhotoTaken => 'No photo taken';

  @override
  String get selectingPhoto => 'Selecting photo...';

  @override
  String get noPhotoSelected => 'No photo selected';

  @override
  String get networkError => 'Network Connection Error';

  @override
  String get checkInternetConnection => 'Please check your internet connection';

  @override
  String get networkConnectionError =>
      'Network error. Please check your internet connection and try again.';

  @override
  String get networkConnectionLost =>
      'Network connection lost. Please check your internet connection and try again.';

  @override
  String get unlockPremiumFeatures => 'اكتشف الميزات المميزة';

  @override
  String get chooseYourPlan => 'اختر الخطة التي تناسبك';

  @override
  String get saveWithYearly => 'وفر حتى 30% مع الاشتراك السنوي';

  @override
  String get current => 'الحالي';

  @override
  String get free => 'مجاني';

  @override
  String get currentPlan => 'الخطة الحالية';

  @override
  String get selectPlan => 'اختر الخطة';

  @override
  String get subscriptionActivated => 'تم تفعيل الاشتراك بنجاح';

  @override
  String get subscriptionFailed => 'فشل في تفعيل الاشتراك';

  @override
  String get purchasesRestored => 'تم استرداد المشتريات بنجاح';

  @override
  String get noPurchasesFound => 'لم يتم العثور على مشتريات';

  @override
  String get subscriptionTerms =>
      'يمكن إدارة الاشتراكات وإلغاؤها من خلال إعدادات حساب Google Play. لا تحدث تجديدات تلقائية بدون موافقتك الصريحة.';

  @override
  String get voiceCommands => 'الأوامر الصوتية';

  @override
  String get voiceCommandsDescription =>
      'استخدم الأوامر الصوتية لإضافة السجلات بسرعة';

  @override
  String get premiumFeature => 'ميزة مميزة';

  @override
  String get premiumRequired => 'هذه الميزة تتطلب اشتراك مميز';

  @override
  String get upgradeNow => 'قم بالترقية الآن';

  @override
  String get notNow => 'لاحقًا';

  @override
  String get tryFree => 'جرب مجاناً لمدة 7 أيام';

  @override
  String get familySharing => 'المشاركة العائلية';

  @override
  String get familySharingDescription =>
      'شارك مع ما يصل إلى 5 أفراد من العائلة';

  @override
  String get unlimitedVehicles => 'مركبات غير محدودة';

  @override
  String get adFreeExperience => 'تجربة خالية من الإعلانات';

  @override
  String get enhancedAnalytics => 'تحليلات محسنة';

  @override
  String get prioritySupport => 'دعم أولوي';

  @override
  String get trialStarted => 'بدأت التجربة المجانية بنجاح';

  @override
  String get trialFailed => 'فشل في بدء التجربة المجانية';

  @override
  String trialExpires(String date) {
    return 'تنتهي التجربة المجانية في $date';
  }

  @override
  String daysRemaining(int days) {
    return '$days أيام متبقية';
  }

  @override
  String get manageSubscription => 'إدارة الاشتراك';

  @override
  String subscriptionExpires(String date) {
    return 'ينتهي الاشتراك في $date';
  }

  @override
  String get voiceCommandsHelp => 'مساعدة الأوامر الصوتية';

  @override
  String get voiceCommandsHelpDescription =>
      'جرب هذه الأوامر مع ميزة الإدخال الصوتي:';

  @override
  String get addOilChange => 'إضافة تغيير زيت';

  @override
  String get productNotAvailable => 'المنتج غير متوفر';

  @override
  String get checkConnectionAndTryAgain =>
      'يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى';

  @override
  String get premium => 'مميز';

  @override
  String get upgradeToPremiun => 'الترقية إلى المميز';

  @override
  String get subscriptionDetails => 'تفاصيل الاشتراك';

  @override
  String get premiumSubscription => 'اشتراك مميز';

  @override
  String get freeVersion => 'النسخة المجانية';

  @override
  String get youHaveActivePremium => 'لديك اشتراك مميز نشط';

  @override
  String get upgradeToRemoveAds =>
      'قم بالترقية إلى النسخة المميزة لإزالة الإعلانات وفتح جميع الميزات';

  @override
  String get upgradeToPremium => 'الترقية إلى بريميوم';

  @override
  String get viewCarDetails => 'عرض تفاصيل السيارة';

  @override
  String get unknownCommand => 'أمر غير معروف';

  @override
  String get youSaid => 'قلت';

  @override
  String get extractedInformation => 'المعلومات المستخرجة';

  @override
  String get confirm => 'تأكيد';

  @override
  String get couldNotUnderstandCommand =>
      'لم نتمكن من فهم الأمر. يرجى المحاولة مرة أخرى.';

  @override
  String get noSpeechDetected => 'لم يتم اكتشاف كلام. يرجى المحاولة مرة أخرى.';

  @override
  String get microphonePermissionDenied =>
      'إذن الميكروفون مطلوب لاستخدام الأوامر الصوتية.';

  @override
  String get cloudBackup => 'نسخ احتياطي سحابي';

  @override
  String get cloudBackupPremiumFeature =>
      'النسخ الاحتياطي السحابي هو ميزة مميزة';

  @override
  String get cloudBackupDescription =>
      'قم بنسخ واستعادة بيانات مركبتك وسجل تغيير الزيت والإعدادات بشكل آمن إلى السحابة.';

  @override
  String get backupSuccessful => 'تم إنشاء النسخة الاحتياطية بنجاح';

  @override
  String get backupFailed => 'فشل إنشاء النسخة الاحتياطية';

  @override
  String get confirmRestore => 'تأكيد الاستعادة';

  @override
  String get firstCarWelcomeMessage =>
      'دعنا نضيف مركبتك الأولى لبدء تتبع تغييرات الزيت والصيانة!';

  @override
  String get firstCarStepIndicator => 'الخطوة 1 من 1: أضف سيارتك الأولى';

  @override
  String get quickTips => 'نصائح سريعة:';

  @override
  String get tipAddPhotos => 'أضف صوراً لتحديد سيارتك بسهولة';

  @override
  String get tipEnterMileage => 'أدخل المسافة المقطوعة الحالية للتتبع الدقيق';

  @override
  String get tipSetIntervals => 'حدد فترات تغيير الزيت بناءً على عادات القيادة';

  @override
  String get congratulations => '🎉 تهانينا!';

  @override
  String get firstCarSuccessMessage =>
      'لقد أضفت سيارتك الأولى بنجاح! أنت الآن جاهز لتتبع تغييرات الزيت والصيانة.';

  @override
  String get whatsNext => 'ما التالي:';

  @override
  String get nextStepRecordOil => 'سجل تغيير الزيت التالي';

  @override
  String get nextStepReminders => 'احصل على تذكيرات للصيانة';

  @override
  String get nextStepTrackHealth => 'تتبع صحة مركبتك';

  @override
  String get tapToAddPhotos => 'اضغط أعلاه لإضافة صور سيارتك';

  @override
  String get dashboardWelcomeSubtitle =>
      'ابدأ في تتبع تغييرات الزيت وصيانة مركبتك بسهولة';

  @override
  String get whatYoullGet => 'ما ستحصل عليه:';

  @override
  String get smartReminders => 'تذكيرات ذكية';

  @override
  String get smartRemindersDesc =>
      'لا تفوت أبداً تغيير الزيت مع الإشعارات الذكية';

  @override
  String get trackHistory => 'تتبع التاريخ';

  @override
  String get trackHistoryDesc => 'راقب تاريخ صيانة مركبتك والتكاليف';

  @override
  String get mileageTracking => 'تتبع المسافة المقطوعة';

  @override
  String get mileageTrackingDesc => 'تتبع المسافة المقطوعة لمركبتك تلقائياً';

  @override
  String get restoreWarning =>
      'سيؤدي هذا إلى استبدال جميع بياناتك الحالية ببيانات النسخة الاحتياطية. لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get restore => 'استعادة';

  @override
  String get restoreSuccessful => 'تمت استعادة البيانات بنجاح';

  @override
  String get restoreFailed => 'فشل استعادة البيانات';

  @override
  String get automaticBackups => 'النسخ الاحتياطي التلقائي';

  @override
  String get automaticBackupsDescription =>
      'قم بنسخ بياناتك احتياطيًا تلقائيًا يوميًا لضمان عدم فقدان سجلاتك.';

  @override
  String get enableAutomaticBackups => 'تمكين النسخ الاحتياطي التلقائي';

  @override
  String get automaticBackupsEnabled => 'تم تمكين النسخ الاحتياطي التلقائي';

  @override
  String get automaticBackupsDisabled => 'تم تعطيل النسخ الاحتياطي التلقائي';

  @override
  String get manualBackup => 'نسخ احتياطي يدوي';

  @override
  String get createBackup => 'إنشاء نسخة احتياطية';

  @override
  String get backupHistory => 'سجل النسخ الاحتياطي';

  @override
  String get noBackupsFound => 'لم يتم العثور على نسخ احتياطية';

  @override
  String get manageCloudBackups =>
      'إدارة النسخ الاحتياطية السحابية واستعادة البيانات';

  @override
  String get chatAssistantTitle => 'مساعد الأعطال 🔧';

  @override
  String get clearChatTooltip => 'مسح المحادثة';

  @override
  String get chatEmptyTitle => 'اسأل عن أي مشكلة في سيارتك';

  @override
  String get chatEmptySubtitle => 'سأساعدك في التشخيص والحلول';

  @override
  String get assistantThinking => 'يفكر المساعد...';

  @override
  String get chatFaqTitle => 'أسئلة شائعة:';

  @override
  String get chatPromptEngineNoStart => 'السيارة لا تدور';

  @override
  String get chatPromptAbsLight => 'مصباح الـ ABS مشتعل';

  @override
  String get chatPromptOilLight => 'لمبة الزيت منورة';

  @override
  String get chatPromptEngineNoise => 'صوت غريب من المحرك';

  @override
  String get chatPromptVibration => 'السيارة تهتز أثناء القيادة';

  @override
  String get chatPromptBrakes => 'مشكلة في الفرامل';

  @override
  String get chatPromptHighTemp => 'حرارة المحرك عالية';

  @override
  String get chatPromptBattery => 'مشكلة في البطارية';

  @override
  String get premiumRemoveAdsDescription =>
      'احصل على جميع الميزات المميزة وأزل الإعلانات';

  @override
  String get monthlyPremium => 'اشتراك شهري مميز';

  @override
  String get annualPremium => 'اشتراك سنوي مميز';

  @override
  String basePrice(String price) {
    return 'السعر الأساسي: $price';
  }

  @override
  String monthlyEquivalent(String price) {
    return 'ما يعادل شهريًا: $price';
  }

  @override
  String get premiumFeaturesTitle => 'ميزات مميزة';

  @override
  String get featureVoiceCommands => 'أوامر صوتية للإدخال السريع';

  @override
  String get featureUnlimitedVehicles => 'مركبات غير محدودة';

  @override
  String get featureCloudBackup => 'النسخ الاحتياطي والمزامنة السحابية';

  @override
  String get featureAdvancedAnalytics => 'تحليلات متقدمة';

  @override
  String get freeTrialTitle => 'تجربة مجانية';

  @override
  String get tryPremiumFeaturesFree => 'جرّب الميزات المميزة مجانًا';

  @override
  String get freeTrialDescription =>
      'استمتع بتجربة مجانية لمدة 7 أيام بدون التزام. يمكنك الإلغاء في أي وقت.';

  @override
  String get selectPlanToTry => 'اختر خطة للتجربة:';

  @override
  String get freeTrialAgreement =>
      'أوافق على أنه بعد فترة التجربة، سيتم تحويل اشتراكي تلقائيًا إلى اشتراك مدفوع ما لم ألغِ الاشتراك قبل انتهاء التجربة.';

  @override
  String get startFreeTrial => 'ابدأ التجربة المجانية';

  @override
  String get freeTrialTerms =>
      'من خلال بدء تجربة مجانية، فإنك توافق على شروط الخدمة وسياسة الخصوصية. يمكنك الإلغاء في أي وقت خلال فترة التجربة دون تحمّل رسوم.';

  @override
  String get loadingPrice => 'جارٍ تحميل السعر...';

  @override
  String get trialStartedSuccess => 'تم بدء التجربة بنجاح!';

  @override
  String get trialStartFailed => 'فشل بدء التجربة. حاول مرة أخرى.';

  @override
  String freeForSevenDaysThen(String price, String perMonth) {
    return 'مجاني لمدة 7 أيام. بعد التجربة: $price / $perMonth (يتطلب اشتراك يدوي)';
  }

  @override
  String get subscriptionPolicy => 'سياسة الاشتراك';

  @override
  String get viewSubscriptionPolicy => 'عرض تفاصيل الاشتراك';

  @override
  String get refundPolicy => 'سياسة الاسترداد';

  @override
  String get viewRefundPolicy => 'عرض سياسة الاسترداد';

  @override
  String get aiAssistant => 'مساعد الأعطال';

  @override
  String get aiChat => 'الدردشة الذكية';

  @override
  String get aiFeatures => 'الميزات الذكية';

  @override
  String get aiFeaturesPremiumDescription =>
      'الأوامر الصوتية ومساعد الدردشة الذكية هي ميزات مميزة تساعدك في إدارة صيانة مركبتك بكفاءة أكبر.';

  @override
  String get voiceCommandsWelcome => 'الأوامر الصوتية';

  @override
  String get voiceCommandsSubtitle =>
      'استخدم صوتك لتسجيل الصيانة وتغيير الزيت بسرعة';

  @override
  String get recordOilChangeVoice => 'سجل تغيير الزيت باستخدام الأوامر الصوتية';

  @override
  String get recordMaintenanceVoice =>
      'سجل مهام الصيانة باستخدام الأوامر الصوتية';

  @override
  String featureLocked(String featureName) {
    return '$featureName مقفلة';
  }

  @override
  String get featureRequiresPremium =>
      'هذه الميزة تتطلب اشتراك مميز لإلغاء القفل.';

  @override
  String get premiumFeatures => 'الميزات المميزة';

  @override
  String get featureAiChat => 'مساعد الدردشة الذكية';

  @override
  String get featureAdFree => 'تجربة بدون إعلانات';

  @override
  String get verificationEmailSent => 'تم إرسال رسالة التحقق';

  @override
  String get errorSendingVerificationEmail =>
      'حدث خطأ أثناء إرسال رسالة التحقق';

  @override
  String get verifyEmail => 'تأكيد البريد الإلكتروني';

  @override
  String get viewCar => 'عرض السيارة';

  @override
  String get noParametersDetected => 'لم يتم اكتشاف أي معلمات';

  @override
  String get notSpecified => 'غير محدد';

  @override
  String get restartApp => 'إعادة تشغيل التطبيق';

  @override
  String get adminNotifications => 'إشعارات المشرف';

  @override
  String get resetAllTokens => 'إعادة تعيين جميع الرموز';

  @override
  String get sendToAllUsers => 'إرسال لجميع المستخدمين';

  @override
  String get notificationType => 'نوع الإشعار';

  @override
  String get promotional => 'ترويجي';

  @override
  String get oilChange => 'تغيير الزيت';

  @override
  String get notificationTitle => 'عنوان الإشعار';

  @override
  String get notificationBody => 'محتوى الإشعار';

  @override
  String get enterNotificationTitle => 'أدخل عنوان الإشعار';

  @override
  String get enterNotificationMessage => 'أدخل رسالة الإشعار';

  @override
  String get titleBodyRequired => 'العنوان والمحتوى مطلوبان';

  @override
  String get accessDeniedAdmin => 'تم الرفض. صلاحيات المشرف مطلوبة.';

  @override
  String get processing => 'جارٍ المعالجة...';

  @override
  String get verificationResults => 'نتائج التحقق';

  @override
  String get googleAccountDialogTitle => 'إدارة حساب Google الخاص بك';

  @override
  String get googleAccountDialogDescription =>
      'لإدارة حساب Google الخاص بك:\n\n1. انتقل إلى الإعدادات > Google\n2. اختر حسابك\n3. قم بإدارة تفضيلاتك';

  @override
  String get goBack => 'رجوع';

  @override
  String get backToMaintenanceList => 'العودة إلى قائمة الصيانة';

  @override
  String get listening => 'يتم الاستماع...';

  @override
  String get noVoiceDetected => 'لم يتم اكتشاف صوت.';

  @override
  String failedToStartRecording(Object error) {
    return 'فشل بدء التسجيل: $error';
  }

  @override
  String get errorMissingCarId => 'خطأ: معرف السيارة مفقود.';

  @override
  String get developerSettings => 'إعدادات المطور';

  @override
  String get premiumFeatureDescription =>
      'المستخدمون المجانيون محدودون بثلاث مركبات. قم بالترقية إلى بريميوم لعدد غير محدود من المركبات.';

  @override
  String get voiceInputFeature1 => 'إدخال صوتي لإدخال البيانات بسرعة';

  @override
  String get voiceInputFeature2 => 'تجربة خالية من الإعلانات';

  @override
  String get voiceInputFeature3 => 'مركبات غير محدودة';

  @override
  String get voiceInputFeature4 => 'تحليلات ورؤى محسنة';

  @override
  String get voiceInputFeature5 => 'نسخ احتياطي ومزامنة سحابية';

  @override
  String get resettingAndRefreshingTokens =>
      'جارٍ إعادة تعيين وتحديث جميع الرموز...';
}
